import { z } from 'zod';

import { PlatformKey } from '@malou-io/package-utils';

import { objectIdValidator } from '../../utils';

export const getRestaurantPostInsightsParamsValidator = z.object({
    restaurantId: objectIdValidator,
});
export type GetRestaurantPostInsightsParamsDto = z.infer<typeof getRestaurantPostInsightsParamsValidator>;

export const getRestaurantPostInsightsBodyValidator = z.object({
    startDate: z.string().datetime(),
    endDate: z.string().datetime(),
    platformKeys: z.array(z.nativeEnum(PlatformKey)),
});

export type GetRestaurantPostInsightsBodyDto = z.infer<typeof getRestaurantPostInsightsBodyValidator>;

// ---------------------------------------------------------------------------------------------
