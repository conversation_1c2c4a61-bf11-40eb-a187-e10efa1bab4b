import { MalouErrorCode, MediaType, PlatformKey, PostInsightEntityType, PostType } from '@malou-io/package-utils';

export interface PostInsightDto {
    id: string;
    platformKey: PlatformKey;
    socialId: string;
    entityType: PostInsightEntityType;
    platformSocialId: string;
    postSocialCreatedAt: string;
    data: {
        impressions: number;
        likes: number;
        comments: number;
        shares: number;
        reach: number | null;
        plays: number | null;
        saved: number | null;
    };
    post: {
        postType: PostType;
        socialLink?: string;
        attachments: {
            socialId: string | null;
            thumbnailUrl: string | null;
            type: MediaType | undefined;
            urls: {
                original: string;
            };
        }[];
    };
}

export interface PlatformPostInsightResponseDto {
    platformKey: PlatformKey;
    postInsights: PostInsightDto[];
    error?: {
        code: MalouErrorCode;
        message: string;
    };
}
