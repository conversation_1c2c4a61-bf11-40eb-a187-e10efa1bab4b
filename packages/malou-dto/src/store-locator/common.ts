import { z } from 'zod';

import { urlValidator } from '../utils/validators';

export const storeLocatorHeadBlockValidator = z.object({
    title: z.string(),
    description: z.string(),
    twitterDescription: z.string(),
    keywords: z.string(),
    url: urlValidator(),
    snippetImageUrl: urlValidator(),
    facebookImageUrl: urlValidator(),
    twitterImageUrl: urlValidator(),
    locale: z.string(),
    organizationName: z.string(),
    xUserName: z.string().optional(),
    googleAnalytics: z.object({
        id: z.string(),
        organizationId: z.string(),
        storeId: z.string().optional(),
        pageCategory: z.string(),
    }),
    googleAnalyticsClientId: z.string().optional(),
    microdata: z.string(),
    isLive: z.boolean(),
    alternatePageUrls: z.array(
        z.object({
            lang: z.string(),
            url: z.string(),
        })
    ),
});

export type GetStoreLocatorHeadBlockDto = z.infer<typeof storeLocatorHeadBlockValidator>;
