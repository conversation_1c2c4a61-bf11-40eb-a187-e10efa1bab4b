import { z } from 'zod';

import { StoreLocatorLanguage } from '@malou-io/package-utils';

import { storeLocatorHeadBlockValidator } from '../common';
import { storeLocatorMapComponentsValidator } from './map-components';
import { storeLocatorMapStoreValidator } from './store-list';

export const getStoreLocatorMapValidator = z.object({
    organizationName: z.string(),
    lang: z.nativeEnum(StoreLocatorLanguage),
    relativePath: z.string(),
    shouldDisplayWhiteMark: z.boolean(),
    headBlock: storeLocatorHeadBlockValidator,
    stores: z.array(storeLocatorMapStoreValidator),
    mapComponents: storeLocatorMapComponentsValidator,
    styles: z.record(
        z.string(), // z.nativeEnum(StoreLocatorCentralizationPageElementIds),
        z.array(z.string())
    ),
});

export type GetStoreLocatorMapDto = z.infer<typeof getStoreLocatorMapValidator>;

// -------------------------------------------------------------------------------

export { storeLocatorMapComponentsValidator };
