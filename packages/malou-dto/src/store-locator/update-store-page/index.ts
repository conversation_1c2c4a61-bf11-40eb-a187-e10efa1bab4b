import { z } from 'zod';

import { GenerateStoreLocatorContentType, StoreLocatorLanguage } from '@malou-io/package-utils';

import { objectIdValidator } from '../../utils';
import { storeLocatorStorePageGalleryBlockValidator } from '../get-store-page';
import { updateStoreLocatorParamsValidator } from '../shared';
import { updateStoreLocatorStorePageCallToActionsBlockValidator } from './call-to-actions-block';
import { updateStoreLocatorStorePageDescriptionsBlockValidator } from './descriptions-block';
import { updateStoreLocatorStorePageFaqBlockValidator } from './faq-block';
import { updateStoreLocatorStorePageInformationBlockValidator } from './information-block';
import { updateStoreLocatorStorePageReviewsBlockValidator } from './reviews-block';
import { updateStoreLocatorStorePageSocialNetworksBlockValidator } from './social-networks';

// -------------------------------------------------------------------------------

export const updateStoreLocatorStorePageParamsValidator = updateStoreLocatorParamsValidator;

export type UpdateStoreLocatorStorePageParamsDto = z.infer<typeof updateStoreLocatorStorePageParamsValidator>;

// -------------------------------------------------------------------------------
export const storeLocatorStorePageCurrentUpdates = z.object({
    restaurantId: objectIdValidator,
    lang: z.nativeEnum(StoreLocatorLanguage),
    information: updateStoreLocatorStorePageInformationBlockValidator.optional(),
    gallery: storeLocatorStorePageGalleryBlockValidator.optional(),
    reviews: updateStoreLocatorStorePageReviewsBlockValidator.optional(),
    callToActions: updateStoreLocatorStorePageCallToActionsBlockValidator.optional(),
    descriptions: updateStoreLocatorStorePageDescriptionsBlockValidator.optional(),
    socialNetworks: updateStoreLocatorStorePageSocialNetworksBlockValidator.optional(),
    faq: updateStoreLocatorStorePageFaqBlockValidator.optional(),
});

export type StoreLocatorStorePageUpdatesDto = z.infer<typeof storeLocatorStorePageCurrentUpdates>;

// -------------------------------------------------------------------------------
export const generateStoreLocatorStorePageContentBodyValidator = z.object({
    type: z.nativeEnum(GenerateStoreLocatorContentType),
    updates: storeLocatorStorePageCurrentUpdates,
    params: z
        .object({
            currentContent: z.string().optional(),
            context: z.array(z.record(z.nativeEnum(GenerateStoreLocatorContentType), z.string())).optional(),
        })
        .optional(),
});

export type GenerateStoreLocatorStorePageContentBodyDto = z.infer<typeof generateStoreLocatorStorePageContentBodyValidator>;

// -------------------------------------------------------------------------------
export const updateStoreLocatorStorePagesBodyValidator = z.array(storeLocatorStorePageCurrentUpdates);

export type UpdateStoreLocatorStorePagesBodyDto = z.infer<typeof updateStoreLocatorStorePagesBodyValidator>;
