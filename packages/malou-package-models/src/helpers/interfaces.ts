import { Types } from 'mongoose';

export const { ObjectId } = Types;

export type ID = Types.ObjectId | string;

export type DbId = Types.ObjectId;

export const toDbId = (stringId: ID): DbId => new ObjectId(stringId);

export const safeToDbId = (stringId: ID): DbId | null => {
    let dbId = null;
    try {
        dbId = toDbId(stringId);
    } catch (error) {
        // ignore
    }
    return dbId;
};

export const newDbId = (): DbId => new ObjectId();

export const toDbIds = (stringIds: ID[]): DbId[] => stringIds.map((id) => new ObjectId(id));
