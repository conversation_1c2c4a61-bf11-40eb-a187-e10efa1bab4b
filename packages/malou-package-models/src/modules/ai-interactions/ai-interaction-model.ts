import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { aiInteractionJSONSchema } from './ai-interaction-schema';

const aiInteractionSchema = createMongooseSchemaFromJSONSchema(aiInteractionJSONSchema);

aiInteractionSchema.index({ relatedEntityCollection: 1, relatedEntityId: 1, createdAt: -1 });
aiInteractionSchema.index({ userId: 1 });
aiInteractionSchema.index({ relatedEntityId: 1 });
aiInteractionSchema.index({ restaurantId: 1, relatedEntityCollection: 1, createdAt: 1 });
aiInteractionSchema.index({ relatedEntityBindingId: 1 });

export type IAiInteraction = FromSchema<
    typeof aiInteractionJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export type IAiChatCompletionMessage = NonNullable<IAiInteraction['messages']>[number];

export const AiInteractionModel = mongoose.model<IAiInteraction>(aiInteractionJSONSchema.title, aiInteractionSchema);
