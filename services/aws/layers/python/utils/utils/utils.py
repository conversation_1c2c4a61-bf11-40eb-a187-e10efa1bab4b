import re
from unidecode import unidecode


class SimpleTextCleaner:
    @staticmethod
    def to_lower(text):
        """Convert text to lowercase."""
        return text.lower()

    def to_upper(text):
        """Convert text to lowercase."""
        return text.upper()

    @staticmethod
    def insert_newlines(text):
        """Replace '\\n' sequences with actual newlines."""
        return text.replace("\\n", "\n")

    @staticmethod
    def delete_newlines(text):
        return text.replace("\n", " ")

    @staticmethod
    def clean_text_edges(text):
        """Remove unwanted characters from the beginning and end of the text."""
        return re.sub(r'^[\n\",\'\(\)\[\]\{\}«» *\§]+|["\[\]\(\) *\§»«\']+$', "", text)

    @staticmethod
    def remove_emojis(text):
        """Remove all emojis from the text."""
        emoji_pattern = re.compile(
            "["
            "\U0001f600-\U0001f64f"
            "\U0001f300-\U0001f5ff"
            "\U0001f680-\U0001f6ff"
            "\U0001f1e0-\U0001f1ff"
            "\U00002500-\U00002bef"
            "\U00002702-\U000027b0"
            "\U000024c2-\U0001f251"
            "\U0001f926-\U0001f937"
            "\U00010000-\U0010ffff"
            "\u2640-\u2642"
            "\u2600-\u2b55"
            "\u200d"
            "\u23cf"
            "\u23e9"
            "\u231a"
            "\ufe0f"
            "\u3030"
            "]+",
            flags=re.UNICODE,
        )
        return emoji_pattern.sub("", text)

    @staticmethod
    def remove_hashtags(text):
        """Remove hashtags like #foodie or #Travel."""
        text = re.sub(r"#\w+", "", text)
        return text.replace("#", " ")

    @staticmethod
    def fix_spacing_and_punctuation(text):
        """Fix common punctuation and spacing issues."""
        text = text.replace(" ,", ",")
        text = text.replace(",", ", ")

        # Remove unwanted markers
        text = text.replace("***", "").replace("**", "")

        # Remove extra spaces
        text = re.sub(r" +(?=[^\n])", " ", text)
        return text.strip()

    @staticmethod
    def clean(text):
        """Run full cleaning pipeline."""
        text = SimpleTextCleaner.clean_text_edges(text)
        text = SimpleTextCleaner.remove_emojis(text)
        text = SimpleTextCleaner.insert_newlines(text)
        text = SimpleTextCleaner.remove_hashtags(text)
        text = SimpleTextCleaner.fix_spacing_and_punctuation(text)
        return text
