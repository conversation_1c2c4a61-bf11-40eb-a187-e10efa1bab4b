from typing import Type
from pydantic import BaseModel
from .ai_provider_abstract import AbstractAiProvider, AiResponse, AiConfig


class AiProvider(AbstractAiProvider):
    def __init__(self, ai_provider_impl: AbstractAiProvider) -> None:
        self.__ai_provider_impl = ai_provider_impl

    def call(
        self,
        prompt: str,
        response_model: Type[BaseModel],
        configuration: AiConfig,
    ) -> AiResponse:
        return self.__ai_provider_impl.call(prompt, configuration, response_model)

    def callWithContext(
        self,
        prompt: str,
        context: str,
        configuration: AiConfig,
        response_model: Type[BaseModel],
    ) -> AiResponse:
        return self.__ai_provider_impl.callWithContext(
            prompt, context, configuration, response_model
        )
