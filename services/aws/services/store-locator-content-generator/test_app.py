import pytest
from unittest.mock import MagicMock
from app import handler
from core.models.event_model import RequestTypeEnum, RequestRelatedEntityCollectionEnum
from aws_lambda_powertools.utilities.typing import LambdaContext
import json
import os

BASE_PAYLOAD = {
    "relatedEntityCollection": RequestRelatedEntityCollectionEnum.STORE_LOCATOR,
    "restaurantData": {
        "previousGeneration": "",
        "restaurantName": "McDonald's SouthBay Anytown",
        "language": "fr",
        "organizationName": "McDonald's",
        "address": {"locality": "123 Main St, Anytown", "postalCode": "12345"},
        "keywords": [
            "fast food restaurant",
            "burgers Anytown",
            "family restaurant",
            "drive-thru Anytown",
            "restaurant drive-thru",
            "fast food Anytown",
            "Burgers and fries",
        ],
        "brandTone": ["friendly", "casual"],
        "targetAudience": ["families", "young adults"],
        "specificsDirectives": ["highlight drive-thru service"],
        "restaurantOffers": ["20% off on first order"],
        "restaurantContext": ["new store opening soon"],
        "context": [
            {
                "h1_title_generation": "La nouvelle expérience McDonald's à Anytown",
            }
        ],
        "regularHours": [
            {
                "openDay": "SUNDAY",
                "openTime": "11:00",
                "closeDay": "SUNDAY",
                "closeTime": "23:00",
                "isClosed": False,
                "isPrimaryPeriod": True,
            },
            {
                "openDay": "SUNDAY",
                "openTime": "10:00",
                "closeDay": "MONDAY",
                "closeTime": "13:00",
                "isClosed": False,
                "isPrimaryPeriod": True,
            },
            {
                "openDay": "TUESDAY",
                "openTime": "11:00",
                "closeDay": "TUESDAY",
                "closeTime": "23:00",
                "isClosed": False,
                "isPrimaryPeriod": True,
            },
            {
                "openDay": "WEDNESDAY",
                "openTime": "11:00",
                "closeDay": "WEDNESDAY",
                "closeTime": "23:00",
                "isClosed": False,
                "isPrimaryPeriod": True,
            },
            {
                "openDay": "THURSDAY",
                "openTime": "11:00",
                "closeDay": "THURSDAY",
                "closeTime": "23:00",
                "isClosed": False,
                "isPrimaryPeriod": True,
            },
            {
                "openDay": "FRIDAY",
                "openTime": "11:00",
                "closeDay": "FRIDAY",
                "closeTime": "23:00",
                "isClosed": False,
                "isPrimaryPeriod": True,
            },
            {
                "openDay": "SATURDAY",
                "openTime": "11:00",
                "closeDay": "SATURDAY",
                "closeTime": "23:00",
                "isClosed": False,
                "isPrimaryPeriod": True,
            },
        ],
    },
}


def append_result(result, output_file="output.json"):
    # Read existing data if file exists
    if os.path.exists(output_file):
        with open(output_file, "r", encoding="utf-8") as f:
            try:
                data = json.load(f)
                if not isinstance(data, list):
                    data = [data]
            except json.JSONDecodeError:
                data = []
    else:
        data = []

    data.append(result)

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


def test_handler_faq_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.FAQ_GENERATION

    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_restaurant_page_url_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.RESTAURANT_RESTAURANT_PAGE_URL_GENERATION
    result = handler(event, context)
    append_result(result)

    assert result is not None


def test_handler_h1_title_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.H1_TITLE_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_h1_title_optimization():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.H1_TITLE_OPTIMIZATION
    event["restaurantData"]["previousGeneration"] = (
        "McDonald's SouthBay - Fast Food Burgers and Fries in Anytown"
    )
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_gallery_block_title_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.GALLERY_BLOCK_TITLE_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_gallery_block_title_optimization():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.GALLERY_BLOCK_TITLE_OPTIMIZATION
    event["restaurantData"]["previousGeneration"] = (
        "Burgers gourmands, frites croustillantes et service rapide au drive-thru McDonald's SouthBay Anytown"
    )
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_gallery_block_subtitle_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.GALLERY_BLOCK_SUBTITLE_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_gallery_block_subtitle_optimization():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.GALLERY_BLOCK_SUBTITLE_OPTIMIZATION
    event["restaurantData"]["previousGeneration"] = (
        "Profitez de notre drive-thru rapide avec burgers, frites et 20% de réduction"
    )
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_description_block_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.DESCRIPTION_BLOCK_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_head_meta_description_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.HEAD_META_DESCRIPTION_GENERATION
    result = handler(event, context)
    assert result is not None


def test_handler_head_meta_twitter_description_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.HEAD_META_TWITTER_DESCRIPTION_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_organization_keywords_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.ORGANIZATION_KEYWORDS_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_social_media_block_title_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.SOCIAL_MEDIA_BLOCK_TITLE_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_social_media_block_title_optimization():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.SOCIAL_MEDIA_BLOCK_TITLE_OPTIMIZATION
    event["restaurantData"]["previousGeneration"] = (
        "Moments McDo : Saveurs, Famille et Fun à Anytown !"
    )
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_cta_block_title_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.CTA_BLOCK_TITLE_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_cta_block_title_optimization():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.CTA_BLOCK_TITLE_OPTIMIZATION
    event["restaurantData"]["previousGeneration"] = (
        "Commandez Facilement, Dégustez Rapidement"
    )
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_map_twitter_description_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.MAP_TWITTER_DESCRIPTION_GENERATION
    result = handler(event, context)
    append_result(result)

    assert result is not None


def test_handler_map_keywords_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.MAP_KEYWORDS_GENERATION
    result = handler(event, context)
    append_result(result)

    assert result is not None


def test_handler_map_description_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.MAP_DESCRIPTION_GENERATION
    result = handler(event, context)
    append_result(result)

    assert result is not None


def test_handler_map_block_title_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.MAP_BLOCK_TITLE_GENERATION
    result = handler(event, context)
    append_result(result)

    assert result is not None


def test_handler_head_meta_description_optimization():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.HEAD_META_DESCRIPTION_OPTIMIZATION
    event["restaurantData"]["previousGeneration"] = (
        "McDonald's SouthBay - Fast food drive-thru 123 Main St Anytown. Burgers, frites pour familles. 20% off première commande ! Commandez maintenant."
    )
    result = handler(event, context)
    assert result is not None


def test_handler_reviews_block_title_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.REVIEWS_BLOCK_TITLE_GENERATION
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_reviews_block_title_optimization():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.REVIEWS_BLOCK_TITLE_OPTIMIZATION
    event["restaurantData"]["previousGeneration"] = (
        "Voix du Goût : Ce que nos familles d’Anytown racontent"
    )
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_description_block_title_optimization():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.DESCRIPTION_BLOCK_TITLE_OPTIMIZATION
    event["restaurantData"]["previousGeneration"] = (
        "McDonald's SouthBay - Fast Food Burgers and Fries in Anytown"
    )
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_description_block_subtitle_optimization():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.DESCRIPTION_BLOCK_SUBTITLE_OPTIMIZATION
    event["restaurantData"]["previousGeneration"] = (
        "McDonald's SouthBay - Fast Food Burgers and Fries in Anytown"
    )
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_description_block_content_optimization():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.DESCRIPTION_BLOCK_CONTENT_OPTIMIZATION
    event["restaurantData"]["previousGeneration"] = (
        "Préparez-vous à découvrir le nouveau McDonald's SouthBay Anytown au 123 Main St ! Notre fast food restaurant ouvre bientôt ses portes pour accueillir les familles et jeunes adultes d'Anytown. Avec une ambiance décontractée et conviviale, nous sommes fiers de vous proposer nos célèbres burgers et frites dans un cadre chaleureux. Que vous veniez entre amis ou en famille, notre équipe vous réserve un accueil souriant. Pour célébrer notre ouverture, profitez d'une offre exceptionnelle : 20% de réduction sur votre première commande ! Une occasion parfaite de goûter à nos spécialités dans ce nouveau restaurant drive-thru d'Anytown"
    )
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_description_block_content_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.DESCRIPTION_BLOCK_CONTENT_GENERATION
    event["restaurantData"]["context"] = [
        {
            "description_block_title_generation": "McDonald's SouthBay - Fast Food Burgers and Fries in Anytown"
        },
        {
            "description_block_subtitle_generation": "McDonald's SouthBay - Fast Food Burgers and Fries in Anytown"
        },
    ]
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_description_block_title_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.DESCRIPTION_BLOCK_TITLE_GENERATION
    event["restaurantData"]["context"] = [
        {
            "description_block_content_generation": "Préparez-vous à découvrir le nouveau McDonald's SouthBay Anytown au 123 Main St ! Notre fast food restaurant ouvre bientôt ses portes pour accueillir les familles et jeunes adultes d'Anytown. Avec une ambiance décontractée et conviviale, nous sommes fiers de vous proposer nos célèbres burgers et frites dans un cadre chaleureux. Que vous veniez entre amis ou en famille, notre équipe vous réserve un accueil souriant. Pour célébrer notre ouverture, profitez d'une offre exceptionnelle : 20% de réduction sur votre première commande ! Une occasion parfaite de goûter à nos spécialités dans ce nouveau restaurant drive-thru d'Anytown"
        },
        {
            "description_block_subtitle_generation": "McDonald's SouthBay - Fast Food Burgers and Fries in Anytown"
        },
    ]
    result = handler(event, context)
    append_result(result)
    assert result is not None


def test_handler_description_block_subtitle_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.DESCRIPTION_BLOCK_SUBTITLE_GENERATION
    event["restaurantData"]["context"] = [
        {
            "description_block_content_generation": "Préparez-vous à découvrir le nouveau McDonald's SouthBay Anytown au 123 Main St ! Notre fast food restaurant ouvre bientôt ses portes pour accueillir les familles et jeunes adultes d'Anytown. Avec une ambiance décontractée et conviviale, nous sommes fiers de vous proposer nos célèbres burgers et frites dans un cadre chaleureux. Que vous veniez entre amis ou en famille, notre équipe vous réserve un accueil souriant. Pour célébrer notre ouverture, profitez d'une offre exceptionnelle : 20% de réduction sur votre première commande ! Une occasion parfaite de goûter à nos spécialités dans ce nouveau restaurant drive-thru d'Anytown"
        },
        {
            "description_block_title_generation": "McDonald's SouthBay - Fast Food Burgers and Fries in Anytown"
        },
    ]
    result = handler(event, context)
    append_result(result)
    assert result is not None
