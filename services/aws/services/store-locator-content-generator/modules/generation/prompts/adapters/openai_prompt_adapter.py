from modules.generation.model import (
    TextGenerationPayload,
    CONTEXT_PROMPTS,
    DEP<PERSON>DENCIES,
    LANGUAGES,
    CONTEXT_DEPENDENCIES,
)
from modules.generation.prompts.prompt_provider_abstraction import (
    PromptProviderAbstraction,
)


class OpenAiPromptAdapter(PromptProviderAbstraction):
    def get_prompt(self, payload: TextGenerationPayload):
        context = CONTEXT_PROMPTS.get(payload.type, None)
        dependencies = DEPENDENCIES.get(payload.type, None)
        prompt = ""
        if payload.previousGeneration and "previousGeneration" in dependencies:
            prompt += f"Input to optimize : {payload.previousGeneration}\n"
        if payload.language and "language" in dependencies:
            prompt += f"Content generated in : {LANGUAGES.get(payload.language.upper(), payload.language.upper())}\n"
        if payload.restaurantName and "restaurantName" in dependencies:
            prompt += f"Restaurant Name: {payload.restaurantName}\n"
        if payload.organizationName and "organizationName" in dependencies:
            prompt += f"Restaurant Group Chain Name: {payload.organizationName}\n"
        if payload.address and "address" in dependencies:
            if any(
                [
                    "paris" in payload.address.locality.lower(),
                    "lyon" in payload.address.locality.lower(),
                ]
            ):
                prompt += f"Restaurant City: {payload.address.locality} + Restaurant District :{payload.address.postalCode[-2:]} + Restaurant Address: {payload.address.formattedAddress}\n"
            else:
                prompt += f"Restaurant locality: {payload.address.locality} + Restaurant Address : {payload.address.formattedAddress}\n"
        if payload.inspiration and "inspiration" in dependencies:
            prompt += f"Use this content for inspiration: {payload.inspiration}\n"
        if payload.keywords and "keywords" in dependencies:
            prompt += f"Keywords: {', '.join(payload.keywords)}\n"
        if payload.brandTone and "brandTone" in dependencies:
            prompt += f"Brand Tone: {', '.join(payload.brandTone)}\n"
        if payload.targetAudience and "targetAudience" in dependencies:
            prompt += f"Target Audience: {', '.join(payload.targetAudience)}\n"
        if payload.specificsDirectives and "specificsDirectives" in dependencies:
            prompt += f"Specific Directives: {', '.join(payload.specificsDirectives)}\n"
        if payload.restaurantOffers and "restaurantOffers" in dependencies:
            prompt += f"Restaurant Offers: {', '.join(payload.restaurantOffers)}\n"
        if payload.restaurantContext and "restaurantContext" in dependencies:
            prompt += f"Restaurant Context: {', '.join(payload.restaurantContext)}\n"
        if payload.context and "context" in dependencies:
            prompt += "Previous generations Context:\n"
            for var in payload.context:
                for key, value in var.items():
                    if key in CONTEXT_DEPENDENCIES.get(payload.type, []):
                        prompt += f"{key.value}: {value}\n"
        if payload.regularHours and "regularHours" in dependencies:
            prompt += payload.regularHours
        return context, prompt
