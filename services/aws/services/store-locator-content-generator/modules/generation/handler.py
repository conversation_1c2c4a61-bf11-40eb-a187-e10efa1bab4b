from .model import (
    AiResponseMetadata,
    AiConfig,
    TextGenerationPayload,
    TextGeneration,
    TextGeneratedModel,
    BlockGenerated,
    KeywordsGenerated,
    Section,
    Block,
    AI_MAX_RETRIES,
    URLGeneratedModel,
    TooManyRetriesError,
    Schedule,
    FAQGeneratedModel,
    Faq,
)
from core.models.event_model import RequestRelatedEntityCollectionEnum, RequestTypeEnum
from openai_provider.ai_provider_port import AiProvider
from modules.generation.prompts.prompt_provider_port import (
    PromptProvider,
)

from openai_provider.adapters.openai.openai_provider_adapter import (
    OpenAiAdapter,
)
from anthropic_provider.adapters.anthropic.anthropic_provider_adapter import (
    AnthropicAdapter,
)
from utils.utils import SimpleTextCleaner
from pydantic import BaseModel
from typing import Any, List, Optional, Dict


class TextGenerator:
    def __init__(
        self,
        ai_providers: List[AiProvider] = [OpenAiAdapter(), AnthropicAdapter()],
        prompt_provider: PromptProvider = PromptProvider(),
    ) -> None:
        self.__openai_provider = ai_providers[0]
        self.__anthropic_provider = ai_providers[1]
        self.__prompt_provider = prompt_provider

    def post_processing(
        self, aiResponse: Any, _type_: RequestTypeEnum, response_model: BaseModel
    ) -> BaseModel:
        if _type_ == RequestTypeEnum.FAQ_SINGULAR_GENERATION:
            return Faq(question=aiResponse.question, answer=aiResponse.answer)
        if _type_ == RequestTypeEnum.FAQ_GENERATION:
            return FAQGeneratedModel(faqs=aiResponse.faqs)
        if _type_ == RequestTypeEnum.RESTAURANT_RESTAURANT_PAGE_URL_GENERATION:
            url = (
                "/restaurant-"
                + aiResponse.specialty.replace(" ", "-").lower()
                + "/"
                + aiResponse.location.replace(" ", "-").lower()
            )
            response_model_ = TextGeneratedModel.create_with_constraints(
                type_name=_type_
            )
            return response_model_(text=url)
        elif _type_ in [
            RequestTypeEnum.HEAD_META_DESCRIPTION_GENERATION,
            RequestTypeEnum.HEAD_META_DESCRIPTION_OPTIMIZATION,
            RequestTypeEnum.HEAD_META_TWITTER_DESCRIPTION_GENERATION,
            RequestTypeEnum.MAP_DESCRIPTION_GENERATION,
            RequestTypeEnum.MAP_TWITTER_DESCRIPTION_GENERATION,
            RequestTypeEnum.MAP_BLOCK_TITLE_GENERATION,
            RequestTypeEnum.GALLERY_BLOCK_TITLE_GENERATION,
            RequestTypeEnum.GALLERY_BLOCK_SUBTITLE_GENERATION,
            RequestTypeEnum.GALLERY_BLOCK_TITLE_OPTIMIZATION,
            RequestTypeEnum.GALLERY_BLOCK_SUBTITLE_OPTIMIZATION,
            RequestTypeEnum.H1_TITLE_GENERATION,
            RequestTypeEnum.H1_TITLE_OPTIMIZATION,
            RequestTypeEnum.REVIEWS_BLOCK_TITLE_GENERATION,
            RequestTypeEnum.REVIEWS_BLOCK_TITLE_OPTIMIZATION,
            RequestTypeEnum.SOCIAL_MEDIA_BLOCK_TITLE_GENERATION,
            RequestTypeEnum.SOCIAL_MEDIA_BLOCK_TITLE_OPTIMIZATION,
            RequestTypeEnum.DESCRIPTION_BLOCK_TITLE_OPTIMIZATION,
            RequestTypeEnum.DESCRIPTION_BLOCK_SUBTITLE_OPTIMIZATION,
            RequestTypeEnum.DESCRIPTION_BLOCK_CONTENT_OPTIMIZATION,
            RequestTypeEnum.DESCRIPTION_BLOCK_CONTENT_GENERATION,
            RequestTypeEnum.DESCRIPTION_BLOCK_SUBTITLE_GENERATION,
            RequestTypeEnum.DESCRIPTION_BLOCK_TITLE_GENERATION,
            RequestTypeEnum.CTA_BLOCK_TITLE_GENERATION,
            RequestTypeEnum.CTA_BLOCK_TITLE_OPTIMIZATION,
            RequestTypeEnum.FAQ_QUESTION_GENERATION,
            RequestTypeEnum.FAQ_ANSWER_GENERATION,
            RequestTypeEnum.FAQ_QUESTION_OPTIMIZATION,
            RequestTypeEnum.FAQ_ANSWER_OPTIMIZATION,
        ]:
            text = SimpleTextCleaner.clean(aiResponse.text)
            return response_model(text=text)

        elif _type_ in [
            RequestTypeEnum.ORGANIZATION_KEYWORDS_GENERATION,
            RequestTypeEnum.MAP_KEYWORDS_GENERATION,
        ]:
            return response_model(
                keywords=[
                    SimpleTextCleaner.clean(keyword) for keyword in aiResponse.keywords
                ],
            )
        elif _type_ == RequestTypeEnum.DESCRIPTION_BLOCK_GENERATION:
            return response_model(
                blocks=[
                    Block(
                        title=SimpleTextCleaner.clean(block.title),
                        sections=[
                            Section(
                                subtitle=SimpleTextCleaner.clean(section.subtitle),
                                text=SimpleTextCleaner.clean(section.text),
                            )
                            for section in block.sections
                        ],
                    )
                    for block in aiResponse.blocks
                ]
            )

    # Function to generate opening hours paragraph
    def summarize_schedule(self, schedules: List[Schedule]) -> str:
        # Initialize variables
        schedule_ranges = []
        current_range = []

        for i, schedule in enumerate(schedules):
            if schedule.isClosed:
                continue

            # If it's the first schedule or if it continues the same day range
            if not current_range:
                current_range.append(schedule)
            else:
                # Check if we can merge consecutive days with the same hours
                last_schedule = current_range[-1]
                if (
                    last_schedule.closeDay == schedule.openDay
                    and last_schedule.openTime == schedule.openTime
                    and last_schedule.closeTime == schedule.closeTime
                ):
                    current_range.append(schedule)
                else:
                    # Otherwise, store the current range and start a new one
                    schedule_ranges.append(current_range)
                    current_range = [schedule]

        # Add the last range if it exists
        if current_range:
            schedule_ranges.append(current_range)

        # Construct the output string
        summary = []
        for range_group in schedule_ranges:
            # For each group of schedules, determine the range
            start_day = range_group[0].openDay
            end_day = range_group[-1].closeDay
            open_time = range_group[0].openTime
            close_time = range_group[-1].closeTime

            # If the schedule spans multiple days with the same time
            if start_day == end_day:
                summary.append(
                    f"{start_day.capitalize()} from {open_time} to {close_time}"
                )
            else:
                # Check if there are consecutive days with the same schedule
                consecutive_days = [start_day]
                for j in range(1, len(range_group)):
                    if (
                        range_group[j].openTime == open_time
                        and range_group[j].closeTime == close_time
                    ):
                        consecutive_days.append(range_group[j].openDay)
                    else:
                        break

                # If we have consecutive days with the same schedule, group them
                if len(consecutive_days) > 1:
                    summary.append(
                        f"{consecutive_days[0].capitalize()} to {consecutive_days[-1].capitalize()} from {open_time} to {close_time}"
                    )
                else:
                    summary.append(
                        f"{start_day.capitalize()} from {open_time} to {close_time}"
                    )

        # Join the different parts and return the complete string
        return ", ".join(summary) + "."

    def ai_config(self, _type_: RequestTypeEnum) -> AiConfig:
        if _type_ == RequestTypeEnum.RESTAURANT_RESTAURANT_PAGE_URL_GENERATION:
            return AiConfig(
                frequencyPenalty=0,
                temperature=0.6,
                timeout=55,
                model="gpt-4.1-mini",
                topP=1.0,
            )
        elif _type_ in [
            RequestTypeEnum.H1_TITLE_GENERATION,
            RequestTypeEnum.H1_TITLE_OPTIMIZATION,
            RequestTypeEnum.MAP_BLOCK_TITLE_GENERATION,
            RequestTypeEnum.MAP_DESCRIPTION_GENERATION,
            RequestTypeEnum.MAP_TWITTER_DESCRIPTION_GENERATION,
            RequestTypeEnum.DESCRIPTION_BLOCK_CONTENT_GENERATION,
            RequestTypeEnum.DESCRIPTION_BLOCK_CONTENT_OPTIMIZATION,
        ]:
            return AiConfig(
                frequencyPenalty=0,
                temperature=0.8,
                timeout=55,
                model="gpt-4.1-mini",
                topP=1.0,
            )
        elif _type_ in [
            RequestTypeEnum.GALLERY_BLOCK_TITLE_GENERATION,
            RequestTypeEnum.GALLERY_BLOCK_SUBTITLE_GENERATION,
            RequestTypeEnum.GALLERY_BLOCK_TITLE_OPTIMIZATION,
            RequestTypeEnum.GALLERY_BLOCK_SUBTITLE_OPTIMIZATION,
            RequestTypeEnum.REVIEWS_BLOCK_TITLE_GENERATION,
            RequestTypeEnum.REVIEWS_BLOCK_TITLE_OPTIMIZATION,
            RequestTypeEnum.CTA_BLOCK_TITLE_GENERATION,
            RequestTypeEnum.CTA_BLOCK_TITLE_OPTIMIZATION,
            RequestTypeEnum.SOCIAL_MEDIA_BLOCK_TITLE_GENERATION,
            RequestTypeEnum.SOCIAL_MEDIA_BLOCK_TITLE_OPTIMIZATION,
            RequestTypeEnum.DESCRIPTION_BLOCK_TITLE_OPTIMIZATION,
            RequestTypeEnum.DESCRIPTION_BLOCK_TITLE_GENERATION,
            RequestTypeEnum.DESCRIPTION_BLOCK_SUBTITLE_OPTIMIZATION,
            RequestTypeEnum.DESCRIPTION_BLOCK_SUBTITLE_GENERATION,
        ]:
            return AiConfig(
                frequencyPenalty=0,
                temperature=1.0,
                timeout=55,
                model="gpt-4.1-mini",
                topP=1.0,
            )
        elif _type_ in [
            RequestTypeEnum.HEAD_META_DESCRIPTION_GENERATION,
            RequestTypeEnum.HEAD_META_DESCRIPTION_OPTIMIZATION,
            RequestTypeEnum.HEAD_META_TWITTER_DESCRIPTION_GENERATION,
            RequestTypeEnum.DESCRIPTION_BLOCK_GENERATION,
            RequestTypeEnum.FAQ_GENERATION,
            RequestTypeEnum.FAQ_QUESTION_GENERATION,
            RequestTypeEnum.FAQ_ANSWER_GENERATION,
            RequestTypeEnum.FAQ_QUESTION_OPTIMIZATION,
            RequestTypeEnum.FAQ_ANSWER_OPTIMIZATION,
            RequestTypeEnum.FAQ_SINGULAR_GENERATION,
        ]:
            return AiConfig(
                frequencyPenalty=0,
                temperature=0.9,
                timeout=55,
                model="claude-sonnet-4-20250514",
                topP=1.0,
            )
        elif _type_ in [
            RequestTypeEnum.ORGANIZATION_KEYWORDS_GENERATION,
            RequestTypeEnum.MAP_KEYWORDS_GENERATION,
        ]:
            return AiConfig(
                frequencyPenalty=0,
                temperature=0.6,
                timeout=55,
                model="gpt-4.1-mini",
                topP=1.0,
            )

    def get_response_model(self, _type_: RequestTypeEnum) -> BaseModel:
        if _type_ == RequestTypeEnum.FAQ_SINGULAR_GENERATION:
            return Faq
        if _type_ == RequestTypeEnum.FAQ_GENERATION:
            return FAQGeneratedModel
        elif _type_ in [
            RequestTypeEnum.ORGANIZATION_KEYWORDS_GENERATION,
            RequestTypeEnum.MAP_KEYWORDS_GENERATION,
        ]:
            return KeywordsGenerated
        elif _type_ == RequestTypeEnum.DESCRIPTION_BLOCK_GENERATION:
            return BlockGenerated
        elif _type_ == RequestTypeEnum.RESTAURANT_RESTAURANT_PAGE_URL_GENERATION:
            return URLGeneratedModel
        else:
            return TextGeneratedModel.create_with_constraints(type_name=_type_)

    def ai_call(
        self,
        prompt: str,
        context: str,
        configuration: AiConfig,
        response_model: BaseModel,
        __type__: RequestTypeEnum,
    ) -> Any:
        if __type__ in [
            RequestTypeEnum.HEAD_META_DESCRIPTION_GENERATION,
            RequestTypeEnum.HEAD_META_TWITTER_DESCRIPTION_GENERATION,
            RequestTypeEnum.DESCRIPTION_BLOCK_GENERATION,
            RequestTypeEnum.HEAD_META_DESCRIPTION_OPTIMIZATION,
            RequestTypeEnum.FAQ_GENERATION,
            RequestTypeEnum.FAQ_QUESTION_GENERATION,
            RequestTypeEnum.FAQ_ANSWER_GENERATION,
            RequestTypeEnum.FAQ_QUESTION_OPTIMIZATION,
            RequestTypeEnum.FAQ_ANSWER_OPTIMIZATION,
            RequestTypeEnum.FAQ_SINGULAR_GENERATION,
        ]:
            return self.__anthropic_provider.callWithContext(
                prompt, context, configuration, response_model
            )

        else:
            return self.__openai_provider.callWithContext(
                prompt, context, configuration, response_model
            )

    def execute(self, payload: TextGenerationPayload) -> TextGeneration:
        # if "paris" or "lyon" in payload.address.locality -> use payload.address.postalCode
        if "paris" not in payload.address.locality.lower():
            payload.address.postalCode = ""
        if payload.regularHours:
            payload.regularHours = self.summarize_schedule(payload.regularHours)
        context, prompt = self.__prompt_provider.get_prompt(payload=payload)
        configuration = self.ai_config(payload.type)
        response_model = self.get_response_model(payload.type)
        for retry in range(AI_MAX_RETRIES):
            try:
                ai_response_details = self.ai_call(
                    prompt=prompt,
                    context=context,
                    configuration=configuration,
                    response_model=response_model,
                    __type__=payload.type,
                )
                break
            except:
                if retry == AI_MAX_RETRIES - 1:
                    raise TooManyRetriesError("Exceeded maximum number of retries.")
                continue

        return TextGeneration(
            aiResponse=self.post_processing(
                ai_response_details.response, payload.type, response_model
            ),
            aiInteractionDetails=AiResponseMetadata(
                relatedEntityCollection=RequestRelatedEntityCollectionEnum.STORE_LOCATOR.value,
                type=payload.type,
                message=ai_response_details.metadata.message,
                completionText=str(ai_response_details.response),
                completionTokenCount=ai_response_details.metadata.completion_tokens,
                promptTokenCount=ai_response_details.metadata.prompt_tokens,
                completionTimeInMilliseconds=ai_response_details.metadata.request_response_time,
                modelConfig=configuration,
                numberOfRetries=retry,
            ),
        ).model_dump()
