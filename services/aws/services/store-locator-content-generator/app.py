from aws_lambda_powertools.utilities.parser import event_parser
from aws_lambda_powertools.utilities.typing import LambdaContext
from core.models.event_model import (
    EventModel,
    RequestTypeEnum,
    RequestRelatedEntityCollectionEnum,
)
from modules.generation.controller import (
    TextGeneratorController,
)
from modules.generation.model import TextGenerationPayload


@event_parser(model=EventModel)
def handler(event: EventModel, context: LambdaContext):
    try:
        if (
            event.relatedEntityCollection
            == RequestRelatedEntityCollectionEnum.STORE_LOCATOR
        ):
            return TextGeneratorController().generate(
                event=TextGenerationPayload(
                    type=event.type,
                    regularHours=event.restaurantData.get("regularHours", None),
                    previousGeneration=event.restaurantData.get(
                        "previousGeneration", None
                    ),
                    language=event.restaurantData.get("language", None),
                    restaurantName=event.restaurantData.get("restaurantName", None),
                    organizationName=event.restaurantData.get("organizationName", None),
                    address=event.restaurantData.get("address", None),
                    keywords=event.restaurantData.get("keywords", []),
                    brandTone=event.restaurantData.get("brandTone", []),
                    targetAudience=event.restaurantData.get("targetAudience", []),
                    specificsDirectives=event.restaurantData.get(
                        "specificsDirectives", []
                    ),
                    restaurantOffers=event.restaurantData.get("restaurantOffers", []),
                    restaurantContext=event.restaurantData.get("restaurantContext", []),
                    context=event.restaurantData.get("context", []),
                )
            )

    except Exception as e:
        print(f"Error in handler: {e}")
        raise
