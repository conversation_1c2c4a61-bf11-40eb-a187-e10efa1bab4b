import { Day } from ':constants/enum';
import type {
    InformationBlockExtraStyles,
    InformationBlockTranslation,
    IStorePage,
} from ':interfaces/pages.interfaces';

class DisplaySchedule extends HTMLElement {
    connectedCallback() {
        const information = JSON.parse(
            this.dataset.information!,
        ) as IStorePage['informationBlock'];

        const translation = JSON.parse(
            this.dataset.translation!,
        ) as InformationBlockTranslation;

        const styles = JSON.parse(
            this.dataset.styles!,
        ) as InformationBlockExtraStyles;

        // Add event listener to redirect to maps
        function redirectMaps(event: Event) {
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

            if (isIOS) {
                window.location.href = `https://maps.apple.com/?q=${information.coordinates.lat},${information.coordinates.lng}`;
                event.preventDefault(); // Stop default Google Maps link
            }
        }

        document
            .getElementById('itinerary-link')
            ?.addEventListener('click', redirectMaps);
        document
            .getElementById('itinerary-button')
            ?.addEventListener('click', redirectMaps);

        if (!information.hours || information.hours.length === 0) {
            return;
        }
        const today = new Date();
        const dayOfWeek = today.getDay();

        const todaySchedule = information.hours[dayOfWeek - 1];
        const tomorrowSchedule = information.hours[dayOfWeek];

        const scheduleOfToday = information.hours.find(
            (h) => h.day === todaySchedule!.day,
        );
        const textNow = getFormattedScheduleOfTheDayOpenOrSoonOpen(
            scheduleOfToday!.periods,
            todaySchedule!.day as Day,
            today,
            false,
            translation,
        );

        const scheduleOfTomorrow = information.hours.find(
            (h) => h.day === tomorrowSchedule!.day,
        );
        const textTomorrow = getFormattedScheduleOfTheDayOpenOrSoonOpen(
            scheduleOfTomorrow!.periods,
            tomorrowSchedule!.day as Day,
            today,
            true,
            translation,
        );

        const schedule = information.hours;
        // need to sort the days by starting with the current day
        const scheduleSortedByStartingTheCurrentDay = schedule
            .slice(new Date().getDay() - 1, schedule.length)
            .concat(schedule.slice(0, new Date().getDay() - 1));

        const ptags = scheduleSortedByStartingTheCurrentDay.map((h, idx) => {
            if (idx === 0) {
                return createScheduleElement({
                    formattedHour: textNow,
                    translation,
                    styles,
                });
            }
            if (idx === 1) {
                return createScheduleElement({
                    formattedHour: textTomorrow,
                    translation,
                    styles,
                });
            }
            return createScheduleElement({
                formattedHour: h.formattedHour,
                translation,
                styles,
            });
        });

        const firstColumn = document.getElementById('hours-column-1');
        const secondColumn = document.getElementById('hours-column-2');

        ptags.forEach((p) => {
            if (ptags.indexOf(p) < 4) {
                firstColumn?.appendChild(p);
            } else {
                secondColumn?.appendChild(p);
            }
        });
    }
}

function getFormattedScheduleOfTheDayOpenOrSoonOpen(
    hours: IStorePage['informationBlock']['hours'][0]['periods'],
    day: Day,
    now: Date,
    isTomorrow: boolean,
    translation: InformationBlockTranslation,
) {
    return (
        translation.DAYS[day] +
        ' : ' +
        hours
            .map((p) => {
                if (p.isClosed) {
                    return translation.CLOSED;
                }

                const nowOrTomorrow = new Date().setDate(
                    now.getDate() + (isTomorrow ? 1 : 0),
                );

                const openTimeHours = p.openTime!.split(':')[0];
                const openTimeMinutes = p.openTime!.split(':')[1];
                const closeTimeHours = p.closeTime!.split(':')[0];
                const closeTimeMinutes = p.closeTime!.split(':')[1];

                const openTimeDate = new Date(
                    new Date(nowOrTomorrow).setHours(
                        +openTimeHours!,
                        +openTimeMinutes!,
                    ),
                );
                const closeTimeDate = new Date(
                    new Date(nowOrTomorrow).setHours(
                        +closeTimeHours!,
                        +closeTimeMinutes!,
                    ),
                );

                if (now >= openTimeDate && now <= closeTimeDate) {
                    return `${translation.OPEN_NOW} - ${p.closeTime}`;
                }

                const openTimeDateForTomorrow = new Date(
                    new Date(nowOrTomorrow).setHours(
                        +openTimeHours!,
                        +openTimeMinutes!,
                    ),
                );

                // need to do this because setHours is mutating the date
                const openTimeDateForTomorrow2 = new Date(
                    new Date(nowOrTomorrow).setHours(
                        +openTimeHours!,
                        +openTimeMinutes!,
                    ),
                );

                if (
                    now >=
                        new Date(
                            openTimeDateForTomorrow.setHours(
                                openTimeDateForTomorrow.getHours() - 1,
                            ),
                        ) &&
                    now < openTimeDateForTomorrow2
                ) {
                    return `${translation.OPEN_SOON} ${p.openTime} - ${p.closeTime}`;
                }

                return `${p.openTime} - ${p.closeTime}`;
            })
            .join(', ')
    );
}

function createScheduleElement({
    formattedHour,
    translation,
    styles,
}: {
    formattedHour: string;
    translation: InformationBlockTranslation;
    styles: InformationBlockExtraStyles;
}) {
    const pTag = document.createElement('p');
    pTag.textContent = formattedHour;

    if (
        styles.hoursHighlight &&
        (formattedHour.includes(translation.OPEN_NOW) ||
            formattedHour.includes(translation.OPEN_SOON))
    ) {
        pTag.classList.add(styles.hoursHighlight ?? '');
    }

    return pTag;
}

customElements.define('display-schedule', DisplaySchedule);
