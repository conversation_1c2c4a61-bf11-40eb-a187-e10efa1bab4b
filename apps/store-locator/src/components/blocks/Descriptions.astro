---
import type { IStorePage } from ':interfaces/pages.interfaces';
import ':styles/global.css';
import { getStyles } from ':utils/get-element-styles';
import { Picture } from 'astro:assets';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    descriptionsBlock: NonNullable<IStorePage['descriptionsBlock']>;
    styles: IStorePage['styles'];
}

const { descriptionsBlock, styles } = Astro.props as Props;
const getElementStyles = getStyles({ styles });
---

<div class="flex flex-col items-center justify-center">
    {
        descriptionsBlock.items.map((descriptionBlock, index) => {
            const isEven = index % 2 === 0;

            return (
                <div
                    class={`${isEven ? getElementStyles({ elementId: 'descriptions-block-even' }) : getElementStyles({ elementId: 'descriptions-block-uneven' })} w-full`}
                >
                    <div class="mx-auto flex w-full max-w-[1600px] flex-col items-stretch justify-center lg:flex-row">
                        <div
                            class={`w-full ${isEven ? 'lg:order-last' : ''} relative h-[225px] overflow-hidden sm:h-[450px] lg:h-auto lg:w-1/2`}
                        >
                            <Picture
                                src={descriptionBlock.imageUrl}
                                formats={['webp']}
                                fallbackFormat="jpg"
                                alt={descriptionBlock.imageDescription}
                                class="absolute h-full w-full object-cover object-center"
                                widths={[3000, 2000, 1000, 750, 500, 250]}
                                sizes="(max-width: 1024px) 100vw, 50vw"
                                inferSize
                            />
                        </div>
                        <div
                            class={`box-border w-full px-5 py-14 sm:px-8 lg:w-1/2 lg:px-14 lg:py-28`}
                        >
                            <h2
                                class={`${isEven ? getElementStyles({ elementId: 'descriptions-block-title-even' }) : getElementStyles({ elementId: 'descriptions-block-title-uneven' })} mb-6 w-full text-3xl font-extrabold uppercase sm:text-4xl`}
                                set:html={descriptionBlock.title}
                            />

                            <div class="flex flex-col gap-4">
                                {descriptionBlock.blocks.map(
                                    ({ title, text }) => {
                                        return (
                                            <>
                                                <h3
                                                    class={`text-xl ${getElementStyles({ elementId: 'descriptions-block-subtitle' })}`}
                                                    set:html={title}
                                                />
                                                <p set:html={text} />
                                            </>
                                        );
                                    },
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            );
        })
    }
</div>
