---
import type { IStorePage } from ':interfaces/pages.interfaces';
import ':styles/global.css';
import { getStyles } from ':utils/get-element-styles';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    callToActionsBlock: NonNullable<IStorePage['callToActionsBlock']>;
    styles: IStorePage['styles'];
}

const { callToActionsBlock, styles } = Astro.props as Props;

const getElementStyles = getStyles({ styles });
---

<div class={getElementStyles({ elementId: 'call-to-actions-wrapper' })}>
    <div
        class="flex max-w-[1600px] flex-col items-center justify-center px-2 py-14 sm:mx-auto sm:px-14"
    >
        <h2
            class={`${getElementStyles({ elementId: 'call-to-actions-title' })} pb-8 text-center text-3xl font-extrabold sm:text-4xl uppercase`}
            set:html={callToActionsBlock.title}
        />
        <ul class="flex flex-col items-center">
            {
                callToActionsBlock.links.map(({ text, url, tracker }) => (
                    <li>
                        <a
                            target="_blank"
                            aria-label={text}
                            href={url}
                            class={`${getElementStyles({ elementId: 'call-to-actions-cta' })} analytics-tracker my-3 block w-[320px] border-[1px] border-solid p-4 text-center text-sm font-extralight uppercase`}
                            {...(tracker && {
                                'data-tracking-event-name': tracker.eventName,
                                'data-tracking-event-category':
                                    tracker.eventCategory,
                                'data-tracking-event-label': tracker.eventLabel,
                            })}
                        >
                            {text}
                        </a>
                    </li>
                ))
            }
        </ul>
    </div>
</div>
