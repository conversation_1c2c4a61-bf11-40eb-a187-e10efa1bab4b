---
import Cart from ':assets/icons/cart.svg';
import Hour from ':assets/icons/hour.svg';
import Phone from ':assets/icons/phone.svg';
import Pin from ':assets/icons/pin.svg';
import Voucher from ':assets/icons/voucher.svg';
import { Day } from ':constants/enum';
import { initTranslationFunction } from ':i18n/index';
import type {
    InformationBlockExtraStyles,
    IStorePage,
} from ':interfaces/pages.interfaces';
import { getStyles } from ':utils/get-element-styles';
import { Picture } from 'astro:assets';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    information: IStorePage['informationBlock'];
    styles: IStorePage['styles'];
}

const { information, styles } = Astro.props as Props;

const getElementStyles = getStyles({ styles });

const extraStyles: InformationBlockExtraStyles = {
    hoursHighlight: getElementStyles({
        elementId: 'information-hours-highlight',
    }),
};

// Hide imageUrl and all references to Malou
const { imageUrl, ...informationInClient } = information;

const t = await initTranslationFunction();

const translations = {
    CLOSED: t('information.closed'),
    OPEN_NOW: t('information.open-now'),
    OPEN_SOON: t('information.open-soon'),
    DAYS: {
        [Day.MONDAY]: t('information.days.monday'),
        [Day.TUESDAY]: t('information.days.tuesday'),
        [Day.WEDNESDAY]: t('information.days.wednesday'),
        [Day.THURSDAY]: t('information.days.thursday'),
        [Day.FRIDAY]: t('information.days.friday'),
        [Day.SATURDAY]: t('information.days.saturday'),
        [Day.SUNDAY]: t('information.days.sunday'),
    },
};
---

<display-schedule
    data-information={JSON.stringify(informationInClient)}
    data-translation={JSON.stringify(translations)}
    data-styles={JSON.stringify(extraStyles)}></display-schedule>

<div
    class={`${getElementStyles({ elementId: 'information-wrapper' })} mx-auto flex flex-col lg:flex-row`}
>
    <div class="relative w-full lg:flex lg:h-auto lg:min-h-0 lg:flex-1">
        <div
            class="h-[225px] w-full overflow-hidden sm:h-[450px] lg:absolute lg:inset-0 lg:h-full"
        >
            <Picture
                src={information.imageUrl}
                formats={['webp']}
                fallbackFormat="jpg"
                class="h-full w-full object-cover object-center"
                alt={information.imageDescription}
                widths={[3000, 2000, 1000, 750, 500, 250]}
                sizes="(max-width: 1024px) 100vw, 50vw"
                inferSize
                priority
            />
        </div>
    </div>

    <div class="flex min-h-fit w-full flex-1 flex-col gap-3 px-4 py-8 md:px-16">
        {
            information.isNotOpenedYet && (
                <p
                    class={`${getElementStyles({ elementId: 'information-opening-soon-banner' })} mb-4 p-6 text-center text-xl font-bold lg:text-3xl`}
                >
                    {t('information.opening-soon')}
                </p>
            )
        }
        <h1
            class={`${getElementStyles({ elementId: 'information-title' })} text-5xl md:text-6xl uppercase`}
        >
            {information.restaurantName}
        </h1>

        <div
            class="grid-cols-[repeat(7, auto);] sm:grid-rows-[repeat(7, auto);] mt-6 auto-cols-auto flex-wrap gap-y-5 text-sm font-normal sm:grid sm:auto-rows-auto sm:gap-x-3 md:gap-x-0 md:gap-y-2 md:text-base"
        >
            <div class="mb-4 sm:mb-0">
                <a
                    id="itinerary-link"
                    aria-label={t('information.itinerary.aria-label')}
                    href={information.itineraryUrl}
                    target="_blank"
                    class="analytics-tracker"
                    data-tracking-event-name="click-cta"
                    data-tracking-event-category="information-block"
                    data-tracking-event-label="itinerary-address"
                >
                    <p class="flex items-start gap-6 sm:gap-4">
                        <Pin
                            class={`${getElementStyles({ elementId: 'information-icons' })}`}
                            height={20}
                            width={20}
                        />
                        <span>{information.fullAddress}</span>
                    </p>
                </a>
            </div>

            <div class="mb-0">
                {
                    information.phone && (
                        <a
                            href={`tel:${information.phone}`}
                            class="analytics-tracker"
                            data-tracking-event-name="click-cta"
                            data-tracking-event-category="information-block"
                            data-tracking-event-label="phone"
                            aria-label={t('information.phone.aria-label')}
                        >
                            <p class="flex items-center gap-4">
                                <Phone
                                    class={`${getElementStyles({ elementId: 'information-icons' })}`}
                                    height={20}
                                    width={20}
                                />

                                {information.phone}
                            </p>
                        </a>
                    )
                }
            </div>

            {
                information.hours?.length > 0 && (
                    <>
                        <div class="col-span-2 mb-0">
                            <div class="my-4 w-full border-t" />
                        </div>

                        <div class="row-start-3">
                            <div class="flex gap-4">
                                <Hour
                                    class={`${getElementStyles({ elementId: 'information-icons' })}`}
                                    height={20}
                                    width={20}
                                />

                                <div
                                    id="hours-column-1"
                                    class="flex flex-col gap-2"
                                />
                            </div>
                        </div>

                        <div class="row-start-3 mt-2 mb-4 ml-9 md:mt-0 md:mb-0 md:ml-0">
                            <div
                                id="hours-column-2"
                                class="flex flex-col gap-2"
                            />
                        </div>
                    </>
                )
            }

            {
                information?.attributesNames?.length > 0 && (
                    <>
                        <div class="col-span-2 mb-0">
                            <div class="my-4 w-full border-t" />
                        </div>

                        <div class="col-span-2 row-start-5 mb-0">
                            <div class="flex items-center gap-4">
                                <Cart
                                    class={`${getElementStyles({ elementId: 'information-icons' })} min-w-5`}
                                    height={20}
                                    width={20}
                                />
                                <p>{information.attributesNames.join(', ')}</p>
                            </div>
                        </div>
                    </>
                )
            }

            {
                information?.paymentMethods?.length > 0 && (
                    <>
                        <div class="col-span-2 row-start-6 mb-0">
                            <div class="my-4 w-full border-t" />
                        </div>

                        <div class="col-span-2 row-start-7">
                            <div class="flex items-center gap-4">
                                <Voucher
                                    class={`${getElementStyles({ elementId: 'information-icons' })}`}
                                    height={20}
                                    width={20}
                                />
                                <p>{information.paymentMethods.join(', ')}</p>
                            </div>
                        </div>
                    </>
                )
            }
        </div>

        <div
            class={`${getElementStyles({ elementId: 'information-banner' })} fixed bottom-0 left-0 z-10 m-auto md:mt-6 md:mb-2 flex w-full justify-center gap-4 p-4 shadow-2xl md:static md:bg-transparent md:p-0 md:shadow-none`}
        >
            {
                information.phone && (
                    <a
                        href={`tel:${information.phone}`}
                        aria-label={t('information.phone.aria-label')}
                        data-tracking-event-name="click-cta"
                        data-tracking-event-category="information-block"
                        data-tracking-event-label="phone-banner"
                        class={`${getElementStyles({ elementId: 'information-banner-cta-1' })} analytics-tracker block border-[1px] border-solid bg-transparent px-7 py-4 font-bold shadow-md md:hidden`}
                    >
                        <Phone
                            class={getElementStyles({
                                elementId: 'information-banner-cta-icon',
                            })}
                            height={20}
                            width={20}
                        />
                    </a>
                )
            }

            {
                information.ctas?.[0] && (
                    <a
                        href={information.ctas[0].url}
                        aria-label={information.ctas[0].text}
                        target="_blank"
                        class={`${getElementStyles({ elementId: 'information-banner-cta-1' })} analytics-tracker border-[1px] border-solid px-7 py-4 font-extralight shadow-md hover:text-white`}
                        {...(information.ctas[0].tracker && {
                            'data-tracking-event-name':
                                information.ctas[0].tracker.eventName,
                            'data-tracking-event-category':
                                information.ctas[0].tracker.eventCategory,
                            'data-tracking-event-label':
                                information.ctas[0].tracker.eventLabel,
                        })}
                    >
                        <span class="uppercase">
                            {information.ctas[0].text}
                        </span>
                    </a>
                )
            }

            {
                information.ctas?.[1] && (
                    <a
                        href={information.ctas[1].url}
                        target="_blank"
                        aria-label={information.ctas[1].text}
                        class={`${getElementStyles({ elementId: 'information-banner-cta-2' })} analytics-tracker flex items-center gap-3 border-[1px] border-solid px-7 py-4 font-extralight shadow-md hover:bg-transparent`}
                        {...(information.ctas[1].tracker && {
                            'data-tracking-event-name':
                                information.ctas[1].tracker.eventName,
                            'data-tracking-event-category':
                                information.ctas[1].tracker.eventCategory,
                            'data-tracking-event-label':
                                information.ctas[1].tracker.eventLabel,
                        })}
                    >
                        <span class="uppercase">
                            {information.ctas[1].text}
                        </span>
                    </a>
                )
            }
        </div>
    </div>
</div>

<script src="./../../scripts/information-block/index.ts"></script>
