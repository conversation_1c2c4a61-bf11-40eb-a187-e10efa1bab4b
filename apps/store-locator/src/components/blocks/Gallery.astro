---
import type { IStorePage } from ':interfaces/pages.interfaces';
import ':styles/global.css';
import { getStyles } from ':utils/get-element-styles';
import { Picture } from 'astro:assets';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    galleryBlock: NonNullable<IStorePage['galleryBlock']>;
    styles: IStorePage['styles'];
}

const { galleryBlock, styles } = Astro.props as Props;
const getElementStyles = getStyles({ styles });
---

<div class={getElementStyles({ elementId: 'gallery-wrapper' })}>
    <div
        class="mx-auto flex max-w-[1600px] flex-col items-center justify-center py-12"
    >
        <h2
            class={`${getElementStyles({ elementId: 'gallery-title' })} px-1 pb-4 text-center text-3xl font-extrabold sm:px-12 sm:text-4xl uppercase`}
            set:html={galleryBlock.title}
        />
        <div
            class={`px-1 pb-8 text-center text-lg sm:px-12 sm:text-xl`}
            set:html={galleryBlock.subtitle}
        />

        <div class="flex w-full flex-col gap-2 lg:flex-row lg:px-8">
            {
                galleryBlock.images[0] && (
                    <div
                        class={`${getElementStyles({ elementId: 'gallery-picture' })} h-[175px] w-full overflow-hidden sm:h-[350px] md:h-[450px] lg:h-[708px] lg:w-1/3`}
                    >
                        <Picture
                            src={galleryBlock.images[0].url}
                            formats={['webp']}
                            fallbackFormat="jpg"
                            alt={galleryBlock.images[0].description}
                            class="h-full w-full object-cover object-center"
                            widths={[3000, 2000, 1000, 750, 500, 250]}
                            sizes="(max-width: 1024px) 100vw, 33vw"
                            inferSize
                        />
                    </div>
                )
            }

            <div
                class="grid w-full grid-cols-2 flex-row flex-wrap gap-2 lg:h-[708px] lg:w-2/3 lg:grid-cols-3"
            >
                {
                    galleryBlock.images.slice(1).map(({ description, url }) => (
                        <div
                            class={`${getElementStyles({ elementId: 'gallery-picture' })} h-[175px] overflow-hidden sm:h-[350px] md:h-[450px] lg:h-[350px]`}
                        >
                            <Picture
                                src={url}
                                formats={['webp']}
                                fallbackFormat="jpg"
                                alt={description}
                                class="h-full w-full object-cover object-center"
                                widths={[2000, 1000, 750, 500, 250]}
                                sizes="(max-width: 1024px) 50vw, 25vw"
                                inferSize
                            />
                        </div>
                    ))
                }
            </div>
        </div>
    </div>
</div>
