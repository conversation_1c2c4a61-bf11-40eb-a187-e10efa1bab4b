import { GoogleSpreadsheet } from 'google-spreadsheet';
import { chunk } from 'lodash';
import { AnyBulkWriteOperation } from 'mongoose';

import { CalendarEventModel, ID, IRestaurant, PopulateBuilderHelper, RestaurantModel } from '@malou-io/package-models';
import { isNotNil } from '@malou-io/package-utils';

import { Config } from ':config';

import Fixture from '../AbstractFixtures';

export default class EventsFixtures extends Fixture {
    async load() {
        await this._removeByDefaultEventsFromRestaurant();
        await this._addEvents();
        await this._addToRestaurants();
    }

    async _addToRestaurants() {
        const eventsByDefault = await CalendarEventModel.find({ byDefault: true });
        const eventsIds = eventsByDefault.map((evt) => evt._id);

        const cursor = RestaurantModel.find<IRestaurant>(
            {},
            {},
            {
                lean: true,
            }
        ).cursor();

        await cursor.eachAsync(
            async (restaurants) => {
                const bulkOperations = restaurants.map((rest) => {
                    const eventIds = rest.calendarEvents.concat(eventsIds);
                    return {
                        updateOne: {
                            filter: { _id: rest._id },
                            update: { $set: { calendarEvents: eventIds } },
                        },
                    };
                });

                await RestaurantModel.bulkWrite(bulkOperations);
            },
            { batchSize: 100 }
        );
    }

    async _removeByDefaultEventsFromRestaurant() {
        const cursor = RestaurantModel.find<
            PopulateBuilderHelper<
                IRestaurant,
                [
                    {
                        path: 'calendarEvents';
                    },
                ]
            >
        >(
            {},
            {},
            {
                lean: true,
                populate: [{ path: 'calendarEvents' }],
            }
        ).cursor();

        await cursor.eachAsync(
            async (restaurants: any[]) => {
                const bulkOperations: AnyBulkWriteOperation[] = restaurants.map((rest) => {
                    const personalEvents = rest.calendarEvents.filter((evt) => !evt.byDefault);
                    const eventIds: ID[] = personalEvents.filter((evt) => !!evt._id).map((p) => p._id);
                    return {
                        updateOne: {
                            filter: { _id: rest._id },
                            update: { $set: { calendarEvents: eventIds } },
                        },
                    };
                });

                await RestaurantModel.bulkWrite(bulkOperations);
            },
            { batchSize: 100 }
        );
    }

    async _addEvents() {
        const doc = new GoogleSpreadsheet('14T-7bxYt2jGfnLYINN-1Lsc-WWLYvEps_aW1n0WHHzc');

        const privateKeys = Config.businessNotificationsAccountKey;
        const spreadSheetAuth = {
            client_email: privateKeys.client_email as string,
            private_key: privateKeys.private_key?.split('||n||').join('\n'),
        };
        await doc.useServiceAccountAuth(spreadSheetAuth);
        await doc.loadInfo();

        const eventsSheet = doc.sheetsByTitle.all_events_please_add_rows_in_this_tab;
        const eventsRows = await eventsSheet.getRows();

        const events = eventsRows.map((record) => this._createEvent(record)).filter((evt) => evt);

        console.log(`${events.length} events fetched from Google Sheet`);

        let index = 0;
        await CalendarEventModel.deleteMany({ byDefault: true });

        const eventsChunks = chunk(events, 100);
        for (const eventsChunk of eventsChunks) {
            const bulkOperations = eventsChunk.map((event) => {
                if (!event || !event.name?.fr || !event.date) {
                    return null;
                }
                index += 1;
                return {
                    updateOne: {
                        filter: {
                            'date.day': event.date.day,
                            'date.month': event.date.month,
                            'date.year': event.date.year,
                            'name.fr': event.name.fr,
                            country: event.country,
                        },
                        update: {
                            $set: {
                                date: event.date,
                                name: event.name,
                                emoji: event.emoji,
                                isBankHoliday: event.isBankHoliday,
                                ideas: event.ideas,
                                example: event.example,
                                byDefault: true,
                                country: event.country,
                                shouldSuggestToPost: event.shouldSuggestToPost,
                                shouldSuggestSpecialHourUpdate: event.shouldSuggestSpecialHourUpdate,
                                updatedAt: new Date(),
                            },
                        },
                        upsert: true,
                    },
                };
            });

            await CalendarEventModel.bulkWrite(bulkOperations.filter(isNotNil));
        }

        console.log(`Inserted ${index} events`);
    }

    _createEvent(cellElt) {
        const {
            date,
            event,
            event_en: eventEn,
            event_it: eventIt,
            event_es: eventEs,
            country,
            holiday,
            ideas,
            ideas_en: ideasEn,
            ideas_it: ideasIt,
            ideas_es: ideasEs,
            example,
            example_en: exampleEn,
            emoji,
            post_notification_suggestion: postNotificationSuggestion,
            holiday_notification_suggestion: holidayNotificationSuggestion,
        } = cellElt;
        if (!date) {
            return;
        }
        const day = parseInt(date.substring(0, 2), 10);
        const month = parseInt(date.substring(3, 5), 10);
        const year = parseInt(date.substring(6), 10);
        const dayMonthYear = {
            day,
            month,
            year,
        };
        return {
            date: dayMonthYear,
            country,
            name: {
                fr: event?.trim(),
                en: eventEn?.trim(),
                it: eventIt?.trim(),
                es: eventEs?.trim(),
            },
            isBankHoliday: holiday === 'OUI',
            ideas: {
                fr: ideas?.trim(),
                en: ideasEn?.trim(),
                it: ideasIt?.trim(),
                es: ideasEs?.trim(),
            },
            example: {
                fr: example?.trim(),
                en: exampleEn?.trim(),
            },
            emoji,
            shouldSuggestToPost: {
                active: postNotificationSuggestion === 'TRUE',
                concernedRestaurantCategories: [],
            },
            shouldSuggestSpecialHourUpdate: holidayNotificationSuggestion === 'TRUE',
            byDefault: true,
        };
    }
}

module.exports = EventsFixtures;
