import { groupBy, sumBy } from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';
import { DeepRequired } from 'utility-types';

import { AvegrageTopKeywordSearchImpressionsDto, GetAverageTopKeywordSearchImpressionsBodyDto } from '@malou-io/package-dto';
import {
    getMonthYearFromMalouComparisonPeriod,
    KeywordSearchImpressionsTypeExtended,
    MalouComparisonPeriod,
    MalouErrorCode,
    MonthYearPeriod,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import KeywordSearchImpressionsRepository from ':modules/keyword-search-impressions/repositories/keyword-search-impressions.repository';
import { RestaurantKeywordsRepository } from ':modules/restaurant-keywords/restaurant-keywords.repository';

@singleton()
export class GetAvegrageTopKeywordSearchImpressionsUseCase {
    constructor(
        private readonly _keywordSearchImpressionsRepository: KeywordSearchImpressionsRepository,
        private readonly _restaurantKeywordsRepository: RestaurantKeywordsRepository
    ) {}

    async execute(reqBody: DeepRequired<GetAverageTopKeywordSearchImpressionsBodyDto>): Promise<AvegrageTopKeywordSearchImpressionsDto> {
        const { restaurantIds, monthYearPeriod, limit, comparisonPeriod } = reqBody;

        const period = this._getPeriod({ monthYearPeriod, comparisonPeriod });

        const topKeywordSearchImpressions = await this._keywordSearchImpressionsRepository.getAverageTopKeywordSearchImpressions({
            restaurantIds,
            monthYearPeriod: period,
            limit,
        });

        const malouSelectedTopKeywordSearchImpressions = await this._getMalouSelectedTopKeywordSearchImpressions({
            restaurantIds,
            monthYearPeriod: period,
            limit,
        });

        return {
            ...topKeywordSearchImpressions,
            [KeywordSearchImpressionsTypeExtended.MALOU_SELECTED]: malouSelectedTopKeywordSearchImpressions,
        };
    }

    private async _getMalouSelectedTopKeywordSearchImpressions({
        restaurantIds,
        monthYearPeriod,
        limit,
    }: {
        restaurantIds: string[];
        monthYearPeriod: MonthYearPeriod;
        limit: number;
    }): Promise<AvegrageTopKeywordSearchImpressionsDto['malouSelected']> {
        const selectedRestaurantKeywords =
            await this._restaurantKeywordsRepository.findSelectedRestaurantKeywordsByRestaurantIds(restaurantIds);

        const mappedKeywords = selectedRestaurantKeywords
            .map((restaurantKeyword) => ({
                keywordSearch: restaurantKeyword.keyword.text,
                restaurantId: restaurantKeyword.restaurantId,
                impressions:
                    restaurantKeyword.impressionsHistory?.filter(
                        (impression) =>
                            DateTime.fromISO(impression.date.toISOString()).startOf('month') >=
                                DateTime.fromObject(monthYearPeriod.startMonthYear).startOf('month') &&
                            DateTime.fromISO(impression.date.toISOString()).startOf('month') <=
                                DateTime.fromObject(monthYearPeriod.endMonthYear).startOf('month')
                    ) ?? [],
            }))
            .filter((restaurantKeyword) => restaurantKeyword.impressions?.length > 0);

        const keywordsGroupedByText = groupBy(mappedKeywords, 'keywordSearch');

        const malouSelectedTopKeywordSearchImpressions = Object.entries(keywordsGroupedByText)
            .map(([keywordSearch, keywords]) => {
                const keywordsGroupedByRestaurant = groupBy(keywords, 'restaurantId');

                const restaurants = Object.entries(keywordsGroupedByRestaurant)
                    .map(([restaurantId, restaurantKeywords]) => {
                        const restaurantImpressions = restaurantKeywords.map((restaurantKeyword) => restaurantKeyword.impressions).flat();
                        const sum = sumBy(restaurantImpressions, 'value');
                        const avgValuePerRestaurant = sum / restaurantImpressions.length;

                        return {
                            restaurantId,
                            avgValuePerRestaurant: Math.round(avgValuePerRestaurant),
                        };
                    })
                    .sort((a, b) => b.avgValuePerRestaurant - a.avgValuePerRestaurant);

                const avgValue = sumBy(restaurants, 'avgValuePerRestaurant') / restaurants.length;
                return { keywordSearch, avgValue: Math.round(avgValue), restaurants };
            })
            .sort((a, b) => b.avgValue - a.avgValue);

        return malouSelectedTopKeywordSearchImpressions.slice(0, limit);
    }

    private _getPeriod({
        monthYearPeriod,
        comparisonPeriod,
    }: {
        monthYearPeriod: MonthYearPeriod;
        comparisonPeriod?: MalouComparisonPeriod;
    }): MonthYearPeriod {
        if (!comparisonPeriod) {
            return monthYearPeriod;
        }

        if (comparisonPeriod !== MalouComparisonPeriod.PREVIOUS_PERIOD) {
            throw new MalouError(MalouErrorCode.INVALID_COMPARISON_PERIOD, {
                message: 'Comparison period not implemented',
                metadata: { comparisonPeriod },
            });
        }
        const period = getMonthYearFromMalouComparisonPeriod({
            dateFilters: {
                startMonthYear: monthYearPeriod.startMonthYear,
                endMonthYear: monthYearPeriod.endMonthYear,
            },
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        });

        if (!period) {
            throw new MalouError(MalouErrorCode.INVALID_DATE_RANGE, {
                message: 'Invalid date range for previous period comparison',
                metadata: { monthYearPeriod, comparisonPeriod },
            });
        }

        return period;
    }
}
