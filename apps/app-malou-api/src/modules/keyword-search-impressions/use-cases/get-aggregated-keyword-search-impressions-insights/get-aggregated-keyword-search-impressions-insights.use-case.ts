import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';
import { DeepRequired } from 'utility-types';

import { AggregatedKeywordSearchImpressionsInsightsDto, GetAggregatedKeywordSearchImpressionsInsightsBodyDto } from '@malou-io/package-dto';
import {
    AggregationType,
    getMonthYearFromMalouComparisonPeriod,
    MalouComparisonPeriod,
    MalouErrorCode,
    MonthYearPeriod,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import KeywordSearchImpressionsRepository from ':modules/keyword-search-impressions/repositories/keyword-search-impressions.repository';

@singleton()
export class GetAggregatedKeywordSearchImpressionsInsightsUseCase {
    readonly _SUPPORTED_AGGREGATION_TYPES = [AggregationType.TOTAL];
    constructor(private readonly _keywordSearchImpressionsRepository: KeywordSearchImpressionsRepository) {}

    async execute<T extends AggregationType>(
        reqBody: DeepRequired<GetAggregatedKeywordSearchImpressionsInsightsBodyDto>
    ): Promise<AggregatedKeywordSearchImpressionsInsightsDto<T>> {
        const { restaurantIds, monthYearPeriod, aggregationType, comparisonPeriod } = reqBody;

        assert(this._SUPPORTED_AGGREGATION_TYPES.includes(aggregationType), `Aggregation type ${aggregationType} is not supported`);

        const period = this._getPeriod({ monthYearPeriod, comparisonPeriod });

        return this._getTotal<T>(restaurantIds, period);
    }

    private async _getTotal<T extends AggregationType>(
        restaurantIds: string[],
        monthYearPeriod: MonthYearPeriod
    ): Promise<AggregatedKeywordSearchImpressionsInsightsDto<T>> {
        const result = await this._keywordSearchImpressionsRepository.getAggregatedKeywordSearchImpressionsTotal({
            restaurantIds,
            monthYearPeriod,
        });

        return restaurantIds.reduce((acc, restaurantId) => {
            acc[restaurantId] = { [AggregationType.TOTAL]: result[restaurantId]?.[AggregationType.TOTAL] ?? null } as any;
            return acc;
        }, {} as AggregatedKeywordSearchImpressionsInsightsDto<T>);
    }

    private _getPeriod({
        monthYearPeriod,
        comparisonPeriod,
    }: {
        monthYearPeriod: MonthYearPeriod;
        comparisonPeriod?: MalouComparisonPeriod;
    }): MonthYearPeriod {
        if (!comparisonPeriod) {
            return monthYearPeriod;
        }

        if (comparisonPeriod !== MalouComparisonPeriod.PREVIOUS_PERIOD) {
            throw new MalouError(MalouErrorCode.INVALID_COMPARISON_PERIOD, {
                message: 'Comparison period not implemented',
                metadata: { comparisonPeriod },
            });
        }
        const period = getMonthYearFromMalouComparisonPeriod({
            dateFilters: {
                startMonthYear: monthYearPeriod.startMonthYear,
                endMonthYear: monthYearPeriod.endMonthYear,
            },
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        });

        if (!period) {
            throw new MalouError(MalouErrorCode.INVALID_DATE_RANGE, {
                message: 'Invalid date range for previous period comparison',
                metadata: { monthYearPeriod, comparisonPeriod },
            });
        }

        return period;
    }
}
