import { singleton } from 'tsyringe';
import { DeepRequired } from 'utility-types';

import { GetKeywordSearchImpressionsInsightsBodyDto, KeywordSearchImpressionsInsightsDto } from '@malou-io/package-dto';
import { getMonthYearFromMalouComparisonPeriod, MalouComparisonPeriod, MalouErrorCode, MonthAndYear } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import KeywordSearchImpressionsRepository from ':modules/keyword-search-impressions/repositories/keyword-search-impressions.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class GetKeywordSearchImpressionsInsightsUseCase {
    constructor(
        private readonly _keywordSearchImpressionsRepository: KeywordSearchImpressionsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute(
        params: DeepRequired<GetKeywordSearchImpressionsInsightsBodyDto> & { restaurantId: string }
    ): Promise<KeywordSearchImpressionsInsightsDto[]> {
        const { restaurantId, startMonthYear, endMonthYear, comparisonPeriod } = params;
        const restaurantStartDate = await this._restaurantsRepository.getRestaurantsCreatedAtByIds([restaurantId]);
        const period = this._getDateRange({
            startMonthYear,
            endMonthYear,
            comparisonPeriod,
            restaurantCratedAt: restaurantStartDate[restaurantId],
        });
        return this._keywordSearchImpressionsRepository.getRestaurantInsights({ restaurantId, period });
    }

    private _getDateRange({
        startMonthYear,
        endMonthYear,
        comparisonPeriod,
        restaurantCratedAt,
    }: {
        startMonthYear: MonthAndYear;
        endMonthYear: MonthAndYear;
        comparisonPeriod?: MalouComparisonPeriod;
        restaurantCratedAt: Date;
    }): { startMonthYear: MonthAndYear; endMonthYear: MonthAndYear } {
        if (!comparisonPeriod) {
            return { startMonthYear, endMonthYear };
        }

        const period = getMonthYearFromMalouComparisonPeriod({
            dateFilters: {
                startMonthYear,
                endMonthYear,
            },
            restaurantStartDate: restaurantCratedAt,
            comparisonPeriod,
        });

        if (!period) {
            throw new MalouError(MalouErrorCode.INVALID_DATE_RANGE, {
                message: 'Invalid date range for previous period comparison',
                metadata: { startMonthYear, endMonthYear, comparisonPeriod },
            });
        }

        return period;
    }
}
