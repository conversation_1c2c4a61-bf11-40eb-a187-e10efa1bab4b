import { DateTime } from 'luxon';
import { Aggregate, PipelineStage } from 'mongoose';
import { singleton } from 'tsyringe';

import {
    EntityRepository,
    IClient,
    IPrivateReview,
    ITranslations,
    PopulateBuilderHelper,
    PrivateReviewModel,
    ReadPreferenceMode,
    toDbId,
} from '@malou-io/package-models';
import { FilterType, JobStatus, PlatformKey, PostedStatus } from '@malou-io/package-utils';

import { Config } from ':config';
import { BasicFilters } from ':helpers/filters/basic-filters';
import { PrivateReviewWithTranslations } from ':modules/private-reviews/private-reviews.interface';

@singleton()
export class PrivateReviewsRepository extends EntityRepository<IPrivateReview> {
    constructor() {
        super(PrivateReviewModel);
    }

    getPrivateReviewById(reviewId: string): Promise<IPrivateReview | null> {
        return this.findOne({
            filter: { _id: toDbId(reviewId) },
            options: { lean: true },
        });
    }

    async getPrivateReviewByIdWithTranslation(
        reviewId: string
    ): Promise<PopulateBuilderHelper<IPrivateReview, [{ path: 'client' }, { path: 'translations' }]> | null> {
        const privateReview = await this.findOne({
            filter: { _id: toDbId(reviewId) },
            options: { populate: [{ path: 'client' }, { path: 'translations' }], lean: true },
        });
        return privateReview ? this.toEntity(privateReview) : null;
    }

    async setTranslationsToPrivateReview(reviewId: string, translationsId: string): Promise<PrivateReviewWithTranslations | null> {
        const privateReview = await this.findOneAndUpdate({
            filter: { _id: toDbId(reviewId) },
            update: { translationsId: toDbId(translationsId) },
            options: { populate: [{ path: 'client' }, { path: 'translations' }], new: true, lean: true },
        });
        return privateReview ? this.toEntity(privateReview) : null;
    }

    // TODO: delete this function when feature toggle 'release-new-semantic-analysis' is removed
    getPrivateReviewsToBeSemanticallyAnalyzed(): Aggregate<any[]> {
        return this.aggregate(
            [
                {
                    $match: {
                        socialCreatedAt: { $gte: DateTime.local().minus({ days: 4 }).toJSDate() },
                        text: { $nin: [null, ''] },
                    },
                },
                ...this.getSemanticAnalysisStages(),
                {
                    $match: {
                        $or: [
                            { semanticAnalysis: null },
                            {
                                'semanticAnalysis.status': JobStatus.FAILED,
                                'semanticAnalysis.failCount': { $lte: 5 },
                            },
                        ],
                    },
                },
                {
                    $sort: {
                        'semanticAnalysis.failCount': 1,
                    },
                },
                { $limit: Config.services.openai.requestsPerMinuteLimit },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getPrivateReviewsToBeSemanticallyAnalyzed',
            }
        );
    }

    getSemanticAnalysisStages(
        isSemanticAnalysisFeatureEnabled: boolean = false
    ): Exclude<PipelineStage, PipelineStage.Merge | PipelineStage.Out>[] {
        if (isSemanticAnalysisFeatureEnabled) {
            return [
                {
                    $lookup: {
                        from: 'segmentanalyses',
                        let: {
                            key: '$key',
                            id: { $toString: '$_id' },
                        },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [{ $eq: ['$platformKey', '$$key'] }, { $eq: ['$reviewSocialId', '$$id'] }],
                                    },
                                },
                            },
                            {
                                $lookup: {
                                    from: 'segmentanalysisparenttopics',
                                    localField: 'segmentAnalysisParentTopicIds',
                                    foreignField: '_id',
                                    as: 'segmentAnalysisParentTopics',
                                    pipeline: [
                                        {
                                            $lookup: {
                                                from: 'translations',
                                                localField: 'translationsId',
                                                foreignField: '_id',
                                                as: 'translation',
                                            },
                                        },
                                        {
                                            $addFields: {
                                                translations: { $arrayElemAt: ['$translation', 0] },
                                            },
                                        },
                                    ],
                                },
                            },
                        ],
                        as: 'semanticAnalysisSegments',
                    },
                },
            ];
        }
        return [
            {
                $lookup: {
                    from: 'reviewanalyses',
                    let: {
                        id: { $toString: '$_id' },
                    },
                    pipeline: [
                        {
                            $match: {
                                platformKey: PlatformKey.PRIVATE,
                                reviewSocialId: '$$id',
                            },
                        },
                    ],
                    as: 'semanticAnalysis',
                },
            },
            {
                $set: {
                    semanticAnalysis: { $arrayElemAt: ['$semanticAnalysis', 0] },
                },
            },
        ];
    }

    getTranslationsStages(): Exclude<PipelineStage, PipelineStage.Merge | PipelineStage.Out>[] {
        return [
            {
                $lookup: {
                    from: 'translations',
                    localField: 'translationsId',
                    foreignField: '_id',
                    as: 'translations',
                },
            },
            {
                $unwind: {
                    path: '$translations',
                    preserveNullAndEmptyArrays: true,
                },
            },
        ];
    }

    getUnansweredPrivateReviews(filters: BasicFilters): Promise<IPrivateReview[]> {
        const searchQuery = filters.buildQuery({ filterType: FilterType.REVIEWS });
        searchQuery.$and.push({
            $or: [{ 'comments.0.posted': { $in: [PostedStatus.PENDING, PostedStatus.RETRY] } }, { comments: { $in: [[], null] } }],
        });

        return this.find({
            filter: searchQuery,
            options: {
                lean: true,
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getUnansweredPrivateReviews',
            },
        });
    }

    toEntity(privateReview: IPrivateReview & { client: IClient; translations: ITranslations }): PrivateReviewWithTranslations {
        return {
            _id: privateReview._id,
            lang: privateReview.lang,
            campaignId: privateReview.campaignId,
            clientId: privateReview.clientId,
            scanId: privateReview.scanId,
            semanticAnalysisFetchStatus: privateReview.semanticAnalysisFetchStatus,
            translationsId: privateReview.translationsId,
            restaurantId: privateReview.restaurantId,
            key: privateReview.key,
            text: privateReview.text,
            socialCreatedAt: privateReview.socialCreatedAt,
            socialSortDate: privateReview.socialSortDate,
            rating: privateReview.rating,
            archived: privateReview.archived,
            comments: privateReview.comments,
            createdAt: privateReview.createdAt,
            updatedAt: privateReview.updatedAt,
            client: privateReview.client,
            translations: privateReview.translations,
            publicBusinessId: privateReview.publicBusinessId,
        };
    }
}
