import { FilterQuery } from 'mongoose';
import { singleton } from 'tsyringe';

import { IPrivateReview } from '@malou-io/package-models';
import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { ReviewFilters } from ':helpers/filters/review-filters';
import { PrivateReviewsSearchFilters } from ':modules/private-reviews/private-reviews.interface';
import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';

@singleton()
export default class PrivateReviewsUseCases {
    constructor(private _privateReviewsRepository: PrivateReviewsRepository) {}

    async searchPrivateReview(searchFilters: PrivateReviewsSearchFilters): Promise<IPrivateReview[]> {
        const andFilters: FilterQuery<IPrivateReview>[] = [];
        if (searchFilters.scanIds) {
            andFilters.push({ scanId: { $in: searchFilters.scanIds } });
        }
        if (!andFilters.length) {
            throw new MalouError(MalouErrorCode.NO_FILTER_PROVIDED, { message: 'No filters provided for private-reviews search' });
        }
        const privateReviews = await this._privateReviewsRepository.find({
            filter: { $and: andFilters },
            options: {
                lean: true,
            },
        });

        return privateReviews;
    }

    async getUnansweredReviewCount(filters: ReviewFilters): Promise<{ count: number }> {
        const reviews = await this._privateReviewsRepository.getUnansweredPrivateReviews(filters);
        const reviewsThatCanBeAnswered = reviews.filter((review) => !!review.campaignId);
        return { count: reviewsThatCanBeAnswered.length };
    }
}
