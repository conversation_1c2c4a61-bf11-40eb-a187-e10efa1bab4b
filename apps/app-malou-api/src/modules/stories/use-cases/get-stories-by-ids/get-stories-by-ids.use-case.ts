import { singleton } from 'tsyringe';

import { StoryItemDto } from '@malou-io/package-dto';

import { StoriesRepository } from ':modules/stories/repository/stories.repository';

@singleton()
export class GetStoriesByIdsUseCase {
    constructor(private readonly _storiesRepository: StoriesRepository) {}

    async execute(storyIds: string[]): Promise<StoryItemDto[]> {
        const stories = await this._storiesRepository.getStoriesByIds(storyIds);
        return stories.map((story) => story.toDto());
    }
}
