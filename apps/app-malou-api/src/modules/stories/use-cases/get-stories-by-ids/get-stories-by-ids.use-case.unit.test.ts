import { container } from 'tsyringe';

import { StoryItemDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { PlatformKey, PostPublicationStatus, PostSource } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GetStoriesByIdsUseCase } from ':modules/stories/use-cases/get-stories-by-ids/get-stories-by-ids.use-case';

describe('GetStoriesByIdsUseCase', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'PostsRepository']);
    });

    it('should return stories by their IDs', async () => {
        const getStoriesByIdsUseCase = container.resolve(GetStoriesByIdsUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .isStory(true)
                                .key(PlatformKey.INSTAGRAM)
                                .keys([PlatformKey.INSTAGRAM])
                                .published(PostPublicationStatus.DRAFT)
                                .attachments([])
                                .socialAttachments([])
                                .hashtags(undefined)
                                .plannedPublicationDate(new Date())
                                .feedbackId(null)
                                .location(null)
                                .author({
                                    _id: newDbId(),
                                    name: 'John',
                                    lastname: 'Doe',
                                    picture: 'picture.jpg',
                                })
                                .build(),
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .isStory(true)
                                .key(PlatformKey.FACEBOOK)
                                .keys([PlatformKey.FACEBOOK])
                                .published(PostPublicationStatus.PENDING)
                                .attachments([])
                                .socialAttachments([])
                                .hashtags(undefined)
                                .plannedPublicationDate(new Date())
                                .feedbackId(null)
                                .location(null)
                                .author({
                                    _id: newDbId(),
                                    name: 'Jane',
                                    lastname: 'Smith',
                                    picture: null,
                                })
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: (dependencies): StoryItemDto[] => {
                const story1 = dependencies.posts[0];
                const story2 = dependencies.posts[1];
                return [
                    {
                        id: story1._id.toString(),
                        platformKeys: [PlatformKey.INSTAGRAM],
                        published: PostPublicationStatus.DRAFT,
                        isPublishing: false,
                        plannedPublicationDate: story1.plannedPublicationDate?.toISOString() ?? null,
                        media: null,
                        feedbackMessageCount: 0,
                        author: {
                            id: story1.author!._id.toString(),
                            name: story1.author!.name,
                            lastname: story1.author!.lastname,
                            picture: story1.author!.picture,
                        },
                        bindingId: story1.bindingId,
                        socialCreatedAt: story1.socialCreatedAt?.toISOString(),
                        sortDate: story1.sortDate?.toISOString(),
                        socialLink: story1.socialLink,
                        createdFromDeviceType: story1.createdFromDeviceType,
                        mostRecentPublicationErrorCode: undefined,
                    },
                    {
                        id: story2._id.toString(),
                        platformKeys: [PlatformKey.FACEBOOK],
                        published: PostPublicationStatus.PENDING,
                        isPublishing: false,
                        plannedPublicationDate: story2.plannedPublicationDate?.toISOString() ?? null,
                        media: null,
                        feedbackMessageCount: 0,
                        author: {
                            id: story2.author!._id.toString(),
                            name: story2.author!.name,
                            lastname: story2.author!.lastname,
                            picture: story2.author!.picture ?? undefined,
                        },
                        bindingId: story2.bindingId,
                        socialCreatedAt: story2.socialCreatedAt?.toISOString(),
                        sortDate: story2.sortDate?.toISOString(),
                        socialLink: story2.socialLink,
                        createdFromDeviceType: story2.createdFromDeviceType,
                        mostRecentPublicationErrorCode: undefined,
                    },
                ];
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();

        const storyIds = [seededObjects.posts[0]._id.toString(), seededObjects.posts[1]._id.toString()];

        const stories = await getStoriesByIdsUseCase.execute(storyIds);

        expect(stories).toEqual(expectedResult);
    });

    it('should return empty array when no story IDs are provided', async () => {
        const getStoriesByIdsUseCase = container.resolve(GetStoriesByIdsUseCase);

        const stories = await getStoriesByIdsUseCase.execute([]);

        expect(stories).toEqual([]);
    });

    it('should return empty array when stories with provided IDs do not exist', async () => {
        const getStoriesByIdsUseCase = container.resolve(GetStoriesByIdsUseCase);

        const nonExistentIds = [newDbId().toString(), newDbId().toString()];

        const stories = await getStoriesByIdsUseCase.execute(nonExistentIds);

        expect(stories).toEqual([]);
    });

    it('should only return stories (not regular posts) when filtering by IDs', async () => {
        const getStoriesByIdsUseCase = container.resolve(GetStoriesByIdsUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            // This is a story
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .isStory(true)
                                .key(PlatformKey.INSTAGRAM)
                                .keys([PlatformKey.INSTAGRAM])
                                .published(PostPublicationStatus.DRAFT)
                                .build(),
                            // This is a regular post (not a story)
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .isStory(false)
                                .key(PlatformKey.FACEBOOK)
                                .keys([PlatformKey.FACEBOOK])
                                .published(PostPublicationStatus.DRAFT)
                                .build(),
                        ];
                    },
                },
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const storyId = seededObjects.posts[0]._id.toString();
        const regularPostId = seededObjects.posts[1]._id.toString();

        // Request both story and regular post IDs
        const stories = await getStoriesByIdsUseCase.execute([storyId, regularPostId]);

        // Should only return the story, not the regular post
        expect(stories).toHaveLength(1);
        expect(stories[0].id).toBe(storyId);
    });

    it('should return partial results when some IDs exist and others do not', async () => {
        const getStoriesByIdsUseCase = container.resolve(GetStoriesByIdsUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .isStory(true)
                                .key(PlatformKey.INSTAGRAM)
                                .keys([PlatformKey.INSTAGRAM])
                                .published(PostPublicationStatus.DRAFT)
                                .build(),
                        ];
                    },
                },
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const existingStoryId = seededObjects.posts[0]._id.toString();
        const nonExistentId = newDbId().toString();

        const stories = await getStoriesByIdsUseCase.execute([existingStoryId, nonExistentId]);

        // Should only return the existing story
        expect(stories).toHaveLength(1);
        expect(stories[0].id).toBe(existingStoryId);
    });
});
