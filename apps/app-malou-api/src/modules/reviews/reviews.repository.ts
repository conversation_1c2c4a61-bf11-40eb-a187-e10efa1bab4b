import { DateTime } from 'luxon';
import { Aggregate, FilterQuery, PipelineStage } from 'mongoose';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { SimpleRestaurant } from '@malou-io/package-dto';
import {
    EntityRepository,
    ID,
    IReview,
    IReviewComment,
    IReviewWithSemanticAnalysis,
    MalouProjection,
    ReadPreferenceMode,
    ReviewModel,
    toDbId,
    toDbIds,
} from '@malou-io/package-models';
import {
    FilterType,
    JobStatus,
    MalouErrorCode,
    NEGATIVE_REVIEW_MAX_DAYS_NOTIFICATION,
    NEGATIVE_REVIEW_MAX_RATING,
    NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION,
    PlatformKey,
    PlatformPresenceStatus,
    PostedStatus,
    ReportType,
    UBEREATS_DAYS_UNTIL_CANT_BE_ANSWERED,
} from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { AdvancedReviewFilters, ReviewFiltersMode } from ':helpers/filters/advanced-review-filters';
import { BasicFilters } from ':helpers/filters/basic-filters';
import { ReviewFilters } from ':helpers/filters/review-filters';
import { DEFAULT_REVIEW_PAGE_SIZE, ReviewPagination } from ':helpers/pagination';
import { PrivateReviewWithScanWithNfc } from ':modules/private-reviews/private-reviews.interface';
import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';
import { MAX_TRIPADVISOR_REPLY_RETRIES } from ':modules/reviews/platforms/tripadvisor/use-cases';
import {
    RestaurantReviewsRatingStats,
    ReviewFiltersInput,
    ReviewWithSemanticAnalysis,
    ReviewWithTranslations,
} from ':modules/reviews/reviews.interfaces';
import {
    isFeatureAvailableForRestaurant,
    isFeatureAvailableForRestaurants,
} from ':services/experimentations-service/experimentation.service';
import { metricsService } from ':services/metrics.service';

const getRestaurantReviewsPaginatedV2DurationHistogram = metricsService
    .getMeter()
    .createHistogram('reviews.getRestaurantReviewsPaginatedV2Duration', {
        description: 'Duration of getRestaurantReviewsPaginated',
        advice: { explicitBucketBoundaries: metricsService.getDefaultBucketBoundaries() },
        unit: 'seconds',
    });

@singleton()
export class ReviewsRepository extends EntityRepository<IReview> {
    // We do not delete reviews that are removed from their platform, we just mark them as not found
    // It is important to always filter them out when we do not want to include them in the results (= always)
    private readonly _reviewsNotDeletedOnTheirPlatformFilter = { platformPresenceStatus: { $ne: PlatformPresenceStatus.NOT_FOUND } };

    constructor(private readonly _privateReviewsRepository: PrivateReviewsRepository) {
        super(ReviewModel);
    }

    async getReviewById(reviewId: string): Promise<IReview | null> {
        return this.findOne({
            filter: { _id: toDbId(reviewId) },
            options: { lean: true, comment: 'getReviewById' },
        });
    }

    getReviewsByIds(reviewIds: string[], options?: { projection?: MalouProjection<IReview> }): Promise<IReview[]> {
        return this.find({
            filter: { _id: { $in: toDbIds(reviewIds) } },
            projection: options?.projection,
            options: { lean: true, comment: 'getReviewsByIds' },
        });
    }

    async getReviewByIdWithTranslation(reviewId: string): Promise<ReviewWithTranslations | null> {
        return this.findOne({
            filter: { _id: toDbId(reviewId) },
            options: { populate: [{ path: 'translations' }], lean: true, comment: 'getReviewByIdWithTranslation' },
        }) as Promise<ReviewWithTranslations | null>;
    }

    async deleteReviewById(reviewId: string): Promise<void> {
        await this.deleteOne({
            filter: { _id: toDbId(reviewId) },
        });
    }

    async setTranslationsToReview(reviewId: string, translationsId: string): Promise<ReviewWithTranslations | null> {
        return this.findOneAndUpdateOrFail({
            filter: { _id: toDbId(reviewId) },
            update: { translationsId: toDbId(translationsId) },
            options: { populate: [{ path: 'translations' }], new: true, lean: true, comment: 'setTranslationsToReview' },
        });
    }

    async updateReviewKeywordsLang(reviewId: string, keywordsLang: string): Promise<ReviewWithSemanticAnalysis> {
        const review = await this.findOneAndUpdate({
            filter: { _id: toDbId(reviewId) },
            update: { keywordsLang },
            projection: { restaurantId: 1 },
            options: { lean: true },
        });

        const isSemanticAnalysisFeatureEnabled = await isFeatureAvailableForRestaurant({
            featureName: 'release-new-semantic-analysis',
            restaurantId: review?.restaurantId?.toString() ?? '',
        });

        const pipeline = [
            {
                $match: { _id: toDbId(reviewId) },
            },
            ...this._getTranslationsStages(),
            ...this._getSemanticAnalysisStages(isSemanticAnalysisFeatureEnabled),
            ...this._getAiRelevantBricksStages(),
        ];

        const result = await this.aggregate(pipeline, { comment: 'updateReviewKeywordsLang' });
        return result[0];
    }

    getRestaurantsReviewsAverageRating(filters: ReviewFilters): any {
        const filtersQuery = filters.buildQuery({ filterType: FilterType.REVIEWS });
        return this.model.aggregate(
            [
                {
                    $match: filtersQuery,
                },
                {
                    $group: {
                        _id: {
                            restaurantId: '$restaurantId',
                            key: '$key',
                        },
                        averageRatingPerPlatform: {
                            $avg: '$rating',
                        },
                        allRatingsPerPlatform: {
                            $push: '$rating',
                        },
                    },
                },
                {
                    $group: {
                        _id: '$_id.restaurantId',
                        platforms: {
                            $push: {
                                platformKey: '$_id.key',
                                averageRating: '$averageRatingPerPlatform',
                            },
                        },
                        allRestaurantRatings: {
                            $push: '$allRatingsPerPlatform',
                        },
                    },
                },
                {
                    $addFields: {
                        concatRestaurantRating: {
                            $reduce: {
                                input: '$allRestaurantRatings',
                                initialValue: [],
                                in: { $concatArrays: ['$$value', '$$this'] },
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        restaurantId: '$_id',
                        averageRating: {
                            $avg: '$concatRestaurantRating',
                        },
                        averageRatingPerPlatform: '$platforms',
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getRestaurantsReviewsAverageRating',
            }
        );
    }

    async getAverageResponseTimeInMillisecondsByRestaurantId({
        restaurantId,
        startDate,
        endDate,
    }: {
        restaurantId: string;
        startDate: Date;
        endDate: Date;
    }): Promise<number> {
        const result = await this.aggregate(
            [
                {
                    $match: {
                        restaurantId: toDbId(restaurantId),
                        socialCreatedAt: { $gte: startDate, $lte: endDate },
                        $and: [{ comments: { $ne: [] } }, { comments: { $ne: null } }],
                        'comments.0.posted': PostedStatus.POSTED,
                        'comments.0.socialUpdatedAt': { $exists: true },
                        ...this._reviewsNotDeletedOnTheirPlatformFilter,
                    },
                },
                {
                    $addFields: {
                        responseTime: { $subtract: [{ $arrayElemAt: ['$comments.socialUpdatedAt', 0] }, '$socialCreatedAt'] },
                    },
                },
                {
                    $group: {
                        _id: null,
                        averageResponseTime: { $avg: '$responseTime' },
                    },
                },
            ],
            { comment: 'getAverageResponseTimeInMillisecondsByRestaurantId' }
        );
        return result[0]?.averageResponseTime ?? null;
    }

    async getLatestNotAnsweredPositiveReviewsByRestaurantId({
        restaurantId,
        limit,
        minDate,
        reviewMinRating = 3,
    }: {
        restaurantId: string;
        limit: number;
        minDate: Date;
        reviewMinRating?: number;
    }): Promise<IReview[]> {
        return this.find({
            filter: {
                restaurantId: toDbId(restaurantId),
                socialCreatedAt: { $gte: minDate },
                rating: { $gte: reviewMinRating },
                'comments.0': {
                    $exists: false,
                },
                platformPresenceStatus: { $ne: PlatformPresenceStatus.NOT_FOUND },
            },
            options: { lean: true, limit, comment: 'getLatestNotAnsweredPositiveReviewsByRestaurantId' },
        });
    }

    async getAnsweredAndNotAnsweredReviewCountByRestaurantId({
        restaurantId,
        startDate,
        endDate,
        platformKey,
    }: {
        restaurantId: string;
        startDate: Date;
        endDate: Date;
        platformKey?: PlatformKey;
    }): Promise<{ answered: number; notAnswered: number }> {
        const dateFilter = { $gte: startDate, $lte: endDate };
        const dateCondition = {
            $or: [{ socialCreatedAt: dateFilter }, { socialUpdatedAt: { $ne: null, $exists: true, ...dateFilter } }],
        };

        const filter = platformKey ? { key: platformKey, ...dateCondition } : dateCondition;

        const answeredAggregation = await this.aggregate(
            [
                {
                    $match: {
                        restaurantId: toDbId(restaurantId),
                        'comments.0.posted': PostedStatus.POSTED,
                        ...filter,
                        ...this._reviewsNotDeletedOnTheirPlatformFilter,
                    },
                },
                {
                    $count: 'answered',
                },
            ],
            { comment: 'getAnsweredAndNotAnsweredReviewCountByRestaurantId - answered' }
        );

        const isReviewNotAnsweredBasedOnPlatformKey = {
            $or: [
                {
                    key: PlatformKey.LAFOURCHETTE,
                    text: { $ne: null },
                },
                {
                    key: PlatformKey.DELIVEROO,
                    text: { $ne: null },
                },
                {
                    key: {
                        $nin: [
                            PlatformKey.LAFOURCHETTE,
                            PlatformKey.FOURSQUARE,
                            PlatformKey.DELIVEROO,
                            PlatformKey.RESY,
                            PlatformKey.SEVENROOMS,
                        ],
                    },
                },
            ],
        };

        const notAnsweredFilter = {
            ...(platformKey && { key: platformKey }),
            $and: [dateCondition, isReviewNotAnsweredBasedOnPlatformKey],
        };

        const notAnsweredAggregation = await this.aggregate(
            [
                {
                    $match: {
                        restaurantId: toDbId(restaurantId),
                        ...notAnsweredFilter,
                        'comments.0': {
                            $exists: false,
                        },
                        ...this._reviewsNotDeletedOnTheirPlatformFilter,
                    },
                },
                {
                    $count: 'notAnswered',
                },
            ],
            { comment: 'getAnsweredAndNotAnsweredReviewCountByRestaurantId - not answered' }
        );

        return {
            answered: answeredAggregation[0]?.answered ?? 0,
            notAnswered: notAnsweredAggregation[0]?.notAnswered ?? 0,
        };
    }

    getRestaurantsReviewsRating(filters: ReviewFilters): any {
        const filtersQuery = filters.buildQuery({ filterType: FilterType.REVIEWS });
        return this.aggregate(
            [
                {
                    $match: { ...filtersQuery, ...this._reviewsNotDeletedOnTheirPlatformFilter },
                },
                {
                    $addFields: {
                        roundedRating: {
                            $round: ['$rating', 0],
                        },
                    },
                },
                {
                    $group: {
                        _id: {
                            rating: '$roundedRating',
                            key: '$key',
                        },
                        nbReviews: { $sum: 1 },
                    },
                },
                {
                    $group: {
                        _id: '$_id.rating',
                        total: {
                            $sum: '$nbReviews',
                        },
                        platforms: {
                            $push: {
                                key: '$_id.key',
                                nbReviews: '$nbReviews',
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        total: 1,
                        rating: '$_id',
                        platforms: 1,
                    },
                },
                {
                    $sort: {
                        rating: -1,
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getRestaurantsReviewsRating',
            }
        );
    }

    getRestaurantReviewsEvolution(filters: ReviewFilters): any {
        const filtersQuery = filters.buildQuery({ filterType: FilterType.REVIEWS });

        // always use $isoWeek and $isoWeekYear when working with weeks to avoid issues with the beginning and the end of the year
        // exemple : 2024-12-31 is the 1st week of 2025.
        // $year operator will return 2024 but $isoWeekYear will return 2025 which is the correct year for 2024-12-31 date in ISO week date format

        // for more information: https://en.wikipedia.org/wiki/ISO_week_date
        return this.aggregate(
            [
                {
                    $match: { ...filtersQuery, ...this._reviewsNotDeletedOnTheirPlatformFilter },
                },
                {
                    $addFields: {
                        day: { $dayOfYear: '$socialCreatedAt' },
                        week: { $isoWeek: '$socialCreatedAt' },
                        month: { $month: '$socialCreatedAt' },
                        year: { $year: '$socialCreatedAt' },
                        weekYear: { $isoWeekYear: '$socialCreatedAt' },
                    },
                },
                {
                    $facet: {
                        dataPerDay: [
                            {
                                $group: {
                                    _id: {
                                        key: '$key',
                                        year: '$year',
                                        day: '$day',
                                    },
                                    total: { $sum: 1 },
                                },
                            },
                            {
                                $project: {
                                    _id: 0,
                                    key: '$_id.key',
                                    year: '$_id.year',
                                    day: '$_id.day',
                                    total: '$total',
                                },
                            },
                            {
                                $sort: {
                                    year: 1,
                                    day: 1,
                                },
                            },
                        ],
                        dataPerWeek: [
                            {
                                $group: {
                                    _id: {
                                        key: '$key',
                                        year: '$weekYear',
                                        week: '$week',
                                    },
                                    total: { $sum: 1 },
                                },
                            },
                            {
                                $project: {
                                    _id: 0,
                                    key: '$_id.key',
                                    year: '$_id.year',
                                    week: '$_id.week',
                                    total: '$total',
                                },
                            },
                            {
                                $sort: {
                                    year: 1,
                                    week: 1,
                                },
                            },
                        ],
                        dataPerMonth: [
                            {
                                $group: {
                                    _id: {
                                        key: '$key',
                                        year: '$year',
                                        month: '$month',
                                    },
                                    total: { $sum: 1 },
                                },
                            },
                            {
                                $project: {
                                    _id: 0,
                                    key: '$_id.key',
                                    year: '$_id.year',
                                    month: '$_id.month',
                                    total: '$total',
                                },
                            },
                            {
                                $sort: {
                                    year: 1,
                                    month: 1,
                                },
                            },
                        ],
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getRestaurantReviewsEvolution',
            }
        );
    }

    getRestaurantGmbReviewsForPeriod({
        restaurantId,
        startDate,
        endDate,
    }: {
        restaurantId: string;
        startDate: Date;
        endDate: Date;
    }): Promise<IReview[]> {
        const dateFilter = { $gte: startDate, $lte: endDate };
        return this.find({
            filter: {
                key: PlatformKey.GMB,
                restaurantId,
                $or: [{ socialCreatedAt: dateFilter }, { socialUpdatedAt: { $ne: null, $exists: true, ...dateFilter } }],
                ...this._reviewsNotDeletedOnTheirPlatformFilter,
            },
            options: { comment: 'getRestaurantGmbReviewsForPeriod' },
        });
    }

    /**
     * @deprecated Consider using getRestaurantReviewsPaginatedV2 instead. It’s faster.
     */
    async getRestaurantReviewsPaginated({
        pagination: { pageNumber, pageSize, skip },
        filters,
        sort,
        isSemanticAnalysisFeatureEnabled,
    }: {
        pagination: ReviewPagination;
        filters: ReviewFiltersInput;
        sort: { sortBy: string; sortOrder: 1 | -1 };
        isSemanticAnalysisFeatureEnabled: boolean;
    }): Promise<{ data: (ReviewWithSemanticAnalysis | PrivateReviewWithScanWithNfc)[]; metadata: { total: number; page: number }[] }[]> {
        const unionPipeline: PipelineStage[] = [
            { $match: new AdvancedReviewFilters(filters, ReviewFiltersMode.PUBLIC_REVIEWS).buildQuery() },
        ];
        if (filters.showPrivate) {
            unionPipeline.push({
                $unionWith: {
                    coll: 'privatereviews',
                    pipeline: [{ $match: new AdvancedReviewFilters(filters, ReviewFiltersMode.PRIVATE_REVIEWS).buildQuery() }],
                },
            });
        }

        const clientPipeline = [
            {
                $lookup: {
                    from: 'clients',
                    localField: 'clientId',
                    foreignField: '_id',
                    as: 'client',
                },
            },
            {
                $addFields: {
                    client: {
                        $arrayElemAt: ['$client', 0],
                    },
                },
            },
        ];

        const scanPipeline = [
            {
                $lookup: {
                    from: 'scans',
                    let: {
                        scanId: '$scanId',
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$_id', { $toObjectId: '$$scanId' }], // Super mean :( it looks like if $$scanId is not converted to ObjectId, it will not use index on _id
                                },
                            },
                        },
                        {
                            $lookup: {
                                from: 'nfcs',
                                localField: 'nfcId',
                                foreignField: '_id',
                                as: 'nfc',
                            },
                        },
                        {
                            $addFields: {
                                nfc: {
                                    $arrayElemAt: ['$nfc', 0],
                                },
                            },
                        },
                    ],
                    as: 'scan',
                },
            },
            {
                $addFields: {
                    scan: {
                        $arrayElemAt: ['$scan', 0],
                    },
                },
            },
        ];

        const sortPipeline = this._getSortPipeline(sort);

        const pipeline: any = [
            ...unionPipeline,
            {
                $facet: {
                    metadata: [{ $count: 'total' }, { $addFields: { page: pageNumber } }],
                    data: [
                        ...sortPipeline,
                        { $skip: skip },
                        { $limit: pageSize || null },
                        ...this._getSemanticAnalysisStages(isSemanticAnalysisFeatureEnabled),
                        ...this._getTranslationsStages(),
                        ...clientPipeline,
                        ...scanPipeline,
                    ],
                },
            },
        ];

        const result = await this.aggregate<{
            data: (ReviewWithSemanticAnalysis | PrivateReviewWithScanWithNfc)[];
            metadata: { total: number; page: number }[];
        }>(pipeline, { comment: 'getRestaurantReviewsPaginated' });
        return result;
    }

    /**
     * Faster and cheaper than the deprecated getRestaurantReviewsPaginated.
     */
    async getRestaurantReviewsPaginatedV2({
        pagination: { pageSize, skip },
        filters,
        isSemanticAnalysisFeatureEnabled,
    }: {
        pagination: ReviewPagination;
        filters: ReviewFiltersInput;
        isSemanticAnalysisFeatureEnabled: boolean;
    }): Promise<(ReviewWithSemanticAnalysis | PrivateReviewWithScanWithNfc)[]> {
        assert(pageSize);
        skip = skip ?? 0;

        const publicReviewsPipeline: PipelineStage[] = [
            { $match: new AdvancedReviewFilters(filters, ReviewFiltersMode.PUBLIC_REVIEWS).buildQuery() },

            // This $sort stage uses the index { restaurantId: 1, socialSortDate: 1 }
            { $sort: { socialSortDate: -1 } },

            // The index of the last public review that can appear in results is
            // (skip + pageSize - 1). Reviews above this index can’t appear
            // in the results because after this stage:
            //
            //   - no stage is filtering out documents (like $match)
            //
            //   - when private reviews will be merged into this list (with the
            //   $unionWith stage below), it will only push public reviews towards
            //   the end of the list, increasing their indices.
            //
            // This limit is important: the $sort stage at the end of the pipeline
            // (after the merge with private reviews) is performed in-memory, so
            // it won’t be fast and cheap if we have thousand of reviews here.
            // Of course this works only if 'skip' stays relatively small, but in
            // practice people don’t scroll very far on pages that list reviews.
            { $limit: skip + pageSize },

            // specific for public reviews
            ...this._getSemanticAnalysisStages(isSemanticAnalysisFeatureEnabled),
        ];

        const unionPipeline = publicReviewsPipeline;
        if (filters.showPrivate) {
            const clientPipeline = [
                {
                    $lookup: {
                        from: 'clients',
                        localField: 'clientId',
                        foreignField: '_id',
                        as: 'client',
                    },
                },
                {
                    $addFields: {
                        client: {
                            $arrayElemAt: ['$client', 0],
                        },
                    },
                },
            ];

            const scanPipeline = [
                {
                    $lookup: {
                        from: 'scans',
                        let: {
                            scanId: '$scanId',
                        },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        // Super mean :( it looks like if $$scanId is not converted to ObjectId, it will not use index on _id
                                        $eq: ['$_id', { $toObjectId: '$$scanId' }],
                                    },
                                },
                            },
                            {
                                $lookup: {
                                    from: 'nfcs',
                                    localField: 'nfcId',
                                    foreignField: '_id',
                                    as: 'nfc',
                                },
                            },
                            {
                                $addFields: {
                                    nfc: {
                                        $arrayElemAt: ['$nfc', 0],
                                    },
                                },
                            },
                        ],
                        as: 'scan',
                    },
                },
                {
                    $addFields: {
                        scan: {
                            $arrayElemAt: ['$scan', 0],
                        },
                    },
                },
            ];

            const semanticAnalysisPipeline = [
                ...this._privateReviewsRepository.getSemanticAnalysisStages(isSemanticAnalysisFeatureEnabled),
            ];

            unionPipeline.push({
                $unionWith: {
                    coll: 'privatereviews',
                    pipeline: [
                        { $match: new AdvancedReviewFilters(filters, ReviewFiltersMode.PRIVATE_REVIEWS).buildQuery() },
                        { $sort: { socialCreatedAt: -1 } },

                        // see the big comment above for the $limit of public reviews
                        { $limit: skip + pageSize },

                        // private reviews don’t have `socialSortDate` but we need it for the
                        // final stage.
                        {
                            $addFields: {
                                socialSortDate: { $ifNull: ['$socialUpdatedAt', '$socialCreatedAt'] },
                            },
                        },

                        // specific for private reviews
                        ...clientPipeline,
                        ...scanPipeline,
                        ...semanticAnalysisPipeline,
                    ],
                },
            });
        }

        const pipeline: any = [
            ...unionPipeline,

            // it works because we added a socialSortDate field on private reviews
            // so they look like public reviews
            { $sort: { socialSortDate: -1 } },

            { $skip: skip },
            { $limit: pageSize },

            ...this._getTranslationsStages(),
            ...this._getAiRelevantBricksStages(),
        ];

        const beginTimestamp = +new Date();
        try {
            return await this.aggregate<ReviewWithSemanticAnalysis | PrivateReviewWithScanWithNfc>(pipeline, {
                comment: 'getRestaurantReviewsPaginatedV2',
                // It seems that mongodb has some troubles to figure out the best
                // index in this case.
                hint: { restaurantId: 1, socialSortDate: 1 },
            });
        } finally {
            getRestaurantReviewsPaginatedV2DurationHistogram.record((+new Date() - beginTimestamp) / 1000);
        }
    }

    async getLightReviewsWithSegmentAnalyses({
        restaurantId,
        startDate,
        endDate,
        platformKeys,
    }: {
        restaurantId: string;
        startDate: Date;
        endDate: Date;
        platformKeys: PlatformKey[];
    }) {
        const isNewSemanticAnalysisFeatureEnabled = true;
        const reviewsPipeline: PipelineStage[] = [
            {
                $match: {
                    restaurantId: toDbId(restaurantId),
                    socialCreatedAt: { $gte: startDate, $lte: endDate },
                    key: { $in: platformKeys },
                    text: { $ne: null },
                    ...this._reviewsNotDeletedOnTheirPlatformFilter,
                },
            },
            {
                $project: {
                    _id: 1,
                    socialId: 1,
                    text: 1,
                    key: 1,
                    rating: 1,
                    restaurantId: 1,
                    translationsId: 1,
                },
            },
            ...this._getTranslationsStages(),
            ...this._getSemanticAnalysisStages(isNewSemanticAnalysisFeatureEnabled),
        ];

        reviewsPipeline.push({
            $unionWith: {
                coll: 'privatereviews',
                pipeline: [
                    {
                        $match: {
                            restaurantId: toDbId(restaurantId),
                            socialCreatedAt: { $gte: startDate, $lte: endDate },
                            key: { $in: platformKeys },
                            text: { $ne: null },
                        },
                    },
                    ...this._privateReviewsRepository.getTranslationsStages(),
                    ...this._privateReviewsRepository.getSemanticAnalysisStages(isNewSemanticAnalysisFeatureEnabled),
                ],
            },
        });
        return this.aggregate(reviewsPipeline, {
            readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
            comment: 'getLightReviewsWithSegmentAnalyses',
        });
    }

    async pushComment(reviewId: string, comment: IReviewComment): Promise<any> {
        return this.findOneAndUpdate({
            filter: { _id: toDbId(reviewId) },
            update: { $push: { comments: comment } },
            options: { lean: true },
        });
    }

    async pushReviewComment({ socialId, key, comment }: { socialId: string; key: string; comment: IReviewComment }): Promise<any> {
        return this.updateMany({ filter: { socialId, key }, update: { $addToSet: { comments: comment } } });
    }

    async updateUniqueReviewComment({ socialId, key, comment }: { socialId: string; key: string; comment: IReviewComment }): Promise<any> {
        return this.updateMany({ filter: { socialId, key }, update: { comments: [comment] } });
    }

    updateReviewComment({ reviewId, commentId, comment }): any {
        return this.findOne({ filter: { _id: reviewId } }).then((review: any) => {
            review.comments.id(commentId).set(comment);
            return review.save();
        });
    }

    countGmbReviewsForRestaurantAndPeriod(
        restaurantId: string,
        startDate: Date,
        endDate: Date,
        { checkTextNull = true, getUpdatedReviews = false }: { checkTextNull?: boolean; getUpdatedReviews?: boolean } = {}
    ): Promise<number> {
        const extraFilter = checkTextNull ? { text: { $ne: null } } : {};
        const dateFilter = { $gte: startDate, $lte: endDate };
        const fullDateFilter = getUpdatedReviews
            ? { $or: [{ socialCreatedAt: dateFilter }, { socialUpdatedAt: { $ne: null, $exists: true, ...dateFilter } }] }
            : { socialCreatedAt: dateFilter };
        return this.countDocuments({
            filter: {
                restaurantId,
                key: PlatformKey.GMB,
                ...fullDateFilter,
                ...extraFilter,
                ...this._reviewsNotDeletedOnTheirPlatformFilter,
            },
            options: { comment: 'countGmbReviewsForRestaurantAndPeriod' },
        });
    }

    getScoreListOfPostedCommentsByRestaurantId(
        restaurantId: string,
        startDate: Date,
        endDate: Date,
        previousPeriod: boolean
    ): Promise<number[]> {
        const basicFilter = new BasicFilters({
            restaurantId,
            startDate,
            endDate,
            previousPeriod,
        });
        const filter =
            basicFilter.startDate && basicFilter.endDate
                ? { socialUpdatedAt: { $gte: basicFilter.startDate, $lt: basicFilter.endDate } }
                : {};
        return this._getScoreListOfFilteredPostedCommentsByRestaurantId(restaurantId, filter, {});
    }

    getScoreListOfPostedCommentsByRestaurantIdAndPlatformKey(
        restaurantId: string,
        startDate: Date,
        endDate: Date,
        previousPeriod: boolean,
        platformKey: PlatformKey
    ): Promise<number[]> {
        const basicFilter = new BasicFilters({
            restaurantId,
            startDate,
            endDate,
            previousPeriod,
        });
        const filter =
            basicFilter.startDate && basicFilter.endDate
                ? { socialUpdatedAt: { $gte: basicFilter.startDate, $lt: basicFilter.endDate } }
                : {};

        return this._getScoreListOfFilteredPostedCommentsByRestaurantId(restaurantId, filter, {
            key: platformKey,
        });
    }

    // TODO: delete this function when feature toggle 'release-new-semantic-analysis' is removed
    getReviewsToBeSemanticallyAnalyzed(): Aggregate<any[]> {
        return this.aggregate(
            [
                {
                    $match: {
                        socialCreatedAt: { $gte: DateTime.local().minus({ days: 4 }).toJSDate() },
                        text: { $nin: [null, ''] },
                        ...this._reviewsNotDeletedOnTheirPlatformFilter,
                    },
                },
                ...this._getSemanticAnalysisStages(false),
                {
                    $match: {
                        $or: [
                            { semanticAnalysis: null },
                            {
                                'semanticAnalysis.status': JobStatus.FAILED,
                                'semanticAnalysis.failCount': { $lte: 5 },
                            },
                        ],
                    },
                },
                {
                    $sort: {
                        'semanticAnalysis.failCount': 1,
                    },
                },
                { $limit: Config.services.openai.requestsPerMinuteLimit },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getReviewsToBeSemanticallyAnalyzed',
            }
        );
    }

    getUnansweredReviews(filters: BasicFilters): Promise<IReview[]> {
        const searchQuery = filters.buildQuery({ filterType: FilterType.REVIEWS });
        searchQuery.$and.push({
            $or: [{ 'comments.0.posted': { $in: [PostedStatus.PENDING, PostedStatus.RETRY] } }, { comments: { $in: [[], null] } }],
        });

        return this.find({
            filter: { ...searchQuery, ...this._reviewsNotDeletedOnTheirPlatformFilter },
            options: {
                lean: true,
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getUnansweredReviews',
            },
        });
    }

    getRestaurantsReviewsGroupedByRestaurantIds(filters: BasicFilters): Promise<RestaurantReviewsRatingStats[]> {
        const searchQuery = filters.buildQuery({ filterType: FilterType.REVIEWS });
        return this.aggregate(
            [
                {
                    $match: { ...searchQuery, ...this._reviewsNotDeletedOnTheirPlatformFilter },
                },
                {
                    $group: {
                        _id: '$restaurantId',
                        reviews: {
                            $push: {
                                key: '$key',
                                socialId: '$socialId',
                                rating: '$rating',
                            },
                        },
                        total: { $sum: 1 },
                        averageRating: { $avg: '$rating' },
                    },
                },
                {
                    $lookup: {
                        from: 'restaurants',
                        localField: '_id',
                        foreignField: '_id',
                        as: 'restaurant',
                    },
                },
                { $unwind: '$restaurant' },
                {
                    $project: {
                        _id: 0,
                        restaurantId: '$_id',
                        restaurant: {
                            _id: 1,
                            name: 1,
                            address: 1,
                            internalName: 1,
                        },
                        reviews: 1,
                        total: 1,
                        averageRating: 1,
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getRestaurantsReviewsGroupedByRestaurantIds',
            }
        ).exec();
    }

    getReviewsWithAnalysis(
        searchQuery: FilterQuery<IReview>,
        isSemanticAnalysisFeatureEnabled: boolean
    ): Promise<Partial<IReviewWithSemanticAnalysis>[]> {
        return this.aggregate(
            [
                { $match: { ...searchQuery, ...this._reviewsNotDeletedOnTheirPlatformFilter } },
                ...this._getSemanticAnalysisStages(isSemanticAnalysisFeatureEnabled),
                {
                    $project: {
                        socialId: 1,
                        text: 1,
                        key: 1,
                        semanticAnalysis: 1, // TODO: delete this line when feature toggle 'release-new-semantic-analysis' is removed
                        semanticAnalysisSegments: 1,
                        reviewer: 1,
                        socialCreatedAt: 1,
                        rating: 1,
                        restaurantId: 1,
                    },
                },
                {
                    $match: {
                        $or: [{ semanticAnalysis: { $exists: true } }, { semanticAnalysisSegments: { $exists: true } }],
                    },
                },
            ],
            { comment: 'getReviewsWithAnalysis' }
        ).exec();
    }

    async getReviewsWithAnalysisById(reviewId: string): Promise<ReviewWithSemanticAnalysis> {
        const review = await this.findOneOrFail({ filter: { _id: toDbId(reviewId) } });
        const isSemanticAnalysisFeatureEnabled = await isFeatureAvailableForRestaurant({
            featureName: 'release-new-semantic-analysis',
            restaurantId: review?.restaurantId?.toString() ?? '',
        });

        const result = await this.aggregate([
            { $match: { _id: toDbId(reviewId) } },
            ...this._getSemanticAnalysisStages(isSemanticAnalysisFeatureEnabled),
            ...this._getTranslationsStages(),
        ]);
        return result[0];
    }

    async getReviewPage(reviewId: string, filtersInput: ReviewFiltersInput): Promise<number> {
        const review = await this.findOne({ filter: { _id: toDbId(reviewId) } });
        if (!review) {
            throw new MalouError(MalouErrorCode.REVIEW_NOT_FOUND, { message: 'Review not found', metadata: { reviewId } });
        }
        const reviewFilters = new AdvancedReviewFilters(filtersInput, ReviewFiltersMode.PUBLIC_REVIEWS);
        const unionPipeline: PipelineStage[] = [
            {
                $match: { ...reviewFilters.buildQuery(), socialCreatedAt: { $gte: review.socialCreatedAt } },
            },
        ];
        if (filtersInput.showPrivate) {
            const privateReviewFilters = new AdvancedReviewFilters(filtersInput, ReviewFiltersMode.PRIVATE_REVIEWS);
            unionPipeline.push({
                $unionWith: {
                    coll: 'privatereviews',
                    pipeline: [
                        {
                            $match: { ...privateReviewFilters.buildQuery() },
                        },
                    ],
                },
            });
        }
        if (
            review.socialUpdatedAt?.getTime() &&
            reviewFilters.startDate?.getTime() &&
            review.socialUpdatedAt.getTime() <= reviewFilters.startDate.getTime()
        ) {
            throw new MalouError(MalouErrorCode.REVIEW_TOO_OLD, { message: 'Review too old for these filters', metadata: { reviewId } });
        }
        const result = await this.aggregate(unionPipeline, { comment: 'getReviewPage' });

        if (!result?.length) {
            throw new MalouError(MalouErrorCode.REVIEW_NOT_IN_RESULTS, { message: 'Review not in results', metadata: { reviewId } });
        }
        const index = result.findIndex((r) => r._id.toString() === reviewId);
        if (index === -1) {
            throw new MalouError(MalouErrorCode.REVIEW_NOT_IN_RESULTS, { message: 'Review not in results', metadata: { reviewId } });
        }
        return Math.floor(result.length / DEFAULT_REVIEW_PAGE_SIZE);
    }

    async getLastDayReceivedUnansweredReviewsCountGroupedByRestaurant(): Promise<{ restaurantId: string; count: number }[]> {
        const lastDay = DateTime.now().minus({ day: 1 }).toJSDate();
        return this.aggregate(
            [
                {
                    $match: {
                        socialCreatedAt: {
                            $gte: lastDay,
                        },
                        'comments.0': {
                            $exists: false,
                        },
                        // Filters the reviews that are answerable
                        $or: [
                            {
                                key: PlatformKey.LAFOURCHETTE,
                                text: { $ne: null },
                            },
                            {
                                key: PlatformKey.DELIVEROO,
                                socialCreatedAt: {
                                    $gt: lastDay,
                                },
                                text: { $ne: null },
                            },
                            {
                                key: PlatformKey.UBEREATS,
                                socialCreatedAt: {
                                    $gt: lastDay,
                                },
                            },
                            // TODO: handle DoorDash answer reply if we manage to get something else than a 403
                            // {
                            //     key: PlatformKey.DOORDASH,
                            //     socialCreatedAt: {
                            //         $gt: lastDay,
                            //     },
                            // },
                            {
                                key: {
                                    $nin: [
                                        PlatformKey.LAFOURCHETTE,
                                        PlatformKey.FOURSQUARE,
                                        PlatformKey.DELIVEROO,
                                        PlatformKey.UBEREATS,
                                        // PlatformKey.DOORDASH,
                                    ],
                                },
                            },
                        ],
                        ...this._reviewsNotDeletedOnTheirPlatformFilter,
                    },
                },
                {
                    $group: {
                        _id: '$restaurantId',
                        count: {
                            $sum: 1,
                        },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        restaurantId: '$_id',
                        count: 1,
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getLastDayReceivedUnansweredReviewsCountGroupedByRestaurant',
            }
        );
    }

    async getReviewsWithCommentsToRetryReplying(platformKey: PlatformKey): Promise<IReview[]> {
        return this.find({
            filter: {
                key: platformKey,
                'comments.posted': PostedStatus.RETRY,
                'comments.retries': { $lte: MAX_TRIPADVISOR_REPLY_RETRIES },
                ...this._reviewsNotDeletedOnTheirPlatformFilter,
            },
            options: {
                lean: true,
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getReviewsWithCommentsToRetryReplying',
            },
        });
    }

    async updateReviewCommentStatus({
        commentId,
        posted,
        retries = 0,
    }: {
        commentId: ID;
        posted: PostedStatus;
        retries?: number;
    }): Promise<IReview> {
        return this.findOneAndUpdateOrFail({
            filter: {
                'comments._id': commentId,
            },
            update: {
                'comments.$.posted': posted,
                'comments.$.retries': retries,
            },
            options: { lean: true, comment: 'updateReviewCommentStatus' },
        });
    }

    public async getEstimatedPublicReviewCount(filters: ReviewFiltersInput): Promise<number> {
        const query = new AdvancedReviewFilters(filters, ReviewFiltersMode.PUBLIC_REVIEWS).buildQuery();
        return await this.model.countDocuments(query).read(ReadPreferenceMode.SECONDARY_PREFERRED);
    }

    public async getEstimatedPublicReviewCountPerRestaurant(filters: ReviewFiltersInput): Promise<{ id: string; reviewCount: number }[]> {
        return await this.aggregate<{ id: string; reviewCount: number }>(
            [
                {
                    $match: new AdvancedReviewFilters(filters, ReviewFiltersMode.PUBLIC_REVIEWS).buildQuery(),
                },
                {
                    $group: {
                        _id: '$restaurantId',
                        reviewCount: { $sum: 1 },
                    },
                },
                {
                    $project: {
                        id: '$_id',
                        reviewCount: 1,
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getEstimatedPublicReviewCountPerRestaurant',
            }
        );
    }

    async getReviewsForReports<T>(restaurants: SimpleRestaurant[], reportType: ReportType): Promise<T> {
        const periodInDays = {
            [ReportType.DAILY_REVIEWS]: 1,
            [ReportType.WEEKLY_REVIEWS]: 7,
        }[reportType];
        const periodStartDate = DateTime.now().minus({ days: periodInDays }).set({ hour: 0, minute: 0, second: 0, millisecond: 0 });
        const periodEndDate = DateTime.now().minus({ days: 1 }).set({ hour: 23, minute: 59, second: 59, millisecond: 999 });
        const previousPeriodStartDate = periodStartDate.minus({ days: periodInDays });
        const previousPeriodEndDate = periodEndDate.minus({ days: periodInDays });

        const { facets, fieldsToAdd } = this._buildGetReviewsForReportsFacets({
            restaurants,
            periodStartDate,
            periodEndDate,
            previousPeriodStartDate,
            previousPeriodEndDate,
            reportType,
        });

        const restaurantsWithSemanticAnalysisFeatureEnabled = await isFeatureAvailableForRestaurants({
            restaurantIds: restaurants.map((r) => r.id),
            featureName: 'release-new-semantic-analysis',
        });
        const isSemanticAnalysisFeatureEnabled = restaurantsWithSemanticAnalysisFeatureEnabled.length === restaurants.length;
        const pipeline = this._buildGetReviewsForReportsPipeline({
            restaurants,
            periodStartDate,
            periodEndDate,
            previousPeriodStartDate,
            previousPeriodEndDate,
            facets,
            fieldsToAdd,
            isSemanticAnalysisFeatureEnabled,
        });
        const res = await this.aggregate(pipeline, {
            readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
            comment: 'getReviewsForReports',
        });
        const [aggregatedData] = res;

        return aggregatedData as unknown as T;
    }

    async getNegativeReviewsToBeNotified(): Promise<IReview[]> {
        return this.find({
            filter: {
                socialCreatedAt: {
                    $gte: DateTime.now().minus({ days: NEGATIVE_REVIEW_MAX_DAYS_NOTIFICATION }).toJSDate(),
                    $lte: DateTime.now().minus({ days: NEGATIVE_REVIEW_MIN_DAYS_NOTIFICATION }).toJSDate(),
                },
                rating: { $lte: NEGATIVE_REVIEW_MAX_RATING },
                comments: { $in: [[], null] },
                ...this._reviewsNotDeletedOnTheirPlatformFilter,
                $or: [
                    {
                        key: PlatformKey.LAFOURCHETTE,
                        text: { $ne: null },
                    },
                    {
                        key: PlatformKey.UBEREATS,
                        socialCreatedAt: {
                            $gt: DateTime.now().minus({ days: UBEREATS_DAYS_UNTIL_CANT_BE_ANSWERED }).toJSDate(),
                        },
                    },
                    // TODO: handle DoorDash answer reply if we manage to get something else than a 403
                    // {
                    //     key: PlatformKey.DOORDASH,
                    //     socialCreatedAt: {
                    //         $gt: DateTime.now().minus({ days: DOORDASH_DAYS_UNTIL_CANT_BE_ANSWERED }).toJSDate(),
                    //     },
                    // },
                    {
                        key: {
                            $nin: [
                                PlatformKey.LAFOURCHETTE,
                                PlatformKey.FOURSQUARE,
                                PlatformKey.DELIVEROO,
                                PlatformKey.UBEREATS,
                                PlatformKey.RESY,
                                PlatformKey.SEVENROOMS,
                                PlatformKey.DOORDASH,
                            ],
                        },
                    },
                ],
            },
            options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED, comment: 'getNegativeReviewsToBeNotified' },
        });
    }

    private _buildGetReviewsForReportsPipeline({
        restaurants,
        periodStartDate,
        periodEndDate,
        previousPeriodStartDate,
        previousPeriodEndDate,
        facets,
        fieldsToAdd,
        isSemanticAnalysisFeatureEnabled,
    }: {
        restaurants: SimpleRestaurant[];
        periodStartDate: DateTime;
        periodEndDate: DateTime;
        previousPeriodStartDate: DateTime;
        previousPeriodEndDate: DateTime;
        fieldsToAdd: any;
        facets: Record<string, any[]>;
        isSemanticAnalysisFeatureEnabled: boolean;
    }): PipelineStage[] {
        return [
            { $limit: 1 },
            { $project: { _id: '$$REMOVE' } },
            {
                $lookup: {
                    from: 'reviews',
                    pipeline: [
                        {
                            $match: {
                                $or: [
                                    {
                                        restaurantId: { $in: restaurants.map((r) => toDbId(r.id)) },
                                        socialCreatedAt: { $gte: periodStartDate, $lte: periodEndDate },
                                    },
                                    {
                                        restaurantId: { $in: restaurants.map((r) => toDbId(r.id)) },
                                        socialCreatedAt: { $gte: previousPeriodStartDate, $lte: previousPeriodEndDate },
                                    },
                                ],
                                ...this._reviewsNotDeletedOnTheirPlatformFilter,
                            },
                        },
                        ...this._getSemanticAnalysisStages(isSemanticAnalysisFeatureEnabled),
                        ...this._getTranslationsStages(),
                    ],
                    as: 'reviews',
                },
            },
            {
                $lookup: {
                    from: 'privatereviews',
                    pipeline: [
                        {
                            $match: {
                                $or: [
                                    {
                                        restaurantId: { $in: restaurants.map((r) => toDbId(r.id)) },
                                        socialCreatedAt: { $gte: periodStartDate, $lte: periodEndDate },
                                    },
                                    {
                                        restaurantId: { $in: restaurants.map((r) => toDbId(r.id)) },
                                        socialCreatedAt: { $gte: previousPeriodStartDate, $lte: previousPeriodEndDate },
                                    },
                                ],
                            },
                        },
                        {
                            $lookup: {
                                from: 'clients',
                                localField: 'clientId',
                                foreignField: '_id',
                                as: 'reviewer',
                            },
                        },
                        {
                            $unwind: {
                                path: '$reviewer',
                                preserveNullAndEmptyArrays: true, // reviews from WOF dont have a clientId
                            },
                        },
                        {
                            $addFields: {
                                'reviewer.displayName': { $concat: ['$reviewer.firstName', ' ', '$reviewer.lastName'] },
                            },
                        },
                        ...this._getTranslationsStages(),
                    ],
                    as: 'privatereviews',
                },
            },
            { $project: { union: { $concatArrays: ['$reviews', '$privatereviews'] } } },
            { $unwind: '$union' },
            { $replaceRoot: { newRoot: '$union' } },
            // the above is to make a union of reviews and private reviews https://stackoverflow.com/questions/5681851/mongodb-combine-data-from-multiple-collections-into-one-how
            {
                $facet: facets,
            },
            {
                $addFields: {
                    data: [...fieldsToAdd],
                    metadata: {
                        $arrayElemAt: ['$metadata', 0],
                    },
                    previousMetadata: {
                        $arrayElemAt: ['$previousMetadata', 0],
                    },
                },
            },
            { $project: { data: 1, metadata: 1, previousMetadata: 1 } },
        ];
    }

    private _buildGetReviewsForReportsFacets({
        restaurants,
        periodStartDate,
        periodEndDate,
        previousPeriodEndDate,
        previousPeriodStartDate,
        reportType,
    }: {
        restaurants: SimpleRestaurant[];
        periodStartDate: DateTime;
        periodEndDate: DateTime;
        previousPeriodStartDate: DateTime;
        previousPeriodEndDate: DateTime;
        reportType: ReportType;
    }) {
        const facets: Record<string, any[]> = {
            metadata: [
                {
                    $match: {
                        $or: [{ socialCreatedAt: { $gte: periodStartDate, $lte: periodEndDate } }],
                        ...this._reviewsNotDeletedOnTheirPlatformFilter,
                    },
                },
                {
                    $group: {
                        _id: null,
                        count: { $sum: 1 },
                        ratingAvg: { $avg: '$rating' },
                        notAnsweredCount: this._buildUnansweredReviewCountInGroupStage(),
                    },
                },
                { $project: { _id: 0, count: 1, ratingAvg: { $round: ['$ratingAvg', 1] }, notAnsweredCount: 1 } },
            ],
            previousMetadata: [
                {
                    $match: {
                        $or: [{ socialCreatedAt: { $gte: previousPeriodStartDate, $lte: previousPeriodEndDate } }],
                        ...this._reviewsNotDeletedOnTheirPlatformFilter,
                    },
                },
                {
                    $group: {
                        _id: null,
                        count: { $sum: 1 },
                        ratingAvg: { $avg: '$rating' },
                        notAnsweredCount: this._buildUnansweredReviewCountInGroupStage(),
                    },
                },
                { $project: { _id: 0, count: 1, ratingAvg: { $round: ['$ratingAvg', 1] }, notAnsweredCount: 1 } },
            ],
        };
        const fieldsToAdd: any[] = [];
        const _buildMatchStage = (restaurantId: string) => {
            const or = [
                {
                    restaurantId: toDbId(restaurantId),
                    socialCreatedAt: { $gte: periodStartDate, $lte: periodEndDate },
                },
            ];
            if (reportType !== ReportType.DAILY_REVIEWS) {
                or.push({
                    restaurantId: toDbId(restaurantId),
                    socialCreatedAt: { $gte: previousPeriodStartDate, $lte: previousPeriodEndDate },
                });
            }
            return {
                $match: {
                    $or: or,
                    ...this._reviewsNotDeletedOnTheirPlatformFilter,
                },
            };
        };
        const _buildOtherStages = () => {
            if (reportType === ReportType.DAILY_REVIEWS) {
                return [{ $sort: { rating: -1, text: -1 } }];
            }
            const groupByKeyStage = {
                $group: {
                    _id: '$key',
                    platformKey: { $first: '$key' },
                    previousReviews: {
                        $push: {
                            $cond: [
                                {
                                    $and: [
                                        { $gte: ['$socialCreatedAt', previousPeriodStartDate] },
                                        { $lte: ['$socialCreatedAt', previousPeriodEndDate] },
                                    ],
                                },
                                '$$ROOT',
                                null,
                            ],
                        },
                    },
                    currentReviews: {
                        $push: {
                            $cond: [
                                {
                                    $and: [{ $gte: ['$socialCreatedAt', periodStartDate] }, { $lte: ['$socialCreatedAt', periodEndDate] }],
                                },
                                '$$ROOT',
                                null,
                            ],
                        },
                    },
                    currentRatingAvg: { $avg: { $cond: [{ $gte: ['$socialCreatedAt', periodStartDate] }, '$rating', null] } },
                    previousRatingAvg: {
                        $avg: {
                            $cond: [
                                {
                                    $and: [
                                        { $gte: ['$socialCreatedAt', previousPeriodStartDate] },
                                        { $lte: ['$socialCreatedAt', previousPeriodEndDate] },
                                    ],
                                },
                                '$rating',
                                null,
                            ],
                        },
                    },
                    currentCount: { $sum: { $cond: [{ $gte: ['$socialCreatedAt', periodStartDate] }, 1, 0] } },
                    previousCount: {
                        $sum: {
                            $cond: [
                                {
                                    $and: [
                                        { $gte: ['$socialCreatedAt', previousPeriodStartDate] },
                                        { $lte: ['$socialCreatedAt', previousPeriodEndDate] },
                                    ],
                                },

                                1,
                                0,
                            ],
                        },
                    },
                },
            };
            const projectStage = {
                $project: {
                    _id: 0,
                    platformKey: 1,
                    currentReviews: {
                        $filter: {
                            input: '$currentReviews',
                            as: 'review',
                            cond: { $ne: ['$$review', null] },
                        },
                    },
                    currentRatingAvg: { $round: ['$currentRatingAvg', 1] },
                    previousRatingAvg: { $round: ['$previousRatingAvg', 1] },
                    currentCount: 1,
                    previousCount: 1,
                },
            };
            const sortStage = { $sort: { socialCreatedAt: -1, rating: -1, text: -1 } };
            return [sortStage, groupByKeyStage, projectStage];
        };

        for (const restaurant of restaurants) {
            const restId = restaurant.id.toString();
            facets[`reviews_${restId}`] = [_buildMatchStage(restaurant.id), ..._buildOtherStages()];
            facets[`metadata_${restId}`] = [
                {
                    $match: {
                        restaurantId: toDbId(restaurant.id),
                        socialCreatedAt: { $gte: periodStartDate, $lte: periodEndDate },
                        ...this._reviewsNotDeletedOnTheirPlatformFilter,
                    },
                },
                {
                    $group: {
                        _id: '$restaurantId',
                        count: { $sum: 1 },
                        ratingAvg: { $avg: '$rating' },
                        notAnsweredCount: this._buildUnansweredReviewCountInGroupStage(),
                    },
                },
                { $project: { _id: 0, count: 1, ratingAvg: { $round: ['$ratingAvg', 1] }, notAnsweredCount: 1 } },
            ];

            facets[`previousMetadata_${restId}`] = [
                {
                    $match: {
                        restaurantId: toDbId(restaurant.id),
                        socialCreatedAt: { $gte: previousPeriodStartDate, $lte: previousPeriodEndDate },
                        ...this._reviewsNotDeletedOnTheirPlatformFilter,
                    },
                },
                {
                    $group: {
                        _id: '$restaurantId',
                        count: { $sum: 1 },
                        ratingAvg: { $avg: '$rating' },
                        notAnsweredCount: this._buildUnansweredReviewCountInGroupStage(),
                    },
                },
                { $project: { _id: 0, count: 1, ratingAvg: { $round: ['$ratingAvg', 1] }, notAnsweredCount: 1 } },
            ];

            fieldsToAdd.push({
                restaurantId: toDbId(restaurant.id),
                name: restaurant.name,
                address: restaurant.formattedAddress,
                logo: restaurant.logo,
                reviews: `$reviews_${restId}`,
                metadata: { $arrayElemAt: [`$metadata_${restId}`, 0] },
                previousMetadata: { $arrayElemAt: [`$previousMetadata_${restId}`, 0] },
            });
        }

        return { facets, fieldsToAdd };
    }

    private _buildUnansweredReviewCountInGroupStage(): Record<string, any> {
        return {
            $sum: {
                $cond: [
                    {
                        $and: [
                            { $eq: ['$comments', []] },
                            {
                                $switch: {
                                    branches: [
                                        // No need to check for DELIVEROO and UBEREATS because it's daily and weekly reports
                                        // So it will always be less than the date limit
                                        // here, return true means the review is not answered
                                        {
                                            case: {
                                                $in: [
                                                    '$key',
                                                    [
                                                        PlatformKey.FOURSQUARE,
                                                        PlatformKey.RESY,
                                                        PlatformKey.SEVENROOMS,
                                                        PlatformKey.DOORDASH,
                                                    ],
                                                ],
                                            },
                                            then: false,
                                        },
                                        {
                                            case: { $eq: ['$key', PlatformKey.LAFOURCHETTE] },
                                            then: { $ne: ['$text', null] },
                                        },
                                        {
                                            case: { $eq: ['$key', PlatformKey.DELIVEROO] },
                                            then: { $ne: ['$text', null] },
                                        },
                                        {
                                            case: { $eq: ['$key', PlatformKey.PRIVATE] },
                                            then: { $eq: ['$campaignId', null] },
                                        },
                                    ],
                                    default: true,
                                },
                            },
                        ],
                    },
                    1,
                    0,
                ],
            },
        };
    }

    private async _getScoreListOfFilteredPostedCommentsByRestaurantId(
        restaurantId: string,
        filter: {
            socialUpdatedAt?: { $gte: Date; $lt: Date };
        },
        matchFilter: {
            key?: PlatformKey;
        }
    ): Promise<number[]> {
        return this.aggregate(
            [
                {
                    $match: {
                        restaurantId: toDbId(restaurantId),
                        ...matchFilter,
                        ...this._reviewsNotDeletedOnTheirPlatformFilter,
                    },
                },
                {
                    $addFields: {
                        postedCommentsWithKA: {
                            $filter: {
                                input: '$comments',
                                as: 'comment',
                                cond: {
                                    $and: [
                                        { $ne: [{ $ifNull: ['$$comment.keywordAnalysis', null] }, null] },
                                        { $eq: ['$$comment.posted', PostedStatus.POSTED] },
                                    ],
                                },
                            },
                        },
                    },
                },
                {
                    $addFields: {
                        sortedPostedCommentsWithKA: {
                            $sortArray: {
                                input: '$postedCommentsWithKA',
                                sortBy: { socialUpdatedAt: -1 },
                            },
                        },
                    },
                },
                {
                    $addFields: {
                        commentChoosen: {
                            $arrayElemAt: ['$sortedPostedCommentsWithKA', 0],
                        },
                    },
                },
                {
                    $match: {
                        commentChoosen: { $ne: null },
                    },
                },
                {
                    $replaceRoot: {
                        newRoot: '$commentChoosen',
                    },
                },
                {
                    $match: {
                        ...filter,
                    },
                },
                {
                    $group: {
                        _id: null,
                        scoreList: {
                            // $push remove missing fields (surely because undefined value does not exists in BSON)
                            $push: '$keywordAnalysis.score',
                        },
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getScoreListOfPostedCommentsByRestaurantId',
            }
        ).then((res) => res?.[0]?.scoreList || []);
    }

    /**
     * @deprecated When sortBy is 'date' the returned sort expression can’t be executed
     * with an index so this can be very slow and costly. Instead of using this function
     * on “public” reviews, consider using sort expressions like { socialSortDate: 1 }.
     */
    private _getSortPipeline(sort: { sortBy: string; sortOrder: 1 | -1 }): Record<string, any>[] {
        switch (sort.sortBy) {
            case 'platform':
                return [{ $sort: { key: sort.sortOrder } }];
            case 'rating':
                return [{ $sort: { rating: sort.sortOrder } }];
            default:
                return [
                    {
                        $addFields: {
                            creationDate: { $ifNull: ['$socialUpdatedAt', '$socialCreatedAt'] },
                        },
                    },
                    { $sort: { creationDate: sort.sortOrder } },
                ];
        }
    }

    /**
     * Returns a list of aggregation stages that add a `semanticAnalysis`
     * field on each document.
     *
     * The input documents must have two fields named `key` and `socialId`.
     *
     * This function makes sense for public reviews only. Semantic analysis
     * is unavailable on private reviews.
     */
    private _getSemanticAnalysisStages(
        isSemanticAnalysisFeatureEnabled: boolean
    ): Exclude<PipelineStage, PipelineStage.Merge | PipelineStage.Out>[] {
        if (isSemanticAnalysisFeatureEnabled) {
            return [
                {
                    $lookup: {
                        from: 'segmentanalyses',
                        let: {
                            key: '$key',
                            socialId: '$socialId',
                        },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [{ $eq: ['$platformKey', '$$key'] }, { $eq: ['$reviewSocialId', '$$socialId'] }],
                                    },
                                },
                            },
                            {
                                $lookup: {
                                    from: 'segmentanalysisparenttopics',
                                    localField: 'segmentAnalysisParentTopicIds',
                                    foreignField: '_id',
                                    as: 'segmentAnalysisParentTopics',
                                    pipeline: [
                                        {
                                            $lookup: {
                                                from: 'translations',
                                                localField: 'translationsId',
                                                foreignField: '_id',
                                                as: 'translation',
                                            },
                                        },
                                        {
                                            $addFields: {
                                                translations: { $arrayElemAt: ['$translation', 0] },
                                            },
                                        },
                                    ],
                                },
                            },
                        ],
                        as: 'semanticAnalysisSegments',
                    },
                },
            ];
        }
        return [
            {
                $lookup: {
                    from: 'reviewanalyses',
                    let: {
                        key: '$key',
                        socialId: '$socialId',
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [{ $eq: ['$platformKey', '$$key'] }, { $eq: ['$reviewSocialId', '$$socialId'] }],
                                },
                            },
                        },
                    ],
                    as: 'semanticAnalysis',
                },
            },
            {
                $set: {
                    semanticAnalysis: { $arrayElemAt: ['$semanticAnalysis', 0] },
                },
            },
        ];
    }

    private _getTranslationsStages(): Exclude<PipelineStage, PipelineStage.Merge | PipelineStage.Out>[] {
        return [
            {
                $lookup: {
                    from: 'translations',
                    localField: 'translationsId',
                    foreignField: '_id',
                    as: 'translations',
                },
            },
            {
                $unwind: {
                    path: '$translations',
                    preserveNullAndEmptyArrays: true,
                },
            },
        ];
    }

    private _getAiRelevantBricksStages(): Exclude<PipelineStage, PipelineStage.Merge | PipelineStage.Out>[] {
        return [
            {
                $lookup: {
                    from: 'translations',
                    localField: 'aiRelevantBricks.translationsId',
                    foreignField: '_id',
                    as: 'aiRelevantBricksTranslations',
                },
            },
            {
                $addFields: {
                    aiRelevantBricks: {
                        $map: {
                            input: '$aiRelevantBricks',
                            as: 'brick',
                            in: {
                                $mergeObjects: [
                                    '$$brick',
                                    {
                                        translations: {
                                            $arrayElemAt: [
                                                {
                                                    $filter: {
                                                        input: '$aiRelevantBricksTranslations',
                                                        as: 'translation',
                                                        cond: {
                                                            $eq: ['$$translation._id', '$$brick.translationsId'],
                                                        },
                                                    },
                                                },
                                                0,
                                            ],
                                        },
                                    },
                                ],
                            },
                        },
                    },
                },
            },
        ];
    }

    async getReviewsWithTextCountByRestaurantId(
        restaurantId: string,
        monthAgo: number
    ): Promise<{ month: number; year: number; count: number }[]> {
        assert(monthAgo >= 0, 'monthAgo must be positive or zero');
        const currentDate = DateTime.now();
        const endOfLastMonth = currentDate.minus({ months: 1 }).endOf('month');
        const xMonthsAgo = currentDate.minus({ months: monthAgo }).startOf('month');

        const result = await this.aggregate(
            [
                {
                    $match: {
                        restaurantId: toDbId(restaurantId),
                        socialCreatedAt: {
                            $gte: xMonthsAgo.toJSDate(),
                            $lte: endOfLastMonth.toJSDate(),
                        },
                        text: { $ne: null },
                        ...this._reviewsNotDeletedOnTheirPlatformFilter,
                    },
                },
                {
                    $addFields: {
                        year: { $year: '$socialCreatedAt' },
                        month: { $month: '$socialCreatedAt' },
                    },
                },
                {
                    $group: {
                        _id: {
                            year: '$year',
                            month: '$month',
                        },
                        count: { $sum: 1 },
                    },
                },
            ],
            {
                readPreference: ReadPreferenceMode.SECONDARY_PREFERRED,
                comment: 'getReviewsWithTextCountByRestaurantId',
            }
        );

        const monthCounts: { month: number; year: number; count: number }[] = [];
        for (let i = 0; i < monthAgo; i++) {
            const targetDate = endOfLastMonth.minus({ months: i });
            const { year, month } = targetDate;

            const monthData = result.find((item) => item._id.year === year && item._id.month === month);

            monthCounts.push({
                month,
                year,
                count: monthData ? monthData.count : 0,
            });
        }

        return monthCounts;
    }
}
