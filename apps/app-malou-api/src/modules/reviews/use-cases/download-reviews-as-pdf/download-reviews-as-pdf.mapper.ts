import { groupBy, round } from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import {
    DownloadRestaurantReviewsDataProps,
    DownloadReviewsTemplateProps,
    ReviewWithTranslationsResponseDto,
    SimpleRestaurant,
} from '@malou-io/package-dto';
import {
    ApplicationLanguage,
    DELIVEROO_DAYS_UNTIL_CANT_BE_ANSWERED,
    mapApplicationLanguageToLocale,
    PlatformKey,
    PostedStatus,
    UBEREATS_DAYS_UNTIL_CANT_BE_ANSWERED,
} from '@malou-io/package-utils';

import { isFeatureAvailableForRestaurant } from ':services/experimentations-service/experimentation.service';
import { ReviewSemanticAnalysis } from ':services/semantic-analysis/reviews/reviews.semantic-analysis.types';

@singleton()
export class RestaurantReviewsToDownloadReviewsTemplateDataMapper {
    async mapToDownloadRestaurantReviewsDataSection({
        restaurantsReviews,
        averageReviewsRating,
        restaurants,
        startDate,
        endDate,
        lang,
        timeZone,
    }: {
        restaurantsReviews: ReviewWithTranslationsResponseDto[];
        averageReviewsRating: number;
        restaurants: SimpleRestaurant[];
        startDate: Date;
        endDate: Date;
        lang: ApplicationLanguage;
        timeZone: string;
    }): Promise<DownloadReviewsTemplateProps> {
        const groupedReviews = groupBy(restaurantsReviews, 'restaurantId');
        const locale = mapApplicationLanguageToLocale(lang);
        return {
            locale,
            concernedRestaurants: restaurants.map((restaurant) => ({
                name: restaurant.name,
                address: restaurant.address,
                image: restaurant.logo,
            })),
            period: {
                startDate: startDate,
                endDate: endDate,
                timeZone,
            },
            reviewsStats: {
                stats: {
                    reviewCount: restaurantsReviews.length,
                    notAnsweredReviewCount: restaurantsReviews.filter(
                        (review) => this._canReviewBeReplied(review) && review.comments.length === 0
                    ).length,
                    averageReviewsRating: round(averageReviewsRating, 1),
                },
                restaurantCount: restaurants.length,
                noReviewsCta: {
                    link: this._getOneOrManyRestaurantsCtaLink(restaurants.length, restaurants[0]._id),
                },
                aiGlobalAnalysisCta: {
                    link: this._getOneOrManyRestaurantsCtaLink(restaurants.length, restaurants[0]._id),
                    noticeText: undefined,
                },
            },
            restaurantsReviews: await Promise.all(
                restaurants.map(async (restaurant) => ({
                    header: {
                        name: restaurant.name,
                        address: restaurant.address ?? '',
                        image: restaurant.logo,
                        reviewsCount: {
                            totalReviewsCount: groupedReviews[restaurant._id]?.length ?? 0,
                            nonAnsweredReviewsCount: (groupedReviews[restaurant._id] ?? []).filter(
                                (review) => this._canReviewBeReplied(review) && review.comments.length === 0
                            ).length,
                        },
                    },
                    cta: {
                        link: this._getOneOrManyRestaurantsCtaLink(restaurants.length, restaurants[0]._id),
                    },
                    reviewsGroupedByPlatform: await this._groupReviewsByGroupedPlatform(groupedReviews[restaurant._id] ?? [], locale),
                    bordered: false,
                }))
            ),
        };
    }

    addAiGlobalAnalysisCta(
        reviewsToDownload: DownloadReviewsTemplateProps,
        aiGlobalAnalysisCta: string | undefined
    ): DownloadReviewsTemplateProps {
        return {
            ...reviewsToDownload,
            reviewsStats: {
                ...reviewsToDownload.reviewsStats,
                aiGlobalAnalysisCta: {
                    ...reviewsToDownload.reviewsStats.aiGlobalAnalysisCta,
                    noticeText: aiGlobalAnalysisCta,
                },
            },
        };
    }

    private _getOneOrManyRestaurantsCtaLink(restaurantCount: number, firstRestaurantId: string): string {
        if (restaurantCount > 1) {
            return `${process.env.BASE_URL}/groups/reputation/reviews`;
        } else {
            return `${process.env.BASE_URL}/restaurants/${firstRestaurantId}/reputation/reviews`;
        }
    }

    private async _groupReviewsByGroupedPlatform(
        restaurantReviews: ReviewWithTranslationsResponseDto[],
        locale: string
    ): Promise<DownloadRestaurantReviewsDataProps['reviewsGroupedByPlatform']> {
        const reviewsGroupedByPlatformKey = groupBy(restaurantReviews, 'key');

        const reviewsGroupedByPlatformKeyWithSegmentAnalysis = await Promise.all(
            Object.entries(reviewsGroupedByPlatformKey).map(async ([key, reviews]) => {
                const reviewsWithSegmentAnalysis = await Promise.all(
                    reviews.map(async (review) => {
                        const segmentAnalysis = await this._getReviewsSemanticAnalysis(review.restaurantId, review);
                        return {
                            ...review,
                            segmentAnalysis,
                        };
                    })
                );
                return { key, reviews: reviewsWithSegmentAnalysis };
            })
        );
        return reviewsGroupedByPlatformKeyWithSegmentAnalysis.reduce(
            (acc, { key, reviews }) => {
                acc[key] = reviews.map((review) => {
                    return {
                        reviewerProfilePhoto: review.reviewer?.profilePhotoUrl,
                        platformImage: review.key,
                        reviewerName: review.reviewer?.displayName,
                        rating: review.rating,
                        text: review.text,
                        translatedFrom: review.lang && review.lang !== locale ? review.lang : undefined,
                        segmentAnalysis: review.segmentAnalysis ?? [],
                        scanChipName: review.scan?.nfcSnapshot?.chipName,
                        socialCreatedAt: review.socialCreatedAt ? new Date(review.socialCreatedAt) : undefined,
                        socialUpdatedAt: review.socialUpdatedAt ? new Date(review.socialUpdatedAt) : undefined,
                    };
                });

                return acc;
            },
            {} as DownloadRestaurantReviewsDataProps['reviewsGroupedByPlatform']
        );
    }

    private async _getReviewsSemanticAnalysis(
        restaurantId: string,
        review: ReviewWithTranslationsResponseDto
    ): Promise<ReviewSemanticAnalysis[]> {
        const isSemanticAnalysisFeatureEnabled = await isFeatureAvailableForRestaurant({
            featureName: 'release-new-semantic-analysis',
            restaurantId,
        });

        if (isSemanticAnalysisFeatureEnabled) {
            return (
                review.semanticAnalysisSegments?.map(({ category, sentiment, segment }) => ({
                    tag: category,
                    sentiment,
                    originalSegment: segment,
                })) ?? []
            );
        }
        return (
            review.semanticAnalysis?.segmentAnalyses?.map(({ tag, sentiment, originalSegment }) => ({
                tag,
                sentiment,
                originalSegment,
            })) ?? []
        );
    }

    private _canReviewBeReplied(review: ReviewWithTranslationsResponseDto): boolean {
        switch (review.key) {
            case PlatformKey.UBEREATS: {
                const hasOnlyRejectedComment =
                    review.comments.length === review.comments.filter((comment) => comment.posted === PostedStatus.REJECTED).length;
                const diffDays = this.computeReviewElapsedDays(review);
                return (
                    (hasOnlyRejectedComment || review.comments?.length === 0) &&
                    (diffDays ?? 0) < (this._getDaysUntilCantBeAnswered(review.key) ?? 0)
                );
            }
            case PlatformKey.DELIVEROO: {
                const diffDays = this.computeReviewElapsedDays(review);
                return (
                    review.comments?.length === 0 &&
                    (diffDays ?? 0) < (this._getDaysUntilCantBeAnswered(review.key) ?? 0) &&
                    !!review.text?.length
                );
            }
            case PlatformKey.DOORDASH:
            case PlatformKey.FOURSQUARE:
            case PlatformKey.RESY:
            case PlatformKey.SEVENROOMS:
                return false;
            case PlatformKey.LAFOURCHETTE:
                return !!review.text?.length;
            case PlatformKey.PRIVATE:
                // only private reviews from campaigns can be replied
                // private reviews from totem scans cannot be replied
                return !!review.campaignId;
            default:
                return true;
        }
    }

    private computeReviewElapsedDays(review: ReviewWithTranslationsResponseDto): number {
        const date = new Date(review.socialCreatedAt);
        return DateTime.now().diff(DateTime.fromJSDate(date), 'day').toObject().days!;
    }

    private _getDaysUntilCantBeAnswered(platformKey: PlatformKey): number | undefined {
        switch (platformKey) {
            case PlatformKey.DELIVEROO:
                return DELIVEROO_DAYS_UNTIL_CANT_BE_ANSWERED;
            case PlatformKey.UBEREATS:
                return UBEREATS_DAYS_UNTIL_CANT_BE_ANSWERED;
            // TODO: handle DoorDash answer reply if we manage to get something else than a 403
            // case PlatformKey.DOORDASH:
            //     return DOORDASH_DAYS_UNTIL_CANT_BE_ANSWERED;
            default:
                return undefined;
        }
    }
}
