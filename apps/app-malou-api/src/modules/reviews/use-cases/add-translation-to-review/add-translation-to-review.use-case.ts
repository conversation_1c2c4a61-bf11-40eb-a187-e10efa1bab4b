import { singleton } from 'tsyringe';

import { ReviewWithTranslationsResponseDto } from '@malou-io/package-dto';
import { ApplicationLanguage, TranslationSource } from '@malou-io/package-utils';

import { AddTranslationToReviewService } from ':modules/reviews/services/add-translation-to-review.service';

@singleton()
export class AddTranslationToReviewUseCase {
    constructor(private readonly _addTranslationToReviewService: AddTranslationToReviewService) {}

    // TODO - Add transaction (cannot currently because it fails the unit tests)
    async execute(
        reviewId: string,
        translation: string,
        language: ApplicationLanguage,
        source: TranslationSource,
        isPrivateReview: boolean
    ): Promise<ReviewWithTranslationsResponseDto> {
        return await this._addTranslationToReviewService.execute({ reviewId, translation, language, source, isPrivateReview });
    }
}
