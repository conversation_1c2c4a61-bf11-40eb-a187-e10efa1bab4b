import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { ReviewWithTranslationsResponseDto } from '@malou-io/package-dto';
import { ITranslations } from '@malou-io/package-models';
import { ApplicationLanguage, MalouErrorCode, mapLanguageStringToApplicationLanguage, TranslationSource } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { PrivateReviewWithTranslations } from ':modules/private-reviews/private-reviews.interface';
import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';
import { ReviewWithTranslations } from ':modules/reviews/reviews.interfaces';
import { ReviewsDtoMapper } from ':modules/reviews/reviews.mapper.dto';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { Translations } from ':modules/translations/entities/translations.entity';
import { TranslationsRepository } from ':modules/translations/translations.repository';

@singleton()
export class AddTranslationToReviewService {
    constructor(
        private readonly _privateReviewsRepository: PrivateReviewsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _translationsRepository: TranslationsRepository,
        private readonly _reviewsDtoMapper: ReviewsDtoMapper
    ) {}

    // TODO - Add transaction (cannot currently because it fails the unit tests)
    async execute({
        reviewId,
        translation,
        language,
        source,
        isPrivateReview,
    }: {
        reviewId: string;
        translation: string;
        language: ApplicationLanguage;
        source: TranslationSource;
        isPrivateReview: boolean;
    }): Promise<ReviewWithTranslationsResponseDto> {
        const review = isPrivateReview
            ? await this._privateReviewsRepository.getPrivateReviewByIdWithTranslation(reviewId)
            : await this._reviewsRepository.getReviewByIdWithTranslation(reviewId);

        if (!review) {
            throw new MalouError(MalouErrorCode.REVIEW_NOT_FOUND, { message: 'Review not found', metadata: { reviewId } });
        }

        const alreadyHasTranslations = !!review.translations?._id;
        let updatedReview: ReviewWithTranslations | PrivateReviewWithTranslations | null = null;

        if (alreadyHasTranslations) {
            await this._updateTranslations(review.translations, translation, language, source);
            updatedReview = { ...review, translations: { ...review.translations, [language]: translation, source } };
        } else {
            const translations = await this._createNewTranslations(translation, language, source, review.text!, review.lang!);
            updatedReview = isPrivateReview
                ? await this._privateReviewsRepository.setTranslationsToPrivateReview(reviewId, translations.id)
                : await this._reviewsRepository.setTranslationsToReview(reviewId, translations.id);
        }

        assert(updatedReview, 'Review not found');
        return this._reviewsDtoMapper.toReviewWithTranslationsResponseDto(updatedReview);
    }

    private async _updateTranslations(
        translations: ReviewWithTranslations['translations'],
        text: string,
        language: ApplicationLanguage,
        source: TranslationSource
    ) {
        const filter: Partial<Translations> = { id: translations._id.toString() };
        const update: Partial<Translations> = { [language]: text, source };
        await this._translationsRepository.updateTranslations(filter, update);
    }

    private async _createNewTranslations(
        text: string,
        language: ApplicationLanguage,
        source: TranslationSource,
        originalText: string,
        originalLanguage: string
    ): Promise<Translations> {
        const originalLanguageAsApplicationLanguage = mapLanguageStringToApplicationLanguage(originalLanguage);
        const translations: Omit<ITranslations, '_id' | 'createdAt' | 'updatedAt'> = {
            language: originalLanguageAsApplicationLanguage,
            source,
            [language]: text,
            [originalLanguage]: originalText,
        };
        const newTranslations = await this._translationsRepository.createTranslations([translations]);
        return newTranslations[0];
    }
}
