import { uniq } from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { IRestaurant, IRestaurantAttribute, IStoreLocatorRestaurantPage, PopulateBuilderHelper, toDbId } from '@malou-io/package-models';
import {
    Day,
    filterByRequiredKeys,
    formatPhoneForDisplay,
    isNotNil,
    MalouAttributesEnum,
    RestaurantAttributeValue,
} from '@malou-io/package-utils';

import { GmbCategoryIdEnum } from ':modules/categories/types';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { RestaurantAttributesRepository } from ':modules/restaurant-attributes/restaurant-attributes.repository';
import { categoriesToServesCuisine } from ':modules/store-locator/mappings/categories-to-schema-org-serves-cuisine.mapping';
import { FetchLatestGmbRatingService } from ':modules/store-locator/services/fetch-latest-gmb-rating/fetch-latest-gmb-rating.service';
import { RestaurantPopulatedForStoreLocator } from ':modules/store-locator/services/fetch-store-head/interfaces';
import { GetPaymentMethodsService } from ':modules/store-locator/services/get-payment-methods/get-payment-methods.service';
import { mapStoreLocatorLanguageToMalouLocale } from ':modules/store-locator/shared/utils';
import { Translation } from ':services/translation.service';

@singleton()
export class GenerateRestaurantSchemaOrgMicroDataService {
    private readonly _DAY_MAPPING: Record<Day, string> = {
        [Day.MONDAY]: 'Mo',
        [Day.TUESDAY]: 'Tu',
        [Day.WEDNESDAY]: 'We',
        [Day.THURSDAY]: 'Th',
        [Day.FRIDAY]: 'Fr',
        [Day.SATURDAY]: 'Sa',
        [Day.SUNDAY]: 'Su',
    };

    constructor(
        private readonly _getPaymentMethodsService: GetPaymentMethodsService,
        private readonly _restaurantAttributesRepository: RestaurantAttributesRepository,
        private readonly _platformRepository: PlatformsRepository,
        private readonly _fetchLatestGmbRatingService: FetchLatestGmbRatingService,
        private readonly _translate: Translation
    ) {}

    async execute({
        restaurant,
        storeLocatorRestaurantPage,
        alternateStoreLocatorRestaurantPages,
    }: {
        restaurant: RestaurantPopulatedForStoreLocator;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
        alternateStoreLocatorRestaurantPages?: IStoreLocatorRestaurantPage[];
    }): Promise<string> {
        const cuisineType = this._getSchemaOrgCuisineType(restaurant);
        const priceRange = '$$'; // todo store-locator
        const openingHours = this._mapOpeningHours(restaurant.regularHours);
        const openingHoursSpecification = this._mapSpecialHours(restaurant.specialHours);
        const phone = formatPhoneForDisplay({
            prefix: restaurant.phone?.prefix ?? undefined,
            digits: restaurant.phone?.digits ?? undefined,
        });

        const [attributes, platforms, gmbReviewsData] = await Promise.all([
            this._restaurantAttributesRepository.find({
                filter: {
                    restaurantId: toDbId(restaurant._id),
                    attributeValue: RestaurantAttributeValue.YES,
                },
                options: {
                    lean: true,
                    populate: [{ path: 'attribute' }],
                },
            }),
            this._platformRepository.find({
                filter: { restaurantId: toDbId(restaurant._id) },
                projection: { socialLink: 1 },
                options: { lean: true },
            }),
            this._fetchLatestGmbRatingService.execute({
                restaurantId: restaurant._id.toString(),
            }),
        ]);

        const paymentMethodAttributes = this._getPaymentMethodsService.execute(attributes);
        const paymentAccepted = uniq([
            ...paymentMethodAttributes.map((pM) => pM.attributeName[storeLocatorRestaurantPage.lang]),
            this._translate
                .fromLang({ lang: mapStoreLocatorLanguageToMalouLocale(storeLocatorRestaurantPage.lang) })
                .store_locator.information_block.payment_method.cash(),
        ]).join(', ');
        const acceptsReservations = this._getReservationValue(restaurant, attributes);

        const microdata = {
            '@context': 'https://schema.org',
            '@type': 'Restaurant',
            name: restaurant.name,
            description: storeLocatorRestaurantPage.blocks.head.description,
            url: storeLocatorRestaurantPage.fullUrl,
            ...(phone && { telephone: phone }),
            ...(restaurant.address && {
                address: {
                    '@type': 'PostalAddress',
                    ...(restaurant.address?.route && { streetAddress: restaurant.address?.route }),
                    ...(restaurant.address?.locality && { addressLocality: restaurant.address?.locality }),
                    ...(restaurant.address?.regionCode && { addressRegion: restaurant.address?.regionCode }),
                    ...(restaurant.address?.postalCode && { postalCode: restaurant.address?.postalCode }),
                    ...(restaurant.address?.country && { addressCountry: restaurant.address?.country }),
                },
            }),
            ...(restaurant.latlng && {
                geo: {
                    '@type': 'GeoCoordinates',
                    latitude: restaurant.latlng.lat,
                    longitude: restaurant.latlng.lng,
                },
            }),
            ...(restaurant.menuUrl && { menu: restaurant.menuUrl }),
            servesCuisine: cuisineType,
            priceRange,
            // todo store-locator: check if these formats should be translated
            ...(openingHours.length > 0 && { openingHours }),
            openingHoursSpecification,
            ...(gmbReviewsData && {
                aggregateRating: {
                    '@type': 'AggregateRating',
                    ratingValue: gmbReviewsData.rating,
                    reviewCount: gmbReviewsData.reviewsCount,
                },
            }),
            // todo store-locator: check if these formats should be translated
            paymentAccepted,
            acceptsReservations,
            ...(restaurant.latlng && {
                hasMap: `https://www.google.com/maps/place/${restaurant.latlng.lat},${restaurant.latlng.lng}`,
            }),
            sameAs: uniq(
                [
                    ...(restaurant.socialNetworkUrls?.map((socialNetworkUrl) => socialNetworkUrl.url) ?? []),
                    ...platforms.map((platform) => platform.socialLink),
                    ...(alternateStoreLocatorRestaurantPages
                        ?.filter(({ _id }) => _id.toString() !== storeLocatorRestaurantPage._id.toString())
                        ?.map((page) => page.fullUrl) ?? []),
                    restaurant.website,
                ].filter(Boolean)
            ),
        };

        return JSON.stringify(microdata);
    }

    private _mapOpeningHours(regularHours: IRestaurant['regularHours']): string[] {
        // Group the hours by openDay
        const groupedByDay = {} as Record<Day, IRestaurant['regularHours']>;

        (regularHours ?? []).forEach(({ openDay, openTime, closeTime, isClosed }) => {
            if (!isClosed) {
                let hoursForDay = groupedByDay[openDay];
                if (!hoursForDay) {
                    hoursForDay = [];
                }
                hoursForDay.push({ openDay, openTime, closeDay: openDay, closeTime, isClosed: false });
            }
        });

        // Create the openingHours array by formatting each day's hours
        const openingHours: string[] = [];

        Object.keys(groupedByDay).forEach((day) => {
            const dayAbbr = this._DAY_MAPPING[day];
            const times = groupedByDay[day];

            // If there are multiple time ranges for the same day, add them separately
            times?.forEach(({ openTime, closeTime }) => {
                openingHours.push(`${dayAbbr} ${openTime}-${closeTime}`);
            });
        });

        // Return the final schema.org formatted data
        return openingHours;
    }

    private _mapSpecialHours(specialHours: IRestaurant['specialHours']) {
        const onlyFutureSpecialHours = filterByRequiredKeys(specialHours, ['startDate', 'endDate'])
            .filter((sH) => {
                const startDate = DateTime.fromObject({
                    day: sH.startDate.day,
                    month: sH.startDate.month + 1, // because month is 0 indexed
                    year: sH.startDate.year,
                }).toJSDate();
                const now = DateTime.now().startOf('day').toJSDate();
                return startDate >= now;
            })
            .map((sH) => {
                return {
                    startDate: DateTime.fromObject({
                        day: sH.startDate.day,
                        month: sH.startDate.month + 1, // because month is 0 indexed
                        year: sH.startDate.year,
                    }).toJSDate(),
                    endDate: DateTime.fromObject({
                        day: sH.endDate.day,
                        month: sH.endDate.month + 1, // because month is 0 indexed
                        year: sH.endDate.year,
                    }).toJSDate(),
                    isClosed: sH.isClosed,
                    openTime: sH.openTime,
                    closeTime: sH.closeTime,
                };
            });
        return onlyFutureSpecialHours.map((sH) => {
            if (sH.isClosed) {
                return {
                    '@type': 'OpeningHoursSpecification',
                    validFrom: sH.startDate.toISOString().split('T')[0],
                    validThrough: sH.endDate.toISOString().split('T')[0],
                };
            }
            return {
                '@type': 'OpeningHoursSpecification',
                validFrom: sH.startDate.toISOString().split('T')[0],
                validThrough: sH.endDate.toISOString().split('T')[0],
                opens: sH.openTime,
                closes: sH.closeTime,
            };
        });
    }

    private _getReservationValue(
        restaurant: RestaurantPopulatedForStoreLocator,
        attributes: Pick<PopulateBuilderHelper<IRestaurantAttribute, [{ path: 'attribute' }]>, 'attribute'>[]
    ): boolean | string {
        if (restaurant.reservationUrl) {
            return restaurant.reservationUrl;
        }

        const reservation = attributes.find((a) => a.attribute.attributeId === MalouAttributesEnum.ACCEPTS_RESERVATIONS);
        if (reservation) {
            return true;
        }
        return false;
    }

    private _getSchemaOrgCuisineType(restaurant: RestaurantPopulatedForStoreLocator): string {
        try {
            const categoryIds = [...restaurant.categoryList, restaurant.category].map(({ categoryId }) => categoryId);

            const schemaOrgCuisineType = categoryIds
                .map((categoryId) => categoriesToServesCuisine[categoryId as GmbCategoryIdEnum])
                .filter(isNotNil)
                .flat();

            if (schemaOrgCuisineType.length === 0) {
                return 'Restaurant';
            }

            return schemaOrgCuisineType.join(', ');
        } catch (err) {
            return 'Restaurant';
        }
    }
}
