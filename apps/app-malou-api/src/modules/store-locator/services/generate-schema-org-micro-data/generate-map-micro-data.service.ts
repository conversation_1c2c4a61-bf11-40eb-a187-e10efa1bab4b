import { uniq } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IStoreLocatorMapPage, toDbId } from '@malou-io/package-models';
import { isNotNil, StoreLocatorPageStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { GenerateRestaurantSchemaOrgMicroDataService } from ':modules/store-locator/services/generate-schema-org-micro-data/generate-restaurant-micro-data.service';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
export class GenerateMapSchemaOrgMicroDataService {
    constructor(
        private readonly _generateRestaurantSchemaOrgMicroDataService: GenerateRestaurantSchemaOrgMicroDataService,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute({
        storeLocatorMapPage,
        alternateStoreLocatorMapPages,
        title,
        description,
        organization,
    }: {
        storeLocatorMapPage: IStoreLocatorMapPage;
        alternateStoreLocatorMapPages: IStoreLocatorMapPage[];
        title: string;
        description: string;
        organization: {
            name: string;
            logo: string;
        };
    }): Promise<string> {
        const restaurantPages = await this._storeLocatorRestaurantPageRepository.find({
            filter: {
                organizationId: toDbId(storeLocatorMapPage.organizationId),
                lang: storeLocatorMapPage.lang,
                status: StoreLocatorPageStatus.PUBLISHED,
            },
            options: { lean: true },
        });

        const restaurants = await this._restaurantsRepository.find({
            filter: { _id: { $in: restaurantPages.map((page) => toDbId(page.restaurantId)) } },
            options: {
                lean: true,
                populate: [
                    { path: 'category' },
                    { path: 'logo' },
                    { path: 'cover' },
                    { path: 'categoryList' },
                    { path: 'attributeList', populate: [{ path: 'attribute' }] },
                ],
            },
        });

        const storesMicroData = (
            await Promise.all(
                restaurants.map(async (restaurant) => {
                    try {
                        const restaurantPage = restaurantPages.find((page) => page.restaurantId.toString() === restaurant._id.toString());
                        assert(restaurantPage, 'Restaurant page not found for restaurant');
                        return await this._generateRestaurantSchemaOrgMicroDataService.execute({
                            restaurant,
                            storeLocatorRestaurantPage: restaurantPage,
                        });
                    } catch (error) {
                        logger.error(`[STORE_LOCATOR] [Map] [Head block] Error generating restaurant schema org`, {
                            error,
                            restaurantId: restaurant._id.toString(),
                            storeLocatorMapPageId: storeLocatorMapPage._id.toString(),
                        });
                        return null;
                    }
                })
            )
        )
            .filter(isNotNil)
            .map((microData) => JSON.parse(microData));

        const microdata = {
            '@context': 'https://schema.org',
            '@type': 'Map',
            name: title,
            description,
            url: storeLocatorMapPage.fullUrl,
            mapType: 'VenueMap',
            // TODO: store-locator to add on restaurant types also [@hamza]
            creator: {
                '@type': 'Organization',
                name: organization.name,
                logo: organization.logo,
            },
            about: storesMicroData,
            sameAs: uniq(
                [
                    ...(alternateStoreLocatorMapPages
                        ?.filter(({ _id }) => _id.toString() !== storeLocatorMapPage._id.toString())
                        ?.map((page) => page.fullUrl) ?? []),
                ].filter(Boolean)
            ),
        };

        return JSON.stringify(microdata);
    }
}
