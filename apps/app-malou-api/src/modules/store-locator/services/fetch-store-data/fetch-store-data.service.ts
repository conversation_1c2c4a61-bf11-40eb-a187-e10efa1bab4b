import lodash from 'lodash';
import { singleton } from 'tsyringe';

import {
    GetStoreLocatorDraftStoreDto,
    GetStoreLocatorStorePageDto,
    storeLocatorDraftStoreValidator,
    storeLocatorStoreValidator,
} from '@malou-io/package-dto';
import { IStoreLocatorRestaurantPage, toDbId } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { FetchStoreLocatorCallToActionsBlockService } from ':modules/store-locator/services/fetch-store-call-to-actions/fetch-store-call-to-actions.service';
import { FetchStoreLocatorDescriptionsBlockService } from ':modules/store-locator/services/fetch-store-descriptions/fetch-store-descriptions.service';
import { FetchStoreLocatorFaqBlockService } from ':modules/store-locator/services/fetch-store-faq/fetch-store-faq.service';
import { FetchStoreLocatorGalleryBlockService } from ':modules/store-locator/services/fetch-store-gallery/fetch-store-gallery.service';
import { FetchStoreLocatorHeadBlockService } from ':modules/store-locator/services/fetch-store-head/fetch-store-head.service';
import { FetchStoreLocatorInformationBlockService } from ':modules/store-locator/services/fetch-store-information/fetch-store-information.service';
import { FetchStoreLocatorReviewsBlockService } from ':modules/store-locator/services/fetch-store-reviews/fetch-store-reviews.service';
import {
    FetchStoreLocatorSocialNetworksBlockService,
    SocialNetworksData,
} from ':modules/store-locator/services/fetch-store-social-networks/fetch-store-social-networks.service';
import { FetchStoreLocatorSuggestionsForEditService } from ':modules/store-locator/services/fetch-store-suggestions-for-edit/fetch-store-suggestions-for-edit.service';

type FetchStoreLocatorStoreResult<T extends boolean | undefined> = {
    success: boolean;
    restaurantId: string;
    data: T extends true ? GetStoreLocatorDraftStoreDto | undefined : GetStoreLocatorStorePageDto | undefined;
};

@singleton()
export class FetchStoreLocatorStoreService {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _fetchStoreLocatorReviewsBlockService: FetchStoreLocatorReviewsBlockService,
        private readonly _fetchStoreLocatorInformationBlockService: FetchStoreLocatorInformationBlockService,
        private readonly _fetchStoreLocatorHeadBlockService: FetchStoreLocatorHeadBlockService,
        private readonly _fetchStoreLocatorSocialNetworksBlockService: FetchStoreLocatorSocialNetworksBlockService,
        private readonly _fetchStoreLocatorGalleryBlockService: FetchStoreLocatorGalleryBlockService,
        private readonly _fetchStoreLocatorDescriptionsBlockService: FetchStoreLocatorDescriptionsBlockService,
        private readonly _fetchStoreLocatorCallToActionsBlockService: FetchStoreLocatorCallToActionsBlockService,
        private readonly _fetchStoreLocatorSuggestionsForEditService: FetchStoreLocatorSuggestionsForEditService,
        private readonly _fetchStoreLocatorFaqBlockService: FetchStoreLocatorFaqBlockService
    ) {}

    async execute<T extends boolean | undefined = false>(params: {
        restaurantId: string;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
        alternateStoreLocatorRestaurantPages?: IStoreLocatorRestaurantPage[];
        socialNetworksData?: SocialNetworksData;
        isForEdit?: T;
    }): Promise<FetchStoreLocatorStoreResult<T>> {
        const {
            isForEdit = false,
            restaurantId,
            storeLocatorOrganizationConfig,
            storeLocatorRestaurantPage,
            socialNetworksData,
            alternateStoreLocatorRestaurantPages,
        } = params;

        try {
            logger.info('[STORE_LOCATOR] Fetching store data');

            const restaurant = await this._restaurantsRepository.findOneOrFail({
                filter: { _id: toDbId(restaurantId) },
                options: {
                    lean: true,
                    populate: [
                        { path: 'category' },
                        { path: 'logo' },
                        { path: 'cover' },
                        { path: 'categoryList' },
                        { path: 'attributeList', populate: [{ path: 'attribute' }] },
                    ],
                },
            });

            const [
                headBlock,
                informationBlock,
                reviewsBlock,
                socialNetworksBlock,
                galleryBlock,
                callToActionsBlock,
                descriptionsBlock,
                faqBlock,
                suggestionsForEdit,
            ] = await Promise.all([
                this._fetchStoreLocatorHeadBlockService.execute({
                    restaurant,
                    storeLocatorOrganizationConfig,
                    storeLocatorRestaurantPage,
                    alternateStoreLocatorRestaurantPages,
                    isForEdit,
                }),
                this._fetchStoreLocatorInformationBlockService.execute({
                    restaurantId,
                    storeLocatorOrganizationConfig,
                    storeLocatorRestaurantPage,
                }),
                this._fetchStoreLocatorReviewsBlockService.execute({ restaurantId, storeLocatorRestaurantPage, isForEdit }),
                this._fetchStoreLocatorSocialNetworksBlockService.execute({
                    restaurant,
                    storeLocatorOrganizationConfig,
                    storeLocatorRestaurantPage,
                    socialNetworksData,
                    isForEdit,
                }),
                this._fetchStoreLocatorGalleryBlockService.execute({ storeLocatorRestaurantPage }),
                this._fetchStoreLocatorCallToActionsBlockService.execute({ storeLocatorRestaurantPage }),
                this._fetchStoreLocatorDescriptionsBlockService.execute({ storeLocatorRestaurantPage }),
                this._fetchStoreLocatorFaqBlockService.execute({ storeLocatorRestaurantPage }),
                ...(isForEdit ? [this._fetchStoreLocatorSuggestionsForEditService.execute({ storeLocatorRestaurantPage })] : []),
            ]);

            const storeData = {
                id: restaurant._id.toString(),
                name: restaurant.name,
                internalName: restaurant.internalName,
                organizationName: storeLocatorOrganizationConfig.organization.name,
                lang: storeLocatorRestaurantPage.lang,
                relativePath: storeLocatorRestaurantPage.relativePath,
                shouldDisplayWhiteMark: storeLocatorOrganizationConfig.shouldDisplayWhiteMark,
                styles: {
                    ...storeLocatorOrganizationConfig.styles.pages.common,
                    ...storeLocatorOrganizationConfig.styles.pages.store,
                },
                headBlock: headBlock.data,
                informationBlock: informationBlock.data,
                galleryBlock: galleryBlock.data,
                reviewsBlock: reviewsBlock.data,
                socialNetworksBlock: socialNetworksBlock.data,
                descriptionsBlock: descriptionsBlock.data,
                callToActionsBlock: callToActionsBlock.data,
                faqBlock: faqBlock.data,
                ...(suggestionsForEdit && { suggestionsForEdit }),
            };
            const success = lodash.every(
                [
                    headBlock,
                    informationBlock,
                    reviewsBlock,
                    socialNetworksBlock,
                    galleryBlock,
                    callToActionsBlock,
                    descriptionsBlock,
                    faqBlock,
                ],
                'success'
            );

            const parsedStoreData = await (isForEdit
                ? storeLocatorDraftStoreValidator.parseAsync(storeData)
                : storeLocatorStoreValidator.parseAsync(storeData));

            return { success, restaurantId, data: parsedStoreData } as FetchStoreLocatorStoreResult<T>;
        } catch (err) {
            logger.error('[STORE_LOCATOR] Failed to fetch store data', { err });
            return { success: false, restaurantId, data: undefined } as FetchStoreLocatorStoreResult<T>;
        }
    }
}
