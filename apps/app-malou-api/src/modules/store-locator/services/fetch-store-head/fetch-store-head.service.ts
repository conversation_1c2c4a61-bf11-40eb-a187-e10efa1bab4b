import { isNil } from 'lodash';
import { singleton } from 'tsyringe';

import { GetStoreLocatorStorePageDto, storeLocatorHeadBlockValidator } from '@malou-io/package-dto';
import { IStoreLocatorRestaurantPage } from '@malou-io/package-models';
import { getXUserName, isNotNil, SocialNetworkKey, StoreLocatorLanguage } from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { RestaurantPopulatedForStoreLocator } from ':modules/store-locator/services/fetch-store-head/interfaces';
import { GenerateRestaurantSchemaOrgMicroDataService } from ':modules/store-locator/services/generate-schema-org-micro-data/generate-restaurant-micro-data.service';
import { mapStoreLocatorLanguageToLocale } from ':modules/store-locator/shared/utils';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
export class FetchStoreLocatorHeadBlockService {
    constructor(
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _generateRestaurantSchemaOrgMicroDataService: GenerateRestaurantSchemaOrgMicroDataService
    ) {}

    async execute({
        restaurant,
        storeLocatorOrganizationConfig,
        storeLocatorRestaurantPage,
        alternateStoreLocatorRestaurantPages,
        isForEdit = false,
    }: {
        restaurant: RestaurantPopulatedForStoreLocator;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
        alternateStoreLocatorRestaurantPages?: IStoreLocatorRestaurantPage[];
        isForEdit?: boolean;
    }): Promise<{ success: boolean; data: GetStoreLocatorStorePageDto['headBlock'] | undefined }> {
        try {
            // In edition mode, prefer backup if it exists to enhance performance
            if (isForEdit && storeLocatorRestaurantPage.blocks.head.backup) {
                try {
                    await storeLocatorHeadBlockValidator.parseAsync(storeLocatorRestaurantPage.blocks.head.backup);

                    return {
                        success: true,
                        data: storeLocatorRestaurantPage.blocks.head.backup as GetStoreLocatorStorePageDto['headBlock'],
                    };
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [Head block] Failed to validate backup', { err: error });
                }
            }

            const url = storeLocatorRestaurantPage.fullUrl;
            const snippetImageUrl = storeLocatorRestaurantPage.blocks.head.snippetImageUrl;
            const facebookImageUrl = storeLocatorRestaurantPage.blocks.head.facebookImageUrl;
            const twitterImageUrl = storeLocatorRestaurantPage.blocks.head.twitterImageUrl;
            const title = storeLocatorRestaurantPage.blocks.head.title;
            const description = storeLocatorRestaurantPage.blocks.head.description;
            const keywords = storeLocatorRestaurantPage.blocks.head.keywords;
            const xUserName = this._getXUserName(restaurant);
            const locale = mapStoreLocatorLanguageToLocale(storeLocatorRestaurantPage.lang);
            const googleAnalytics = {
                id: Config.storeLocator.googleAnalyticsId,
                organizationId: storeLocatorOrganizationConfig.organization.name.trim().toLowerCase().replaceAll(/ /g, '-'),
                storeId: (restaurant.internalName || restaurant.name).trim().toLowerCase().replaceAll(/ /g, '-'),
                pageCategory: 'local-store-page',
            };
            const googleAnalyticsClientId = storeLocatorOrganizationConfig.plugins?.googleAnalytics?.trackingId;
            const alternatePageUrls = this._getAlternateStoreLocatorRestaurantPages({
                storeLocatorRestaurantPages: alternateStoreLocatorRestaurantPages,
                storeLocatorOrganizationConfig,
            });

            const microdata = await this._generateRestaurantSchemaOrgMicroDataService.execute({
                restaurant,
                storeLocatorRestaurantPage,
                alternateStoreLocatorRestaurantPages,
            });

            const headBlock = {
                title,
                description,
                twitterDescription: storeLocatorRestaurantPage.blocks.head.twitterDescription,
                keywords,
                url,
                snippetImageUrl,
                facebookImageUrl,
                twitterImageUrl,
                locale,
                ...(xUserName && { xUserName }),
                googleAnalytics,
                ...(googleAnalyticsClientId && { googleAnalyticsClientId }),
                organizationName: storeLocatorOrganizationConfig.organization.name,
                isLive: storeLocatorOrganizationConfig.isLive,
                alternatePageUrls,
                microdata,
            };

            const parsedHeadBlock = await storeLocatorHeadBlockValidator.parseAsync(headBlock);

            logger.info('[STORE_LOCATOR] [Head block] Head block is valid, updating it as backup and returning it');
            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorRestaurantPage._id },
                update: { 'blocks.head.backup': parsedHeadBlock },
            });

            return { success: true, data: parsedHeadBlock };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Head block] Failed to fetch store information, try to return backup', { err });

            if (storeLocatorRestaurantPage.blocks?.head?.backup) {
                try {
                    const headBlock = storeLocatorRestaurantPage.blocks.head.backup;
                    const parsedHeadBlock = await storeLocatorHeadBlockValidator.parseAsync(headBlock);

                    return { success: false, data: parsedHeadBlock };
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [Head block] Failed to validate backup', { err: error });
                }
            }

            return { success: false, data: undefined };
        }
    }

    private _getXUserName(restaurant: RestaurantPopulatedForStoreLocator): string | undefined {
        const xProfileUrl = restaurant.socialNetworkUrls?.find(({ key }) => key === SocialNetworkKey.X)?.url;
        return getXUserName(xProfileUrl) ?? undefined;
    }

    private _getAlternateStoreLocatorRestaurantPages({
        storeLocatorRestaurantPages,
        storeLocatorOrganizationConfig,
    }: {
        storeLocatorRestaurantPages?: IStoreLocatorRestaurantPage[];
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
    }): { lang: string; url: string }[] {
        // If only 1 page, no need to link to other localized pages
        if (isNil(storeLocatorRestaurantPages) || storeLocatorRestaurantPages.length <= 1) {
            return [];
        }

        const defaultAlternateStoreLocatorRestaurantPage =
            storeLocatorRestaurantPages.find(({ lang }) => storeLocatorOrganizationConfig.languages.primary === lang) ??
            storeLocatorRestaurantPages.find(({ lang }) => lang === StoreLocatorLanguage.EN) ??
            storeLocatorRestaurantPages.find(({ lang }) => lang === StoreLocatorLanguage.FR);

        return [
            defaultAlternateStoreLocatorRestaurantPage
                ? {
                      lang: 'x-default',
                      url: defaultAlternateStoreLocatorRestaurantPage.fullUrl,
                  }
                : undefined,
            ...(storeLocatorRestaurantPages.map(({ lang, fullUrl }) => ({ lang, url: fullUrl })) ?? []),
        ].filter(isNotNil);
    }
}
