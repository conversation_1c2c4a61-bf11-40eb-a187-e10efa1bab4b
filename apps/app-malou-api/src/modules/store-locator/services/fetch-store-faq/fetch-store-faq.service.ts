import { singleton } from 'tsyringe';

import { GetStoreLocatorStorePageDto, storeLocatorStorePageFaqBlockValidator } from '@malou-io/package-dto';
import { IStoreLocatorRestaurantPage } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
export class FetchStoreLocatorFaqBlockService {
    constructor(private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository) {}

    async execute({
        storeLocatorRestaurantPage,
    }: {
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
    }): Promise<{ success: boolean; data: GetStoreLocatorStorePageDto['faqBlock'] | undefined }> {
        try {
            const faqBlock: GetStoreLocatorStorePageDto['faqBlock'] = {
                title: storeLocatorRestaurantPage.blocks.faq.title.toUpperCase(),
                items: storeLocatorRestaurantPage.blocks.faq.items.map(({ answer, question }) => ({
                    question,
                    answer,
                })),
            };

            const parsedFaqBlock = await storeLocatorStorePageFaqBlockValidator.parseAsync(faqBlock);

            logger.info('[STORE_LOCATOR] [Faq block] Faq block is valid, updating it as backup and returning it');
            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorRestaurantPage._id },
                update: { 'blocks.Faq.backup': parsedFaqBlock },
            });

            return { success: true, data: parsedFaqBlock };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Faq block] Failed to fetch store Faq, try to return backup', { err });

            if (storeLocatorRestaurantPage.blocks?.faq?.backup) {
                try {
                    const faqBlock = storeLocatorRestaurantPage.blocks.faq.backup;
                    const parsedFaqBlock = await storeLocatorStorePageFaqBlockValidator.parseAsync(faqBlock);

                    return { success: false, data: parsedFaqBlock };
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [Faq block] Failed to validate backup', { err: error });
                }
            }

            return { success: false, data: undefined };
        }
    }
}
