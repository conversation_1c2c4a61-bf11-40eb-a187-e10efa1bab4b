import { getUrlDomain, Locale, StoreLocatorLanguage } from '@malou-io/package-utils';

export function getCtaTrackingEventProps(
    cta: { text: string; url: string } | undefined
): { eventName: string; eventLabel: string } | undefined {
    return cta
        ? {
              // todo store-locator: declare an enum and use it here and in store locator
              eventName: 'click-cta',
              eventLabel: getUrlDomain(cta.url) || cta.text.trim().toLowerCase().replace(/\s+/g, '-'),
          }
        : undefined;
}

export function mapStoreLocatorLanguageToLocale(storeLocatorLanguage: StoreLocatorLanguage): string {
    return (
        {
            [StoreLocatorLanguage.FR]: 'fr_FR',
            [StoreLocatorLanguage.EN]: 'en_US',
        }[storeLocatorLanguage] || 'fr_FR'
    );
}

export function mapStoreLocatorLanguageToMalouLocale(storeLocatorLanguage: StoreLocatorLanguage): Locale {
    return (
        {
            [StoreLocatorLanguage.FR]: Locale.FR,
            [StoreLocatorLanguage.EN]: Locale.EN,
        }[storeLocatorLanguage] || Locale.FR
    );
}
