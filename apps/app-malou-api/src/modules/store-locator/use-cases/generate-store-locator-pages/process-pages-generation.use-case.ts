import { singleton } from 'tsyringe';

import { GenerateStorePageService } from ':modules/store-locator/services/generate-store-page/generate-store-page.service';

@singleton()
export class ProcessPagesGenerationUseCase {
    constructor(private readonly _generateStorePageService: GenerateStorePageService) {}

    async execute({ organizationId }: { organizationId: string }): Promise<void> {
        await this._generateStorePageService.generateMissingStorePages({ organizationId });
    }
}
