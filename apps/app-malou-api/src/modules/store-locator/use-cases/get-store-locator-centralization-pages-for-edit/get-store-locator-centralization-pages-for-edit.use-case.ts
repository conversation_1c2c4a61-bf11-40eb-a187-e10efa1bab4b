import { singleton } from 'tsyringe';

import { GetStoreLocatorCentralizationDraftDto } from '@malou-io/package-dto';

import { GetStoreLocatorMapService } from ':modules/store-locator/services/get-store-locator-map/get-store-locator-map.service';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
export class GetStoreLocatorCentralizationPagesForEditUseCase {
    constructor(
        private readonly _getStoreLocatorMapService: GetStoreLocatorMapService,
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository
    ) {}

    async execute(organizationId: string): Promise<GetStoreLocatorCentralizationDraftDto> {
        const storeLocatorOrganizationConfig =
            await this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId);

        const centralizationPages =
            (await this._getStoreLocatorMapService.execute(storeLocatorOrganizationConfig, {
                isForEdit: true,
            })) ?? [];

        return {
            centralizationPages,
        };
    }
}
