import { singleton } from 'tsyringe';

import { GetStoreLocatorDraftPagesDto } from '@malou-io/package-dto';

import { GenerateStorePageService } from ':modules/store-locator/services/generate-store-page/generate-store-page.service';
import { GetStoreLocatorStorePagesForEditService } from ':modules/store-locator/services/get-store-locator-store-pages-for-edit/get-store-locator-store-pages-for-edit.service';

@singleton()
export class GetStoreLocatorStorePagesForEditUseCase {
    constructor(
        private readonly _getStoreLocatorStorePagesForEditService: GetStoreLocatorStorePagesForEditService,
        private readonly _generateStorePageService: GenerateStorePageService
    ) {}

    async execute(organizationId: string): Promise<GetStoreLocatorDraftPagesDto> {
        // todo store-locator move this with popin confirmation later on
        await this._generateStorePageService.generateMissingStorePages({ organizationId });

        return await this._getStoreLocatorStorePagesForEditService.execute(organizationId);
    }
}
