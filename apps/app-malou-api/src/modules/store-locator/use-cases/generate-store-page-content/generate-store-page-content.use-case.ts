import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { GenerateStoreLocatorStorePageContentBodyDto, StoreLocatorStorePageUpdatesDto } from '@malou-io/package-dto';
import { GenerateStoreLocatorContentType, StoreLocatorPageStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { AiStoreLocatorContentType } from ':microservices/ai-store-locator-content-generator.service';
import { GenerateStorePageContentService } from ':modules/store-locator/services/generate-store-page-content/generate-store-page-content.service';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
export default class GenerateStoreLocatorStorePageContentUseCase {
    constructor(
        private readonly _storeLocatorRestaurantPagesRepository: StoreLocatorRestaurantPageRepository,
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _generateStorePageContentService: GenerateStorePageContentService
    ) {}

    async execute<T extends GenerateStoreLocatorContentType>({
        organizationId,
        updates,
        params,
        type,
    }: {
        type: T;
        organizationId: string;
        updates: StoreLocatorStorePageUpdatesDto;
        params?: GenerateStoreLocatorStorePageContentBodyDto['params'];
    }): Promise<AiStoreLocatorContentType<T>> {
        logger.info(`[STORE_LOCATOR] [Generate content] About to generate new content for page`, {
            metadata: {
                organizationId,
                type,
                restaurantId: updates.restaurantId,
                lang: updates.lang,
            },
        });

        const [existingPage, storeLocatorOrganizationConfig] = await Promise.all([
            this._storeLocatorRestaurantPagesRepository.findOne({
                filter: {
                    restaurantId: updates.restaurantId,
                    organizationId,
                    lang: updates.lang,
                    status: StoreLocatorPageStatus.DRAFT,
                },
                options: {
                    lean: true,
                },
            }),
            this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId),
        ]);
        assert(existingPage);
        assert(storeLocatorOrganizationConfig);

        const pageWithModifications = this._storeLocatorRestaurantPagesRepository.toDocumentWithModifications(existingPage, updates);
        const generatedContent = await this._generateStorePageContentService.generateSpecificPageContent({
            type,
            storeLocatorOrganizationConfig,
            storeLocatorRestaurantPage: pageWithModifications,
            lang: updates.lang,
            ...(params && { params }),
        });

        return generatedContent;
    }
}
