import { singleton } from 'tsyringe';

import { CheckForStoreLocatorRestaurantPagesResponseDto } from '@malou-io/package-dto';
import { ReadPreferenceMode, toDbId } from '@malou-io/package-models';
import { BusinessCategory } from '@malou-io/package-utils';

import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
export class CheckForStoreLocatorRestaurantPagesUseCase {
    constructor(
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute(organizationId: string): Promise<CheckForStoreLocatorRestaurantPagesResponseDto> {
        const organizationRestaurantIds = (
            await this._restaurantsRepository.find({
                filter: {
                    organizationId: toDbId(organizationId),
                    active: true,
                    type: BusinessCategory.LOCAL_BUSINESS,
                    shouldNotHaveStoreLocatorPage: false,
                },
                projection: { _id: 1 },
                options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY },
            })
        ).map((restaurant) => restaurant._id.toString());

        const storeLocatorRestaurantPages = await this._storeLocatorRestaurantPageRepository.find({
            filter: { organizationId: toDbId(organizationId) },
            projection: { restaurantId: 1, hasBeenUpdated: 1 },
            options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY },
        });

        const hasAtLeastOnePageGenerated = storeLocatorRestaurantPages.length > 0;
        const hasUpdatedPages = storeLocatorRestaurantPages.some((page) => page.hasBeenUpdated);
        const hasMissingRestaurantPages = organizationRestaurantIds.some(
            (restaurantId) => !storeLocatorRestaurantPages.some((page) => page.restaurantId.toString() === restaurantId)
        );

        return { organizationId, hasAtLeastOnePageGenerated, hasUpdatedPages, hasMissingRestaurantPages };
    }
}
