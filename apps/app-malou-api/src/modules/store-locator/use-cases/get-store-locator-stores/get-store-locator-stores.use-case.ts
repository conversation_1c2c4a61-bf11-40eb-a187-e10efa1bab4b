import { singleton } from 'tsyringe';

import { GetStoreLocatorStorePageDto } from '@malou-io/package-dto';

import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { GetStoreLocatorStoresService } from ':modules/store-locator/services/get-store-locator-stores/get-store-locator-stores.service';

@singleton()
export class GetStoreLocatorStoresUseCase {
    constructor(private readonly _getStoreLocatorStoresService: GetStoreLocatorStoresService) {}

    async execute(storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration): Promise<GetStoreLocatorStorePageDto[]> {
        return await this._getStoreLocatorStoresService.execute(storeLocatorOrganizationConfig);
    }
}
