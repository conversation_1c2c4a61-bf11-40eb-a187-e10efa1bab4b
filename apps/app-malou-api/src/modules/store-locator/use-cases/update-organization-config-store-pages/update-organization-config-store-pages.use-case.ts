import { singleton } from 'tsyringe';

import { UpdateOrganizationConfigurationPagesBodyDto } from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
export default class UpdateOrganizationConfigPagesUseCase {
    constructor(private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository) {}

    async execute({
        organizationId,
        pages,
    }: {
        organizationId: string;
        pages: UpdateOrganizationConfigurationPagesBodyDto;
    }): Promise<void> {
        const storePagesUpdateMap = this._getUpdateMap({ pages, isStore: true });
        const centralizationPagesUpdateMap = this._getUpdateMap({ pages, isStore: false });

        logger.info(`[STORE_LOCATOR] [UPDATE_ORGANIZATION_CONFIG_STORE_PAGES] Updating store pages for organization id`, {
            metadata: {
                organizationId,
                storePagesUpdateMap,
            },
        });

        await this._storeLocatorOrganizationConfigRepository.findOneAndUpdate({
            filter: { organizationId: toDbId(organizationId) },
            update: { ...storePagesUpdateMap, ...centralizationPagesUpdateMap },
        });

        return;
    }

    // returns a record with the appropriate keys and values for the update operation
    // e.g. { 'styles.pages.storeDraft.key1': ['value1'], '
    private _getUpdateMap({
        pages,
        isStore = true,
    }: {
        pages: UpdateOrganizationConfigurationPagesBodyDto;
        isStore: boolean;
    }): Record<string, string[]> {
        // TODO: change naming to centralizationDraft [@hamza]
        const { store, centralization } = pages;
        const base = isStore ? 'styles.pages.storeDraft' : 'styles.pages.mapDraft';
        return Object.entries(isStore ? store : centralization).reduce(
            (acc, [key, value]) => {
                acc[`${base}.${key}`] = value;
                return acc;
            },
            {} as Record<string, string[]>
        );
    }
}
