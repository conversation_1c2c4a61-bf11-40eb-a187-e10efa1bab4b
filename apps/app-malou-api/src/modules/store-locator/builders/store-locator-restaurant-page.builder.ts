import { Builder } from 'builder-pattern';

import { IStoreLocatorRestaurantPage, newDbId } from '@malou-io/package-models';
import { StoreLocatorLanguage, StoreLocatorPageStatus } from '@malou-io/package-utils';

type StoreLocatorRestaurantPagePayload = IStoreLocatorRestaurantPage;

const _buildStoreLocatorRestaurantPageConfig = (storeLocatorRestaurantPage: StoreLocatorRestaurantPagePayload) =>
    Builder<StoreLocatorRestaurantPagePayload>(storeLocatorRestaurantPage);

export const getDefaultStoreLocatorRestaurantPage = () =>
    _buildStoreLocatorRestaurantPageConfig({
        _id: newDbId(),
        organizationId: newDbId(),
        restaurantId: newDbId(),
        createdAt: new Date(),
        updatedAt: new Date(),
        status: StoreLocatorPageStatus.PUBLISHED,
        hasBeenUpdated: true,
        fullUrl: 'https://example.com/restaurant-page',
        lang: StoreLocatorLanguage.EN,
        relativePath: '/restaurant-page',
        blocks: {
            head: {
                title: 'Restaurant Page',
                facebookImageUrl: 'https://example.com/facebook-image.jpg',
                twitterImageUrl: 'https://example.com/twitter-image.jpg',
                description: 'A description of the restaurant page',
                keywords: 'restaurant, food, dining',
                snippetImageUrl: 'https://example.com/snippet-image.jpg',
                twitterDescription: 'A brief description for Twitter',
            },
            information: {
                title: 'Restaurant Name',
                image: {
                    url: 'https://example.com/image.jpg',
                    description: 'A beautiful restaurant image',
                },
                ctas: [
                    {
                        text: 'Book a Table',
                        url: 'https://example.com/book',
                    },
                ],
            },
            gallery: {
                title: 'Gallery',
                subtitle: 'Our Photos',
                images: [],
            },
            reviews: {
                title: 'Reviews',
                cta: {
                    text: 'Leave a Review',
                    url: 'https://example.com/review',
                },
            },
            callToActions: {
                title: 'Actions',
                ctas: [],
            },
            descriptions: {
                items: [],
            },
            socialNetworks: {
                title: 'Social Networks',
            },
            faq: {
                title: 'Frequently Asked Questions',
                items: [],
            },
        },
    });
