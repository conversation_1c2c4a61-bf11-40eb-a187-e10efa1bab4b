import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IReviewWithTranslations } from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    ApplicationLanguage,
    PlatformKey,
    TranslationSource,
} from '@malou-io/package-utils';

import { TranslateTextService } from ':modules/ai/services/translate-text.service';
import { AddTranslationToReviewService } from ':modules/reviews/services/add-translation-to-review.service';

@singleton()
export class TranslateReviewTextService {
    constructor(
        private readonly _translateTextService: TranslateTextService,
        private readonly _addTranslationToReviewService: AddTranslationToReviewService
    ) {}

    async execute({
        review,
        desiredLanguage,
    }: {
        review: Pick<IReviewWithTranslations, '_id' | 'text' | 'restaurantId' | 'lang' | 'translations' | 'key'>;
        desiredLanguage: ApplicationLanguage;
    }): Promise<string | null> {
        const translation = review.translations?.[desiredLanguage as keyof typeof review.translations];

        if (translation) {
            return translation;
        }

        assert(review.text, '[TRANSLATE_REVIEW_TEXT_SERVICE] Review text is not defined');

        const translationResult: string | undefined = await this._translateTextService.execute({
            relatedEntityId: review._id.toString(),
            aiInteractionRelatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
            type: AiInteractionType.REVIEW_TRANSLATION,
            text: review.text,
            lang: desiredLanguage,
            restaurantId: review.restaurantId.toString(),
        });

        if (!translationResult) {
            return null;
        }

        this._addTranslationToReviewService
            .execute({
                reviewId: review._id.toString(),
                translation: translationResult,
                language: desiredLanguage,
                source: TranslationSource.SERVERLESS_AI_TEXT_GENERATOR,
                isPrivateReview: review.key === PlatformKey.PRIVATE,
            })
            .catch((error) => {
                console.error('[TRANSLATE_REVIEW_TEXT_SERVICE] Error adding translation to review:', error);
            });

        return translationResult;
    }
}
