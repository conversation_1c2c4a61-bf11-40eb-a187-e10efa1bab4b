import { groupBy } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import {
    RestaurantPerformanceProps,
    ReviewsStatsPerformanceReportProps,
    reviewsStatsPerformanceReportValidator,
    SimpleRestaurant,
} from '@malou-io/package-dto';
import { IReview, IReviewWithSemanticAnalysis } from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    APP_DEFAULT_LANGUAGE,
    filterByRequiredKeys,
    FilterType,
    getGrowthVariation,
    getPositiveAndNegativeStatsForSemanticAnalysis,
    getReviewsForSemanticAnalysis,
    getSegmentsForSemanticAnalysisStats,
    LANGUAGES,
    roundToDecimals,
} from '@malou-io/package-utils';

import { ReviewFilters } from ':helpers/filters/review-filters';
import { logger } from ':helpers/logger';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';
import PrivateReviewsUseCases from ':modules/private-reviews/private-reviews.use-cases';
import { Report } from ':modules/reports/report.entity';
import { IPerformancePeriods } from ':modules/reports/use-cases/performance-reports/performance-reports.interface';
import { RestaurantReviewsRatingStats } from ':modules/reviews/reviews.interfaces';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import ReviewsUseCases from ':modules/reviews/reviews.use-cases';
import {
    isFeatureAvailableForRestaurant,
    isFeatureAvailableForRestaurants,
} from ':services/experimentations-service/experimentation.service';
import { ReviewsSemanticAnalysisService } from ':services/semantic-analysis/reviews/reviews.semantic-analysis.service';
import { ReviewSemanticAnalysis } from ':services/semantic-analysis/reviews/reviews.semantic-analysis.types';

@singleton()
export class GetPerformanceReviewsSectionUseCase {
    private readonly AVERAGE_RATING_THRESHOLD = 4;

    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _privateReviewsRepository: PrivateReviewsRepository,
        private readonly _reviewsUseCases: ReviewsUseCases,
        private readonly _privateReviewsUseCases: PrivateReviewsUseCases,
        private readonly _reviewsSemanticAnalysisService: ReviewsSemanticAnalysisService
    ) {}

    async execute({
        report,
        config,
        periods,
    }: {
        report: Report;
        config: Report['configurations'][0];
        periods: IPerformancePeriods;
    }): Promise<ReviewsStatsPerformanceReportProps | undefined> {
        const metaData = report.getMetaDataFromConfig(config);

        try {
            const { restaurants } = config;
            const restaurantIds = restaurants.map(({ _id }) => _id);

            const filters = new ReviewFilters({
                startDate: periods.current.startDate,
                endDate: periods.current.endDate,
                restaurantId: restaurantIds,
            });

            const previousFilters = new ReviewFilters({
                startDate: periods.previous.startDate,
                endDate: periods.previous.endDate,
                restaurantId: restaurantIds,
            });

            // Get platforms, and compute total nb of reviews
            const [
                nbOfConnectedPlatforms,
                totalPublicReviewsCount,
                totalPublicReviewsPreviousCount,
                totalPrivateReviewsCount,
                totalPrivateReviewsPreviousCount,
            ] = await Promise.all([
                this._platformsRepository.countDocuments({
                    filter: { restaurantId: { $in: restaurantIds } },
                }),
                this._reviewsRepository.countDocuments({
                    filter: filters.buildQuery({ filterType: FilterType.REVIEWS }),
                }),
                this._reviewsRepository.countDocuments({
                    filter: previousFilters.buildQuery({ filterType: FilterType.REVIEWS }),
                }),
                this._privateReviewsRepository.countDocuments({
                    filter: filters.buildQuery({ filterType: FilterType.REVIEWS }),
                }),
                this._privateReviewsRepository.countDocuments({
                    filter: previousFilters.buildQuery({ filterType: FilterType.REVIEWS }),
                }),
            ]);

            const totalReviewsCount = totalPublicReviewsCount + totalPrivateReviewsCount;
            const totalReviewsPreviousCount = totalPublicReviewsPreviousCount + totalPrivateReviewsPreviousCount;

            // If no connected platforms, don't display the section
            if (nbOfConnectedPlatforms === 0) {
                return undefined;
            }

            let returnData: ReviewsStatsPerformanceReportProps = {
                totalReviewsCount,
                // Lien totems
                reviewsCta: {
                    // eslint-disable-next-line max-len
                    link: `${process.env.BASE_URL}/restaurants/${restaurants[0]._id}/resources/totems?from_email=${report.type}&clicked_on=no_reviews_cta`,
                },
            };

            // If no reviews, display
            if (totalReviewsCount === 0) {
                reviewsStatsPerformanceReportValidator.parse(returnData);

                return returnData;
            }

            // Else, compute data
            const [
                { count: nonAnsweredPublicReviewsCount },
                { count: nonAnsweredPrivateReviewsCount },
                { topReviewsRestaurants, flopReviewsRestaurants },
                semanticAnalysis,
            ] = await Promise.all([
                this._reviewsUseCases.getUnansweredReviewCount(filters),
                this._privateReviewsUseCases.getUnansweredReviewCount(filters),
                this._getBestAndWorstPerformingRestaurants({ filters, report, config }),
                this._getReviewsSemanticAnalysisForStats({ filters, previousFilters, report, config }),
            ]);

            const nonAnsweredReviewsCount = nonAnsweredPublicReviewsCount + nonAnsweredPrivateReviewsCount;
            // Notice from AI
            let noticeFromAI: string | undefined = undefined;
            if (semanticAnalysis?.reviewAnalyses) {
                noticeFromAI = await this._getReviewsSemanticAnalysisOverviewFromAI({
                    report,
                    config,
                    reviews: semanticAnalysis?.reviewAnalyses,
                });
            }

            returnData = {
                ...returnData,
                ...(noticeFromAI && {
                    cta: {
                        noticeText: noticeFromAI,
                        link: `${report.getBaseUrl(config)}/statistics/e-reputation?from_email=${report.type}&clicked_on=reco_from_ai_cta`,
                    },
                }),
                variation: getGrowthVariation(totalReviewsCount - totalReviewsPreviousCount),
                totalReviewsCount,
                ...(nonAnsweredReviewsCount !== 0 && { nonAnsweredReviewsCount }),
                ...(topReviewsRestaurants && { topReviewsRestaurants }),
                ...(flopReviewsRestaurants && { flopReviewsRestaurants }),
                ...(semanticAnalysis && { semanticAnalysis }),
                reviewsCta: {
                    link: `${report.getBaseUrl(config)}/reputation/reviews?from_email=${report.type}&clicked_on=answer_reviews_cta`,
                },
            };

            reviewsStatsPerformanceReportValidator.parse(returnData);

            return returnData;
        } catch (err) {
            logger.error(`${report.getLogGroup()} Reviews insight section`, {
                err,
                ...metaData,
            });

            return undefined;
        }
    }

    private async _getBestAndWorstPerformingRestaurants({
        filters,
        report,
        config,
    }: {
        filters: ReviewFilters;
        report: Report;
        config: Report['configurations'][0];
    }): Promise<{
        topReviewsRestaurants?: RestaurantPerformanceProps[];
        flopReviewsRestaurants?: RestaurantPerformanceProps[];
    }> {
        const metaData = report.getMetaDataFromConfig(config);

        try {
            const { restaurants } = config;
            const rawResults = await this._reviewsUseCases.getRestaurantsReviewsGroupedByRestaurantIds(filters);
            const filteredRawResults = filterByRequiredKeys(rawResults, ['averageRating']);
            const results = filteredRawResults.map((result) => ({
                ...result,
                averageRating: roundToDecimals(result.averageRating, 1),
            }));

            if (results.length < 2) {
                return {
                    topReviewsRestaurants: undefined,
                    flopReviewsRestaurants: undefined,
                };
            }

            const restaurantsMaxCountInTopFlop = restaurants.length <= 10 ? 1 : 3;

            // Get restaurants with average rating above AVERAGE_RATING_THRESHOLD, sort in descending order
            const topReviewsRestaurants: RestaurantPerformanceProps[] = results
                .filter((result) => result.averageRating >= this.AVERAGE_RATING_THRESHOLD)
                .sort((a, b) => b.averageRating - a.averageRating)
                .slice(0, restaurantsMaxCountInTopFlop)
                .map(this._mapResultToEmailValues(restaurants));

            // Get restaurants with average rating below AVERAGE_RATING_THRESHOLD, sort in ascending order
            const flopReviewsRestaurants = results
                .filter((result) => result.averageRating < this.AVERAGE_RATING_THRESHOLD)
                .sort((a, b) => a.averageRating - b.averageRating)
                .slice(0, restaurantsMaxCountInTopFlop)
                .map(this._mapResultToEmailValues(restaurants));

            return {
                ...(topReviewsRestaurants.length > 0 && { topReviewsRestaurants }),
                ...(flopReviewsRestaurants.length > 0 && { flopReviewsRestaurants }),
            };
        } catch (err) {
            logger.error(`${report.getLogGroup()} Reviews insight section, get top flop restaurants`, {
                err,
                ...metaData,
            });

            return {
                topReviewsRestaurants: undefined,
                flopReviewsRestaurants: undefined,
            };
        }
    }

    private _mapResultToEmailValues =
        (restaurants: SimpleRestaurant[]) =>
        ({ total, restaurant, averageRating }: RestaurantReviewsRatingStats): RestaurantPerformanceProps => {
            const originalRestaurant = restaurants.find(({ _id }) => _id.toString() === restaurant._id.toString());
            assert(originalRestaurant, `[GetPerformanceReviewsSectionUseCase] Restaurant not found for id ${restaurant._id}`);
            return {
                metaData: {
                    image: originalRestaurant.logo,
                    name: originalRestaurant.name,
                    address: originalRestaurant.formattedAddress,
                },
                stats: {
                    totalReviewsCount: total ?? 0,
                    averageNote: averageRating ?? 0,
                },
            };
        };

    private async _getReviewsSemanticAnalysisForStats({
        filters,
        previousFilters,
        report,
        config,
    }: {
        filters: ReviewFilters;
        previousFilters: ReviewFilters;
        report: Report;
        config: Report['configurations'][0];
    }): Promise<
        | (ReviewsStatsPerformanceReportProps['semanticAnalysis'] & {
              reviewAnalyses: Partial<IReview>[];
          })
        | undefined
    > {
        const metaData = report.getMetaDataFromConfig(config);

        try {
            const searchQuery = filters.buildQuery();
            const previousSearchQuery = previousFilters.buildQuery();

            const restaurantIds = filters.restaurantIds;
            assert(restaurantIds && restaurantIds.length > 0, `[GetPerformanceReviewsSectionUseCase] No restaurant ids found`);

            const areSemanticAnalysisFeatureEnabled = await Promise.all(
                restaurantIds.map(async (restaurantId) =>
                    isFeatureAvailableForRestaurant({
                        featureName: 'release-new-semantic-analysis',
                        restaurantId,
                    })
                )
            );
            const isSemanticAnalysisFeatureEnabledForAllRestaurants = areSemanticAnalysisFeatureEnabled.every((isEnabled) => isEnabled);

            const [reviewAnalyses, previousReviewAnalyses] = await Promise.all([
                this._reviewsUseCases.getReviewsWithAnalysis(searchQuery, isSemanticAnalysisFeatureEnabledForAllRestaurants),
                this._reviewsUseCases.getReviewsWithAnalysis(previousSearchQuery, isSemanticAnalysisFeatureEnabledForAllRestaurants),
            ]);

            const reviewAnalysesForStats = await this._getReviewAnalysisForStats(reviewAnalyses);

            if (reviewAnalyses.length === 0 || reviewAnalysesForStats.length === 0) {
                return undefined;
            }

            const previousReviewAnalysesForStats = await this._getReviewAnalysisForStats(previousReviewAnalyses);
            const { positiveSentimentsPercentage } = await this._getPositiveAndNegativePercentagesFromReviewAnalyses(reviewAnalyses);

            // If no previous data, don't display the progression
            if (previousReviewAnalysesForStats.length === 0) {
                return {
                    totalRated: positiveSentimentsPercentage,
                    growth: {
                        rate: 0,
                        variation: getGrowthVariation(0),
                        flipped: false,
                        isSemantic: true,
                        isPercentage: true,
                    },
                    reviewAnalyses,
                };
            }

            const { positiveSentimentsPercentage: positiveSentimentsPreviousPercentage } =
                await this._getPositiveAndNegativePercentagesFromReviewAnalyses(previousReviewAnalyses);
            const positiveSentimentsPercentageEvolution = positiveSentimentsPercentage - positiveSentimentsPreviousPercentage;

            // Compute stats, if evolution of positive sentiments is negative, we display the negative sentiments percentage
            const totalRated =
                positiveSentimentsPercentageEvolution >= 0 ? positiveSentimentsPercentage : 100 - positiveSentimentsPercentage;
            const rate = Math.abs(positiveSentimentsPercentageEvolution);
            const flipped = positiveSentimentsPercentageEvolution < 0;

            return {
                totalRated,
                growth: {
                    rate,
                    variation: getGrowthVariation(positiveSentimentsPercentageEvolution),
                    flipped,
                    isSemantic: true,
                    isPercentage: true,
                },
                reviewAnalyses,
            };
        } catch (err) {
            logger.error(`${report.getLogGroup()} Reviews insight section, get semanticAnalysis`, {
                err,
                ...metaData,
            });

            return undefined;
        }
    }

    private async _getReviewAnalysisForStats(
        reviews: Partial<IReviewWithSemanticAnalysis>[]
    ): Promise<Partial<IReviewWithSemanticAnalysis>[]> {
        const reviewsFromAIAnalysis = getReviewsForSemanticAnalysis(reviews);
        const restaurantsWithSemanticAnalysisFeatureEnabled = await isFeatureAvailableForRestaurants({
            restaurantIds: reviews.map((r) => r.restaurantId?.toString() ?? ''),
            featureName: 'release-new-semantic-analysis',
        });
        const isSemanticAnalysisFeatureEnabled = restaurantsWithSemanticAnalysisFeatureEnabled.length === reviews.length;

        return getSegmentsForSemanticAnalysisStats(reviewsFromAIAnalysis, isSemanticAnalysisFeatureEnabled);
    }

    private async _getPositiveAndNegativePercentagesFromReviewAnalyses(reviews: Partial<IReview>[]): Promise<{
        positiveSentimentsPercentage: number;
        negativeSentimentsPercentage: number;
    }> {
        const reviewsForStats = await this._getReviewAnalysisForStats(reviews);
        const restaurantsWithSemanticAnalysisFeatureEnabled = await isFeatureAvailableForRestaurants({
            restaurantIds: reviews.map((r) => r.restaurantId?.toString() ?? ''),
            featureName: 'release-new-semantic-analysis',
        });
        const isSemanticAnalysisFeatureEnabled = restaurantsWithSemanticAnalysisFeatureEnabled.length === reviews.length;

        const { positiveSentimentsPercentage, negativeSentimentsPercentage } = getPositiveAndNegativeStatsForSemanticAnalysis(
            getSegmentsForSemanticAnalysisStats(reviewsForStats, isSemanticAnalysisFeatureEnabled)
        );

        return { positiveSentimentsPercentage, negativeSentimentsPercentage };
    }

    private async _getReviewsSemanticAnalysisOverviewFromAI({
        reviews,
        report,
        config,
    }: {
        reviews: Partial<IReviewWithSemanticAnalysis>[];
        report: Report;
        config: Report['configurations'][0];
    }): Promise<string | undefined> {
        const metaData = report.getMetaDataFromConfig(config);

        try {
            const { restaurants } = config;
            const reviewsWithRestaurantId: (Partial<IReviewWithSemanticAnalysis> & { restaurantId: string })[] = filterByRequiredKeys(
                reviews,
                ['restaurantId']
            ) as (Partial<IReviewWithSemanticAnalysis> & { restaurantId: string })[];
            const reviewsPerRestaurantId: Record<string, Partial<IReviewWithSemanticAnalysis & { restaurantId: string }>[]> = groupBy(
                reviewsWithRestaurantId,
                (review) => review.restaurantId.toString()
            );
            const restaurantsData = await Promise.all(
                Object.keys(reviewsPerRestaurantId).map(async (restaurantId) => {
                    const restaurant = restaurants.find(({ _id }) => _id.toString() === restaurantId.toString());
                    assert(restaurant, `[GetPerformanceReviewsSectionUseCase] Restaurant not found`);
                    const reviewsForRestaurant = reviewsPerRestaurantId[restaurantId];
                    const reviewsSemanticAnalysis = await this._getReviewsSemanticAnalysis(restaurantId, reviewsForRestaurant);

                    return {
                        restaurantName: restaurant.name,
                        reviewsSemanticAnalysis,
                    };
                })
            );

            const { semanticAnalysisResult } = await this._reviewsSemanticAnalysisService.getSemanticAnalysisOverview({
                collection: AiInteractionRelatedEntityCollection.REPORTS,
                collectionId: metaData.reportId.toString(),
                language: metaData.user?.defaultLanguage ? LANGUAGES[metaData.user.defaultLanguage] : LANGUAGES[APP_DEFAULT_LANGUAGE],
                restaurantsData,
            });

            if (!semanticAnalysisResult || semanticAnalysisResult === '') {
                logger.warn(`${report.getLogGroup()} Reviews insight section, semanticAnalysis from AI returned empty value`, {
                    ...metaData,
                });

                return undefined;
            }

            return semanticAnalysisResult;
        } catch (err) {
            logger.error(`${report.getLogGroup()} Reviews insight section, get semanticAnalysis from AI`, {
                err,
                ...metaData,
            });

            return undefined;
        }
    }

    private async _getReviewsSemanticAnalysis(
        restaurantId: string,
        reviews: Partial<IReviewWithSemanticAnalysis & { restaurantId: string }>[]
    ): Promise<ReviewSemanticAnalysis[]> {
        const isSemanticAnalysisFeatureEnabled = await isFeatureAvailableForRestaurant({
            featureName: 'release-new-semantic-analysis',
            restaurantId,
        });

        if (isSemanticAnalysisFeatureEnabled) {
            return reviews.flatMap(
                (review) =>
                    review.semanticAnalysisSegments?.map(({ category, sentiment, segment }) => ({
                        tag: category,
                        sentiment,
                        originalSegment: segment,
                    })) ?? []
            );
        }
        return reviews.flatMap(
            (review) =>
                review.semanticAnalysis?.segmentAnalyses?.map(({ tag, sentiment, originalSegment }) => ({
                    tag,
                    sentiment,
                    originalSegment,
                })) ?? []
        );
    }
}
