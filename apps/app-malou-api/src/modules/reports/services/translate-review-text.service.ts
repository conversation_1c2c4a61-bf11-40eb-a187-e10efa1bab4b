import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IReviewWithSemanticAnalysisAndTranslations } from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    ApplicationLanguage,
    PlatformKey,
    TranslationSource,
} from '@malou-io/package-utils';

import { TranslateTextService } from ':modules/ai/services/translate-text.service';
import { AddTranslationToReviewService } from ':modules/reviews/services/add-translation-to-review.service';

@singleton()
export class TranslateReviewTextService {
    constructor(
        private readonly _translateTextService: TranslateTextService,
        private readonly _addTranslationToReviewService: AddTranslationToReviewService
    ) {}

    async execute(
        review: IReviewWithSemanticAnalysisAndTranslations,
        user: { _id: string; defaultLanguage: string }
    ): Promise<IReviewWithSemanticAnalysisAndTranslations> {
        const translation = review.translations?.[user.defaultLanguage as keyof typeof review.translations];
        if (translation) {
            return {
                ...review,
                text: translation,
            };
        }
        assert(review.text, '[TranslateReviewTextService] Review text is not defined');
        const translationResult: string | undefined = await this._translateTextService.execute({
            relatedEntityId: review._id.toString(),
            aiInteractionRelatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
            type: AiInteractionType.REVIEW_TRANSLATION,
            text: review.text,
            lang: user.defaultLanguage,
            restaurantId: review.restaurantId.toString(),
            userId: user._id,
        });
        if (!translationResult) {
            return review;
        }

        await this._addTranslationToReviewService.execute({
            reviewId: review._id.toString(),
            translation: translationResult,
            language: user.defaultLanguage as ApplicationLanguage,
            source: TranslationSource.SERVERLESS_AI_TEXT_GENERATOR,
            isPrivateReview: review.key === PlatformKey.PRIVATE,
        });
        return {
            ...review,
            text: translationResult,
        };
    }
}
