import { createDate, PlatformKey, PostInsightEntityType, TimeInMilliseconds } from '@malou-io/package-utils';

import { MappedPostInsight } from ':modules/post-insights/v2/platforms/interface';
import { TiktokPostData } from ':modules/posts/platforms/tiktok/tiktok-post.interface';

export class TiktokPostInsightsMapper {
    static mapToMalouPostInsight({ post, platformSocialId }: { post: TiktokPostData; platformSocialId: string }): MappedPostInsight {
        const isReel = post.duration > 0;
        const entityType = isReel ? PostInsightEntityType.REEL : PostInsightEntityType.POST;
        return {
            socialId: post.id,
            platformKey: PlatformKey.TIKTOK,
            entityType,
            postSocialCreatedAt: post.create_time ? (createDate(post.create_time * TimeInMilliseconds.SECOND) ?? new Date()) : new Date(),
            platformSocialId,
            lastFetchedAt: new Date(),
            data: {
                impressions: isReel ? 0 : post.view_count,
                plays: isReel ? post.view_count : 0,
                likes: post.like_count,
                comments: post.comment_count,
                shares: post.share_count,
                reach: null, // TikTok does not provide reach
                saved: null, // TikTok does not provide saved
            },
        };
    }
}
