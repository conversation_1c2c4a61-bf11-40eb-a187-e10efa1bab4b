import assert from 'node:assert/strict';

import { PlatformKey, PostInsightEntityType } from '@malou-io/package-utils';

import { FacebookApiTypes } from ':modules/credentials/platforms/facebook/facebook.types';
import { MappedPostInsight } from ':modules/post-insights/v2/platforms/interface';
import { FbPostData, InsightType } from ':modules/posts/platforms/facebook/facebook-post.interface';
import {
    FbPostInsightsByIdsResponse,
    FbReelInsightsByIdsResponse,
} from ':providers/meta/facebook/post-insights/facebook-post-insights.interface';

export class FacebookPostInsightsMapper {
    static mapToMalouPostInsight({ post, platformSocialId }: { post: FbPostData; platformSocialId: string }): MappedPostInsight {
        assert(post.id, '[FacebookPostInsightsMapper] Missing id on post');
        assert(post.created_time, '[FacebookPostInsightsMapper] Missing created_time on post');
        return {
            socialId: post.id,
            platformKey: PlatformKey.FACEBOOK,
            entityType: PostInsightEntityType.POST,
            postSocialCreatedAt: new Date(post.created_time),
            platformSocialId,
            lastFetchedAt: new Date(),
            data: {
                shares: post.shares?.count ?? 0,
                impressions: post.insights?.data?.find((d) => d.name === InsightType.POST_IMPRESSIONS)?.values?.[0]?.value ?? 0,
                likes: post.likes?.summary?.total_count ?? 0,
                comments: post.comments?.data?.length || 0,
                reach: null, // Reach is not available for facebook posts
                plays: null, // Plays is not available for facebook posts
                saved: null, // Saved is not available for facebook posts
            },
        };
    }

    static mapToMalouReelInsight({
        reel,
        platformSocialId,
    }: {
        reel: FacebookApiTypes.Reels.GetReelWithInsightsResponse;
        platformSocialId: string;
    }): MappedPostInsight {
        assert(reel.id, 'Missing id on reel');
        assert(reel.created_time, 'Missing created_time on reel');

        const socialActionsReelInsights = reel.video_insights.data.find(FacebookApiTypes.Reels.isSocialActionsReelInsights);
        const impressionsReelInsights = reel.video_insights.data.find(FacebookApiTypes.Reels.isImpressionsReelInsights);
        const likesReelInsights = reel.video_insights.data.find(FacebookApiTypes.Reels.isLikesReelInsights);
        const playsReelInsights = reel.video_insights.data.find(FacebookApiTypes.Reels.isPlaysReelInsights);
        return {
            socialId: reel.id,
            platformKey: PlatformKey.FACEBOOK,
            entityType: PostInsightEntityType.REEL,
            postSocialCreatedAt: new Date(reel.created_time),
            platformSocialId,
            lastFetchedAt: new Date(),
            data: {
                shares: socialActionsReelInsights?.values[0]?.value?.SHARE ?? 0,
                impressions: impressionsReelInsights?.values[0].value ?? 0,
                likes: likesReelInsights?.values[0]?.value?.REACTION_LIKE ?? 0,
                comments: socialActionsReelInsights?.values[0]?.value?.COMMENT ?? 0,
                plays: playsReelInsights?.values[0].value ?? 0,
                reach: null, // Reach is not available for facebook reels
                saved: null, // Saved is not available for facebook reels
            },
        };
    }

    static mapToMalouPostInsightData(data: FbPostInsightsByIdsResponse[number]): MappedPostInsight['data'] {
        assert(data.id, '[FacebookPostInsightsMapper] Missing id on post insights data');
        return {
            shares: data.shares?.count ?? 0,
            impressions: data.insights?.data?.find((d) => d.name === InsightType.POST_IMPRESSIONS)?.values?.[0]?.value ?? 0,
            likes: data.likes?.summary?.total_count ?? 0,
            comments: data.comments?.summary?.total_count ?? 0,
            reach: null, // Reach is not available for facebook posts
            plays: null, // Plays is not available for facebook posts
            saved: null, // Saved is not available for facebook posts
        };
    }

    static mapToMalouReelInsightData(data: FbReelInsightsByIdsResponse[number]): MappedPostInsight['data'] {
        assert(data.id, '[FacebookPostInsightsMapper] Missing id on reel insights data');
        const socialActionsReelInsights = data.video_insights.data.find(FacebookApiTypes.Reels.isSocialActionsReelInsights);
        const impressionsReelInsights = data.video_insights.data.find(FacebookApiTypes.Reels.isImpressionsReelInsights);
        const likesReelInsights = data.video_insights.data.find(FacebookApiTypes.Reels.isLikesReelInsights);
        const playsReelInsights = data.video_insights.data.find(FacebookApiTypes.Reels.isPlaysReelInsights);

        return {
            shares: socialActionsReelInsights?.values[0]?.value?.SHARE ?? 0,
            impressions: impressionsReelInsights?.values[0].value ?? 0,
            likes: likesReelInsights?.values[0]?.value?.REACTION_LIKE ?? 0,
            comments: socialActionsReelInsights?.values[0]?.value?.COMMENT ?? 0,
            plays: playsReelInsights?.values[0].value ?? 0,
            reach: null, // Reach is not available for facebook reels
            saved: null, // Saved is not available for facebook reels
        };
    }
}
