import { Builder } from 'builder-pattern';

import { IRegularPostInsight, newDbId } from '@malou-io/package-models';
import { PlatformKey, PostInsightEntityType } from '@malou-io/package-utils';

const _buildPostInsight = (postInsight: IRegularPostInsight) => Builder<IRegularPostInsight>(postInsight);

export const getDefaultPostInsight = () =>
    _buildPostInsight({
        _id: newDbId(),
        platformKey: PlatformKey.INSTAGRAM,
        socialId: 'random_social_id',
        entityType: PostInsightEntityType.POST,
        platformSocialId: 'platform_social_id_123',
        lastFetchedAt: new Date(),
        postSocialCreatedAt: new Date(),
        data: {
            impressions: 1000,
            likes: 50,
            comments: 10,
            shares: 5,
            reach: 800,
            plays: null,
            saved: 3,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
    });
