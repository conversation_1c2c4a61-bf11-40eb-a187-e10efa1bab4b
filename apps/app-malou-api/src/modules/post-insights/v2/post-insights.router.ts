import { Router } from 'express';
import { singleton } from 'tsyringe';

import { PostInsightsController } from ':modules/post-insights/v2/post-insights.controller';
import { authorize } from ':plugins/passport';

@singleton()
export default class PostInsightsRouterV2 {
    private readonly prefix = '/post-insights/v2';

    constructor(private readonly _postsInsightsController: PostInsightsController) {}

    init(router: Router): void {
        router.post(`${this.prefix}/restaurants/:restaurantId`, authorize(), (req, res, next) =>
            this._postsInsightsController.handleGetRestaurantPostInsights(req, res, next)
        );
    }
}
