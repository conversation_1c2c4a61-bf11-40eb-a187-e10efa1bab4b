import { groupBy } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { PlatformPostInsightResponseDto, PostInsightDto } from '@malou-io/package-dto';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PostInsight } from ':modules/post-insights/v2/entities';
import { PostInsightRepository } from ':modules/post-insights/v2/repositories/post-insight.repository';
import { RefreshPlatformPostInsightsService } from ':modules/post-insights/v2/services/refresh-platform-post-insights/refresh-platform-post-insights.service';

@singleton()
export class GetRestaurantPostInsightsUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _postInsightRepository: PostInsightRepository,
        private readonly _refreshPlatformPostInsightsService: RefreshPlatformPostInsightsService
    ) {}

    async execute({
        restaurantId,
        startDate,
        endDate,
        platformKeys,
    }: {
        restaurantId: string;
        startDate: Date;
        endDate: Date;
        platformKeys: PlatformKey[];
    }): Promise<PlatformPostInsightResponseDto[]> {
        const promises = platformKeys.map((platformKey) =>
            this._getPlatformPostInsights({
                restaurantId,
                platformKey,
                startDate,
                endDate,
            }).catch((error) => {
                const mappedError = MalouError.isMalouError(error)
                    ? { code: error.malouErrorCode, message: error.message }
                    : { code: MalouErrorCode.POST_INSIGHTS_UNEXPECTED_ERROR, message: JSON.stringify(error) };

                return { platformKey, error: mappedError, postInsights: [] };
            })
        );

        const result = await Promise.all(promises);
        return result;
    }

    private async _getPlatformPostInsights({
        restaurantId,
        platformKey,
        startDate,
        endDate,
    }: {
        restaurantId: string;
        platformKey: PlatformKey;
        startDate: Date;
        endDate: Date;
    }): Promise<{ platformKey: PlatformKey; postInsights: PostInsightDto[] }> {
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, platformKey);
        if (!platform || !platform.socialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: { restaurantId, key: platformKey, socialId: platform?.socialId },
            });
        }

        const postInsights = await this._postInsightRepository.findByPlatformSocialId({
            platformSocialId: platform.socialId,
            startDate,
            endDate,
        });
        if (!postInsights.length) {
            throw new MalouError(MalouErrorCode.POST_INSIGHTS_NOT_FOUND, {
                metadata: { restaurantId, platformKey, startDate, endDate },
            });
        }

        const postInsightsToRefresh = postInsights.filter((postInsight) => postInsight.shouldBeRefreshed());
        if (!postInsightsToRefresh.length) {
            const postInsightDtos = postInsights.map((postInsight) => postInsight.toDto());
            return { platformKey, postInsights: postInsightDtos };
        }

        const refreshedPostInsights = await this._refreshPlatformPostInsights({
            postInsights: postInsightsToRefresh,
            platform,
        });

        const groupedResfreshedPost = groupBy(refreshedPostInsights, (postInsight) => postInsight.socialId);

        // replace the refreshed insights in the original array
        const updatedPostInsights = postInsights.map((postInsight) => {
            const refreshedInsight = groupedResfreshedPost[postInsight.socialId]?.[0];
            return refreshedInsight ? refreshedInsight : postInsight;
        });
        const postInsightDtos = updatedPostInsights.map((postInsight) => postInsight.toDto());

        return { platformKey, postInsights: postInsightDtos };
    }

    private async _refreshPlatformPostInsights({
        postInsights,
        platform,
    }: {
        postInsights: PostInsight[];
        platform: Platform;
    }): Promise<PostInsight[]> {
        assert(platform.socialId, 'Platform socialId is required for post insights refresh');
        const credentialId = platform.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND, {
                message: 'Credentials not found in instagram synchronize use case',
            });
        }
        switch (platform.key) {
            case PlatformKey.FACEBOOK:
                return this._refreshPlatformPostInsightsService.refreshFbPostInsights({
                    postInsights,
                    platformSocialId: platform.socialId,
                    credentialId: credentialId,
                });
            case PlatformKey.INSTAGRAM:
                return this._refreshPlatformPostInsightsService.refreshIgPostInsights({
                    postInsights,
                    platformSocialId: platform.socialId,
                    credentialId: credentialId,
                });
            case PlatformKey.TIKTOK:
                return this._refreshPlatformPostInsightsService.refreshTiktokPostInsights({
                    postInsights,
                    platformSocialId: platform.socialId,
                    restaurantId: platform.restaurantId.toString(),
                });
            default:
                return [];
        }
    }
}
