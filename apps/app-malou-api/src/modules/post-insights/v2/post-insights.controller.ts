import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    GetRestaurantPostInsightsBodyDto,
    getRestaurantPostInsightsBodyValidator,
    GetRestaurantPostInsightsParamsDto,
    getRestaurantPostInsightsParamsValidator,
    PlatformPostInsightResponseDto,
} from '@malou-io/package-dto';
import { ApiResultV2 } from '@malou-io/package-utils';

import { Body, Params } from ':helpers/decorators/validators';
import { GetRestaurantPostInsightsUseCase } from ':modules/post-insights/v2/use-cases/get-restaurant-post-insights/get-restaurant-post-insights.use-case';

@singleton()
export class PostInsightsController {
    constructor(private readonly _getRestaurantPostInsightsUseCase: GetRestaurantPostInsightsUseCase) {}

    @Params(getRestaurantPostInsightsParamsValidator)
    @Body(getRestaurantPostInsightsBodyValidator)
    async handleGetRestaurantPostInsights(
        req: Request<GetRestaurantPostInsightsParamsDto, never, GetRestaurantPostInsightsBodyDto>,
        res: Response<ApiResultV2<PlatformPostInsightResponseDto[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { startDate, endDate, platformKeys } = req.body;

            const postInsights = await this._getRestaurantPostInsightsUseCase.execute({
                restaurantId,
                startDate: new Date(startDate),
                endDate: new Date(endDate),
                platformKeys,
            });

            return res.json({ data: postInsights });
        } catch (error) {
            next(error);
        }
    }
}
