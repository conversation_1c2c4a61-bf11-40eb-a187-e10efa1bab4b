import { singleton } from 'tsyringe';

import { PostInsightEntityType, postsUpdateTexts } from '@malou-io/package-utils';

import { FacebookApiTypes } from ':modules/credentials/platforms/facebook/facebook.types';
import { FacebookPostInsightsMapper } from ':modules/post-insights/v2/platforms/facebook/facebook-post-insights.mapper';
import { InstagramPostInsightMapper } from ':modules/post-insights/v2/platforms/instagram/instagram-post-insights.mapper';
import { TiktokPostInsightsMapper } from ':modules/post-insights/v2/platforms/tiktok/tiktok-post-insights.mapper';
import { PostInsightRepository } from ':modules/post-insights/v2/repositories/post-insight.repository';
import { FbPostData } from ':modules/posts/platforms/facebook/facebook-post.interface';
import { IgMediaProductType, IgPostData } from ':modules/posts/platforms/instagram/instagram-post.interface';
import { TiktokPostData } from ':modules/posts/platforms/tiktok/tiktok-post.interface';

@singleton()
export class UpsertPlatformPostInsightsService {
    constructor(private readonly _postInsightRepository: PostInsightRepository) {}

    async upsertFbPostInsights(params: { posts: FbPostData[]; platformSocialId: string }): Promise<void> {
        const { posts, platformSocialId } = params;
        const mappedPost = posts
            .filter((p) => p.status_type !== 'mobile_status_update' && p.status_type !== 'shared_story' && p.is_published !== false)
            .filter((p) => !p.story || !postsUpdateTexts.FACEBOOK.filter((updateRegExp) => p.story?.match(updateRegExp))?.length)
            .map((post) => FacebookPostInsightsMapper.mapToMalouPostInsight({ post, platformSocialId }));

        await this._postInsightRepository.upsertMany(mappedPost);
    }

    async upsertFbReelInsights(params: {
        reels: FacebookApiTypes.Reels.GetReelWithInsightsResponse[];
        platformSocialId: string;
    }): Promise<void> {
        const { reels, platformSocialId } = params;
        const mappedReels = reels.map((reel) => FacebookPostInsightsMapper.mapToMalouReelInsight({ reel, platformSocialId }));
        await this._postInsightRepository.upsertMany(mappedReels);
    }

    async upsertIgPostInsights(params: { posts: IgPostData[]; platformSocialId: string }): Promise<void> {
        const { posts, platformSocialId } = params;

        const mappedPosts = posts.map((post) =>
            InstagramPostInsightMapper.mapToMalouPostInsight({
                post,
                platformSocialId,
                entityType: post.media_product_type === IgMediaProductType.REELS ? PostInsightEntityType.REEL : PostInsightEntityType.POST,
            })
        );

        await this._postInsightRepository.upsertMany(mappedPosts);
    }

    async upsertTiktokPostInsights(params: { posts: TiktokPostData[]; platformSocialId: string }): Promise<void> {
        const { posts, platformSocialId } = params;
        const mappedPosts = posts.map((post) => TiktokPostInsightsMapper.mapToMalouPostInsight({ post, platformSocialId }));

        await this._postInsightRepository.upsertMany(mappedPosts);
    }
}
