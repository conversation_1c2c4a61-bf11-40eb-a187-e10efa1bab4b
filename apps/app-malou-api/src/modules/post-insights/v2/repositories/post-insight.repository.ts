import { singleton } from 'tsyringe';

import { EntityRepository, IRegularPostInsight, ReadPreferenceMode, RegularPostInsightModel } from '@malou-io/package-models';

import { ISourcePost, PostInsight, PostInsightProps } from ':modules/post-insights/v2/entities';
import { DEFAULT_POST_POPULATE_OPTIONS } from ':modules/post-insights/v2/repositories/base-post-insight.repository';

type PostInsightWithPost = IRegularPostInsight & { post: ISourcePost };
@singleton()
export class PostInsightRepository extends EntityRepository<IRegularPostInsight> {
    constructor() {
        super(RegularPostInsightModel);
    }

    async upsertMany(postInsights: Omit<PostInsightProps, 'id' | 'post' | 'createdAt' | 'updatedAt'>[]): Promise<void> {
        const bulkOps = postInsights.map((postInsight) => ({
            updateOne: {
                filter: { socialId: postInsight.socialId, platformSocialId: postInsight.platformSocialId },
                update: { $set: postInsight },
                upsert: true,
            },
        }));

        await this.bulkOperations({ operations: bulkOps });
    }

    async findByPlatformSocialId({
        platformSocialId,
        startDate,
        endDate,
    }: {
        platformSocialId: string;
        startDate: Date;
        endDate: Date;
    }): Promise<PostInsight[]> {
        const docs = (await this.find({
            filter: {
                platformSocialId,
                postSocialCreatedAt: { $gte: new Date(startDate), $lte: new Date(endDate) },
            },
            options: {
                populate: DEFAULT_POST_POPULATE_OPTIONS,
                lean: true,
                readPreference: ReadPreferenceMode.SECONDARY,
            },
        })) as PostInsightWithPost[];

        return docs.filter((doc) => !!doc.post).map((doc) => this.toEntity(doc));
    }

    toEntity(doc: PostInsightWithPost): PostInsight {
        const attachments = doc.post.attachments?.length ? doc.post.attachments : doc.post.socialAttachments;

        return new PostInsight({
            id: doc._id.toString(),
            platformKey: doc.platformKey,
            socialId: doc.socialId,
            entityType: doc.entityType,
            platformSocialId: doc.platformSocialId,
            lastFetchedAt: doc.lastFetchedAt,
            postSocialCreatedAt: doc.postSocialCreatedAt,
            data: {
                ...doc.data,
                reach: doc.data.reach || null,
                plays: doc.data.plays || null,
                saved: doc.data.saved || null,
            },
            post: {
                postType: doc.post.postType,
                socialLink: doc.post.socialLink,
                attachments: attachments || [],
            },
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
        });
    }
}
