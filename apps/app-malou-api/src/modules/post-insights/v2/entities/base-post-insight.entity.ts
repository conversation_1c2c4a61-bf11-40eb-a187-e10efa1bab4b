import { DateTime } from 'luxon';

import { ISocialAttachment } from '@malou-io/package-models';
import { PlatformKey, PostInsightEntityType, PostType } from '@malou-io/package-utils';

export type BasePostInsightProps = {
    id: string;
    platformKey: PlatformKey;
    socialId: string;
    entityType: PostInsightEntityType;
    platformSocialId: string;
    lastFetchedAt: Date;
    postSocialCreatedAt: Date;
    post: Omit<ISourcePost, 'socialAttachments'>;
    createdAt: Date;
    updatedAt: Date;
};
export interface ISourcePost {
    postType: PostType;
    socialLink?: string;
    attachments: ISocialAttachment[];
    socialAttachments?: ISocialAttachment[];
}

export class BasePostInsight {
    // Constant to adjust the refresh ratio (higher value means less frequent refresh)
    static readonly REFRESH_RATIO_MULTIPLIER = 0.2;

    // Posts younger than this threshold are always refreshed to capture active interactions
    static readonly RECENT_POST_THRESHOLD_DAYS = 15;

    id: string;
    platformKey: PlatformKey;
    socialId: string;
    entityType: PostInsightEntityType;
    platformSocialId: string;
    lastFetchedAt: Date;
    postSocialCreatedAt: Date;
    post: Omit<ISourcePost, 'socialAttachments'>;
    createdAt: Date;
    updatedAt: Date;

    constructor(props: BasePostInsightProps) {
        this.id = props.id;
        this.platformKey = props.platformKey;
        this.socialId = props.socialId;
        this.entityType = props.entityType;
        this.platformSocialId = props.platformSocialId;
        this.lastFetchedAt = props.lastFetchedAt;
        this.postSocialCreatedAt = props.postSocialCreatedAt;
        this.post = props.post;
        this.createdAt = props.createdAt;
        this.updatedAt = props.updatedAt;
    }

    /**
     * Determines whether a post insight should be refreshed based on adaptive refresh intervals.
     *
     * The refresh logic follows these rules:
     * 1. Recent posts (less than 15 days old) are always refreshed to capture active interactions
     * 2. Older posts use an adaptive refresh interval that increases with post age
     * 3. The refresh interval is calculated using square root of post age to smooth the progression
     * 4. A multiplier constant allows global adjustment of refresh frequency
     *
     * Examples with REFRESH_RATIO_MULTIPLIER = 0.2:
     * - 1 month old post: refresh if not fetched for ~28.1 days
     * - 6 months old post: refresh if not fetched for ~67.3 days
     * - 1 year old post: refresh if not fetched for ~95.6 days
     *
     * @returns true if the post insight should be refreshed, false otherwise
     */
    shouldBeRefreshed(): boolean {
        const now = DateTime.now();
        const postSocialCreatedAtDT = DateTime.fromJSDate(this.postSocialCreatedAt);
        const lastFetchedAtDT = DateTime.fromJSDate(this.lastFetchedAt);

        // For very recent posts (less than RECENT_POST_THRESHOLD_DAYS), always refresh
        if (postSocialCreatedAtDT > now.minus({ days: BasePostInsight.RECENT_POST_THRESHOLD_DAYS })) {
            return true;
        }

        // Calculate post age in days
        const postAgeInDays = now.diff(postSocialCreatedAtDT, 'days').days;

        // Calculate time elapsed since last fetch in days
        const daysSinceLastFetch = now.diff(lastFetchedAtDT, 'days').days;

        // Calculate adaptive refresh interval based on post age
        // Older posts have longer refresh intervals
        const adaptiveRefreshInterval = Math.sqrt(postAgeInDays) / BasePostInsight.REFRESH_RATIO_MULTIPLIER;

        // Refresh if time since last fetch exceeds the adaptive interval
        return daysSinceLastFetch >= adaptiveRefreshInterval;
    }

    /**
     * Calculates the number of days after which a post should be refreshed based on its age and multiplier.
     * This private function helps developers test and choose an appropriate REFRESH_RATIO_MULTIPLIER.
     *
     * @param refreshRatioMultiplier - The multiplier to adjust refresh frequency
     * @param postSocialCreatedAt - The creation date of the post
     * @param _lastFetchedAt - The last fetch date (not used for calculation, kept for API consistency)
     * @returns The number of days after which the post should be refreshed
     */
    private static _calculateRefreshIntervalInDays(refreshRatioMultiplier: number, postSocialCreatedAt: Date): number {
        const now = DateTime.now();
        const postCreatedAtDT = DateTime.fromJSDate(postSocialCreatedAt);

        // Calculate post age in days from now
        const postAgeInDays = now.diff(postCreatedAtDT, 'days').days;

        // For very recent posts (less than RECENT_POST_THRESHOLD_DAYS), return 1 day (always refresh frequently)
        if (postAgeInDays <= BasePostInsight.RECENT_POST_THRESHOLD_DAYS) {
            return 1;
        }

        // Calculate adaptive refresh interval
        return Math.sqrt(postAgeInDays) / refreshRatioMultiplier;
    }
}
