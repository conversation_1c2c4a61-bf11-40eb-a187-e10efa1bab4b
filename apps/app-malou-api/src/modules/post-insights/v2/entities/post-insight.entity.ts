import { PostInsightDto } from '@malou-io/package-dto';
import { MediaType, SocialAttachmentsMediaTypes } from '@malou-io/package-utils';

import { BasePostInsight, BasePostInsightProps } from ':modules/post-insights/v2/entities/base-post-insight.entity';

export interface PostInsightData {
    impressions: number;
    likes: number;
    comments: number;
    shares: number;
    reach: number | null;
    plays: number | null;
    saved: number | null;
}

export type PostInsightProps = BasePostInsightProps & {
    data: PostInsightData;
};

export class PostInsight extends BasePostInsight {
    data: PostInsightData;

    constructor(props: PostInsightProps) {
        super({ ...props });
        this.data = props.data;
    }

    refreshData(data: PostInsightData): void {
        this.data = data;
        this.lastFetchedAt = new Date();
    }

    toDto(): PostInsightDto {
        return {
            id: this.id,
            platformKey: this.platformKey,
            socialId: this.socialId,
            entityType: this.entityType,
            platformSocialId: this.platformSocialId,
            postSocialCreatedAt: this.postSocialCreatedAt.toISOString(),
            data: {
                impressions: this.data.impressions,
                likes: this.data.likes,
                comments: this.data.comments,
                shares: this.data.shares,
                reach: this.data.reach,
                plays: this.data.plays,
                saved: this.data.saved,
            },
            post: {
                postType: this.post.postType,
                socialLink: this.post.socialLink,
                attachments: this.post.attachments.map((attachment) => ({
                    socialId: attachment.socialId || null,
                    thumbnailUrl: attachment.thumbnailUrl || null,
                    type: this._mapToMediaType(attachment.type),
                    urls: {
                        original: attachment.urls.original,
                    },
                })),
            },
        };
    }

    private _mapToMediaType(type: SocialAttachmentsMediaTypes | undefined): MediaType | undefined {
        switch (type) {
            case SocialAttachmentsMediaTypes.IMAGE:
                return MediaType.PHOTO;
            case SocialAttachmentsMediaTypes.VIDEO:
                return MediaType.VIDEO;
            default:
                return undefined;
        }
    }
}
