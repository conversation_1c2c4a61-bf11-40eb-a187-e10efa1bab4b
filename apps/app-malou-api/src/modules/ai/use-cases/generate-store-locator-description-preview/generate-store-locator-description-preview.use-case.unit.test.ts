import { AssertionError } from 'node:assert/strict';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import {
    BusinessCategory,
    CountryCode,
    GenerateStoreLocatorContentType,
    MalouAttributesEnum,
    MalouErrorCode,
    StoreLocatorAiSettingsLanguageStyle,
    StoreLocatorLanguage,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { AiStoreLocatorContentService, AiStoreLocatorContentType } from ':microservices/ai-store-locator-content-generator.service';
import { GenerateStoreLocatorDescriptionPreviewUseCase } from ':modules/ai/use-cases/generate-store-locator-description-preview/generate-store-locator-description-preview.use-case';
import { getDefaultAttribute, getDefaultRestaurantAttribute } from ':modules/attributes/tests/attribute.builder';
import { getDefaultKeywordTemp } from ':modules/keywords/tests/keyword.builder';
import { getDefaultOrganization } from ':modules/organizations/organization.builder';
import { getDefaultRestaurantKeyword } from ':modules/restaurant-keywords/tests/restaurant-keywords.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultStoreLocatorOrganizationConfig } from ':modules/store-locator/builders/store-locator-organization-config.builder';

class AiStoreLocatorContentServiceMock {
    async generateStoreLocatorContent<T extends GenerateStoreLocatorContentType>(
        _type: T,
        _payload: any,
        _retryCount?: number
    ): Promise<{ aiResponse: AiStoreLocatorContentType<T>; aiInteractionDetails: any[] }> {
        return {
            aiResponse: {
                blocks: [
                    {
                        title: 'Test Block',
                        sections: [
                            {
                                subtitle: 'Test Section',
                                text: 'Generated description preview text',
                            },
                        ],
                    },
                ],
            } as AiStoreLocatorContentType<T>,
            aiInteractionDetails: [],
        };
    }
}

describe('GenerateStoreLocatorDescriptionPreviewUseCase', () => {
    let useCase: GenerateStoreLocatorDescriptionPreviewUseCase;
    let aiServiceMock: AiStoreLocatorContentServiceMock;

    beforeAll(() => {
        registerRepositories([
            'RestaurantsRepository',
            'OrganizationsRepository',
            'RestaurantKeywordsRepository',
            'StoreLocatorOrganizationConfigRepository',
            'AttributesRepository',
            'RestaurantAttributesRepository',
            'KeywordsTempRepository',
        ]);

        aiServiceMock = new AiStoreLocatorContentServiceMock();

        container.registerInstance(AiStoreLocatorContentService, aiServiceMock);
        useCase = container.resolve(GenerateStoreLocatorDescriptionPreviewUseCase);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('execute', () => {
        it('should generate store locator description preview successfully', async () => {
            const testCase = new TestCaseBuilderV2<
                | 'organizations'
                | 'restaurants'
                | 'keywordsTemp'
                | 'restaurantKeywords'
                | 'attributes'
                | 'storeLocatorOrganizationConfigs'
                | 'restaurantAttributes'
            >({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().name('Test Organization').build()];
                        },
                    },
                    attributes: {
                        data() {
                            return [
                                getDefaultAttribute()
                                    .attributeId(MalouAttributesEnum.HAS_SEATING_OUTDOORS)
                                    .attributeName({ fr: 'Terrasse' })
                                    .build(),
                                getDefaultAttribute().attributeId(MalouAttributesEnum.WI_FI).attributeName({ fr: 'Wifi gratuit' }).build(),
                            ];
                        },
                    },
                    storeLocatorOrganizationConfigs: {
                        data(dependencies) {
                            return [
                                getDefaultStoreLocatorOrganizationConfig()
                                    .organizationId(dependencies.organizations()[0]._id)
                                    .aiSettings({
                                        tone: ['inspiring', 'friendly'],
                                        languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                                        attributeIds: [dependencies.attributes()[0].attributeId],
                                        restaurantKeywordIds: [],
                                        specialAttributes: [],
                                    })
                                    .build(),
                            ];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurant()
                                    .name('Test Restaurant')
                                    .organizationId(dependencies.organizations()[0]._id)
                                    .type(BusinessCategory.LOCAL_BUSINESS)
                                    .active(true)
                                    .address({
                                        locality: 'Paris',
                                        postalCode: '75001',
                                        country: 'France',
                                        regionCode: CountryCode.FRANCE,
                                    })
                                    .build(),
                            ];
                        },
                    },
                    restaurantAttributes: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantAttribute()
                                    .attributeId(dependencies.attributes()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp().text('burger végétarien').build(),
                                getDefaultKeywordTemp().text('pizza bio').build(),
                            ];
                        },
                    },
                    restaurantKeywords: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .keywordId(dependencies.keywordsTemp()[0]._id)
                                    .selected(true)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .keywordId(dependencies.keywordsTemp()[1]._id)
                                    .selected(true)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seeds = testCase.getSeededObjects();
            const generateStoreLocatorContentSpy = jest.spyOn(aiServiceMock, 'generateStoreLocatorContent');

            const params = {
                organizationId: seeds.organizations[0]._id.toString(),
                userId: 'user123',
                aiSettings: {
                    tone: ['friendly', 'professional'],
                    languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                    attributeIds: [MalouAttributesEnum.HAS_SEATING_OUTDOORS],
                    restaurantKeywordIds: [seeds.restaurantKeywords[0]._id.toString(), seeds.restaurantKeywords[1]._id.toString()],
                    specialAttributes: [
                        {
                            restaurantId: seeds.restaurants[0]._id.toString(),
                            text: 'Menu végétarien',
                        },
                    ],
                },
            };

            const result = await useCase.execute(params);

            expect(result).toEqual('Generated description preview text');
            expect(generateStoreLocatorContentSpy).toHaveBeenCalledWith(GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_GENERATION, {
                restaurantName: 'Test Restaurant',
                organizationName: 'Test Organization',
                address: {
                    locality: 'Paris',
                    postalCode: '75001',
                },
                keywords: ['burger végétarien', 'pizza bio'],
                brandTone: ['friendly', 'professional', 'formal'],
                bricks: [],
                language: StoreLocatorLanguage.FR,
                restaurantOffers: ['terrasse'],
                specificsDirectives: ['menu végétarien'],
            });
        });

        it('should generate store locator description preview without special attributes', async () => {
            const testCase = new TestCaseBuilderV2<
                | 'organizations'
                | 'restaurants'
                | 'keywordsTemp'
                | 'restaurantKeywords'
                | 'attributes'
                | 'storeLocatorOrganizationConfigs'
                | 'restaurantAttributes'
            >({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().name('Test Organization').build()];
                        },
                    },
                    attributes: {
                        data() {
                            return [
                                getDefaultAttribute()
                                    .attributeId(MalouAttributesEnum.HAS_SEATING_OUTDOORS)
                                    .attributeName({ fr: 'Terrasse' })
                                    .build(),
                                getDefaultAttribute().attributeId(MalouAttributesEnum.WI_FI).attributeName({ fr: 'Wifi gratuit' }).build(),
                            ];
                        },
                    },
                    storeLocatorOrganizationConfigs: {
                        data(dependencies) {
                            return [
                                getDefaultStoreLocatorOrganizationConfig()
                                    .organizationId(dependencies.organizations()[0]._id)
                                    .aiSettings({
                                        tone: ['inspiring', 'friendly'],
                                        languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                                        attributeIds: [dependencies.attributes()[0].attributeId],
                                        restaurantKeywordIds: [],
                                        specialAttributes: [],
                                    })
                                    .build(),
                            ];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurant()
                                    .name('Test Restaurant')
                                    .organizationId(dependencies.organizations()[0]._id)
                                    .type(BusinessCategory.LOCAL_BUSINESS)
                                    .active(true)
                                    .address({
                                        locality: 'Paris',
                                        postalCode: '75001',
                                        country: 'France',
                                        regionCode: CountryCode.FRANCE,
                                    })
                                    .build(),
                            ];
                        },
                    },
                    restaurantAttributes: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantAttribute()
                                    .attributeId(dependencies.attributes()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp().text('burger végétarien').build(),
                                getDefaultKeywordTemp().text('pizza bio').build(),
                            ];
                        },
                    },
                    restaurantKeywords: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .keywordId(dependencies.keywordsTemp()[0]._id)
                                    .selected(true)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .keywordId(dependencies.keywordsTemp()[1]._id)
                                    .selected(true)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seeds = testCase.getSeededObjects();
            const generateStoreLocatorContentSpy = jest.spyOn(aiServiceMock, 'generateStoreLocatorContent');

            const params = {
                organizationId: seeds.organizations[0]._id.toString(),
                userId: 'user123',
                aiSettings: {
                    tone: ['casual'],
                    languageStyle: StoreLocatorAiSettingsLanguageStyle.INFORMAL,
                    attributeIds: [MalouAttributesEnum.HAS_SEATING_OUTDOORS],
                    restaurantKeywordIds: [seeds.restaurantKeywords[0]._id.toString()],
                    specialAttributes: [{ restaurantId: newDbId().toString(), text: 'Spécialité mexicaine' }],
                },
            };

            const result = await useCase.execute(params);

            expect(result).toEqual('Generated description preview text');
            expect(generateStoreLocatorContentSpy).toHaveBeenCalledWith(GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_GENERATION, {
                restaurantName: 'Test Restaurant',
                organizationName: 'Test Organization',
                address: {
                    locality: 'Paris',
                    postalCode: '75001',
                },
                keywords: ['burger végétarien'],
                bricks: [],
                brandTone: ['casual', 'informal'],
                language: StoreLocatorLanguage.FR,
                restaurantOffers: ['terrasse'],
                specificsDirectives: [],
            });
        });

        it('should generate store locator description preview with specific restaurant when restaurantId is provided', async () => {
            const testCase = new TestCaseBuilderV2<
                | 'organizations'
                | 'restaurants'
                | 'keywordsTemp'
                | 'restaurantKeywords'
                | 'attributes'
                | 'storeLocatorOrganizationConfigs'
                | 'restaurantAttributes'
            >({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().name('Test Organization').build()];
                        },
                    },
                    attributes: {
                        data() {
                            return [
                                getDefaultAttribute()
                                    .attributeId(MalouAttributesEnum.HAS_SEATING_OUTDOORS)
                                    .attributeName({ fr: 'Terrasse' })
                                    .build(),
                                getDefaultAttribute().attributeId(MalouAttributesEnum.WI_FI).attributeName({ fr: 'Wifi gratuit' }).build(),
                            ];
                        },
                    },
                    storeLocatorOrganizationConfigs: {
                        data(dependencies) {
                            return [
                                getDefaultStoreLocatorOrganizationConfig()
                                    .organizationId(dependencies.organizations()[0]._id)
                                    .aiSettings({
                                        tone: ['inspiring', 'friendly'],
                                        languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                                        attributeIds: [dependencies.attributes()[0].attributeId],
                                        restaurantKeywordIds: [],
                                        specialAttributes: [],
                                    })
                                    .build(),
                            ];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurant()
                                    .name('Test Restaurant')
                                    .organizationId(dependencies.organizations()[0]._id)
                                    .type(BusinessCategory.LOCAL_BUSINESS)
                                    .active(true)
                                    .address({
                                        locality: 'Paris',
                                        postalCode: '75001',
                                        country: 'France',
                                        regionCode: CountryCode.FRANCE,
                                    })
                                    .build(),
                            ];
                        },
                    },
                    restaurantAttributes: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantAttribute()
                                    .attributeId(dependencies.attributes()[0]._id)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp().text('burger végétarien').build(),
                                getDefaultKeywordTemp().text('pizza bio').build(),
                            ];
                        },
                    },
                    restaurantKeywords: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .keywordId(dependencies.keywordsTemp()[0]._id)
                                    .selected(true)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .keywordId(dependencies.keywordsTemp()[1]._id)
                                    .selected(true)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seeds = testCase.getSeededObjects();
            const generateStoreLocatorContentSpy = jest.spyOn(aiServiceMock, 'generateStoreLocatorContent');

            const params = {
                organizationId: seeds.organizations[0]._id.toString(),
                userId: 'user123',
                aiSettings: {
                    tone: ['casual'],
                    languageStyle: StoreLocatorAiSettingsLanguageStyle.INFORMAL,
                    attributeIds: [MalouAttributesEnum.HAS_SEATING_OUTDOORS],
                    restaurantKeywordIds: [seeds.restaurantKeywords[0]._id.toString()],
                    specialAttributes: [{ restaurantId: seeds.restaurants[0]._id.toString(), text: 'Spécialité mexicaine' }],
                },
            };

            const result = await useCase.execute(params);

            expect(result).toEqual('Generated description preview text');
            expect(generateStoreLocatorContentSpy).toHaveBeenCalledWith(GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_GENERATION, {
                restaurantName: 'Test Restaurant',
                organizationName: 'Test Organization',
                address: {
                    locality: 'Paris',
                    postalCode: '75001',
                },
                keywords: ['burger végétarien'],
                bricks: [],
                brandTone: ['casual', 'informal'],
                language: StoreLocatorLanguage.FR,
                restaurantOffers: ['terrasse'],
                specificsDirectives: ['spécialité mexicaine'],
            });
        });

        it('should throw error when organization is not found', async () => {
            const params = {
                organizationId: newDbId().toString(),
                userId: 'user123',
                aiSettings: {
                    tone: ['friendly'],
                    languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                    attributeIds: [],
                    restaurantKeywordIds: [],
                    specialAttributes: [],
                },
            };

            await expect(useCase.execute(params)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.NOT_FOUND,
                })
            );
        });

        it('should throw error when restaurant is not found', async () => {
            const testCase = new TestCaseBuilderV2<'organizations' | 'storeLocatorOrganizationConfigs'>({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().name('Test Organization').build()];
                        },
                    },
                    storeLocatorOrganizationConfigs: {
                        data(dependencies) {
                            return [
                                getDefaultStoreLocatorOrganizationConfig()
                                    .organizationId(dependencies.organizations()[0]._id)
                                    .aiSettings({
                                        tone: ['inspiring', 'friendly'],
                                        languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                                        attributeIds: [],
                                        restaurantKeywordIds: [],
                                        specialAttributes: [],
                                    })
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seeds = testCase.getSeededObjects();

            const params = {
                organizationId: seeds.organizations[0]._id.toString(),
                userId: 'user123',
                aiSettings: {
                    tone: ['friendly'],
                    languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                    attributeIds: [],
                    restaurantKeywordIds: [],
                    specialAttributes: [],
                },
            };

            await expect(useCase.execute(params)).rejects.toThrow(AssertionError);
        });

        it('should throw error when AI service returns no description preview', async () => {
            const testCase = new TestCaseBuilderV2<'organizations' | 'storeLocatorOrganizationConfigs' | 'restaurants'>({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().name('Test Organization').build()];
                        },
                    },
                    storeLocatorOrganizationConfigs: {
                        data(dependencies) {
                            return [
                                getDefaultStoreLocatorOrganizationConfig()
                                    .organizationId(dependencies.organizations()[0]._id)
                                    .aiSettings({
                                        tone: ['inspiring', 'friendly'],
                                        languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                                        attributeIds: [],
                                        restaurantKeywordIds: [],
                                        specialAttributes: [],
                                    })
                                    .build(),
                            ];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurant()
                                    .name('Test Restaurant')
                                    .organizationId(dependencies.organizations()[0]._id)
                                    .type(BusinessCategory.LOCAL_BUSINESS)
                                    .active(true)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seeds = testCase.getSeededObjects();

            jest.spyOn(aiServiceMock, 'generateStoreLocatorContent').mockResolvedValueOnce({
                aiResponse: { blocks: [] },
                aiInteractionDetails: [],
            });

            const params = {
                organizationId: seeds.organizations[0]._id.toString(),
                userId: 'user123',
                aiSettings: {
                    tone: ['friendly'],
                    languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                    attributeIds: [],
                    restaurantKeywordIds: [],
                    specialAttributes: [],
                },
            };

            await expect(useCase.execute(params)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.AI_REQUEST_FAILED,
                })
            );
        });

        it('should handle empty keywords and attributes arrays', async () => {
            const testCase = new TestCaseBuilderV2<'organizations' | 'storeLocatorOrganizationConfigs' | 'restaurants'>({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().name('Test Organization').build()];
                        },
                    },
                    storeLocatorOrganizationConfigs: {
                        data(dependencies) {
                            return [
                                getDefaultStoreLocatorOrganizationConfig()
                                    .organizationId(dependencies.organizations()[0]._id)
                                    .aiSettings({
                                        tone: ['inspiring', 'friendly'],
                                        languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                                        attributeIds: [],
                                        restaurantKeywordIds: [],
                                        specialAttributes: [],
                                    })
                                    .build(),
                            ];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurant()
                                    .name('Test Restaurant')
                                    .organizationId(dependencies.organizations()[0]._id)
                                    .type(BusinessCategory.LOCAL_BUSINESS)
                                    .active(true)
                                    .address({
                                        locality: 'Marseille',
                                        postalCode: '13001',
                                        country: 'France',
                                        regionCode: CountryCode.FRANCE,
                                    })
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seeds = testCase.getSeededObjects();
            const generateStoreLocatorContentSpy = jest.spyOn(aiServiceMock, 'generateStoreLocatorContent');

            const params = {
                organizationId: seeds.organizations[0]._id.toString(),
                userId: 'user123',
                aiSettings: {
                    tone: ['neutral'],
                    languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                    attributeIds: [],
                    restaurantKeywordIds: [],
                    specialAttributes: [],
                },
            };

            const result = await useCase.execute(params);

            expect(result).toEqual('Generated description preview text');
            expect(generateStoreLocatorContentSpy).toHaveBeenCalledWith(GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_GENERATION, {
                restaurantName: 'Test Restaurant',
                organizationName: 'Test Organization',
                address: {
                    locality: 'Marseille',
                    postalCode: '13001',
                },
                keywords: [],
                bricks: [],
                brandTone: ['neutral', 'formal'],
                language: StoreLocatorLanguage.FR,
                restaurantOffers: [],
                specificsDirectives: [],
            });
        });
    });
});
