import { sample } from 'lodash';
import { singleton } from 'tsyringe';

import {
    AI_HARD_LIMIT_CALL_COUNT,
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    ApplicationLanguage,
    getApplicationLanguageDisplayName,
    MalouErrorCode,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { AiReviewsService } from ':microservices/ai-reviews.service';
import { AiTextGenerationService } from ':microservices/ai-text-generation.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { assertRestaurantCanMakeAiCall } from ':modules/ai/helpers/assert-restaurant-can-make-ai-call.helper';
import { TranslateTextPayload } from ':modules/ai/interfaces/ai.interfaces';
import { AiCompletionMapper } from ':modules/ai/mappers/ai-completion.mapper';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { RestaurantAiSettingsRepository } from ':modules/restaurant-ai-settings/restaurant-ai-settings.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class TranslateTextService {
    constructor(
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _aiTextGenerationService: AiTextGenerationService<TranslateTextPayload>,
        private readonly _aiReviewsService: AiReviewsService<TranslateTextPayload>,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _aiCompletionMapper: AiCompletionMapper,
        private readonly _restaurantAiSettingsRepository: RestaurantAiSettingsRepository,
        private readonly _postsRepository: PostsRepository
    ) {}

    async execute({
        relatedEntityId,
        aiInteractionRelatedEntityCollection,
        type,
        text,
        lang,
        restaurantId,
        userId,
    }: {
        relatedEntityId?: string;
        aiInteractionRelatedEntityCollection: AiInteractionRelatedEntityCollection;
        type: AiInteractionType;
        text: string;
        lang?: ApplicationLanguage | string;
        restaurantId: string;
        userId?: string;
    }): Promise<string> {
        let bindingId: string | undefined;
        if (relatedEntityId && aiInteractionRelatedEntityCollection === AiInteractionRelatedEntityCollection.POSTS) {
            const post = await this._postsRepository.findById(relatedEntityId);
            bindingId = post?.bindingId;
        }
        const aiInteraction = await this._aiInteractionsRepository.createAiInteraction({
            type,
            relatedEntityCollection: aiInteractionRelatedEntityCollection,
            relatedEntityId,
            relatedEntityBindingId: bindingId,
            userId,
        });

        try {
            const restaurant = await this._restaurantsRepository.getRestaurantById(restaurantId);

            if (!restaurant) {
                logger.warn('[AI_USE_CASE] Restaurant not found', { restaurantId });
                throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, { message: 'Restaurant not found', metadata: { restaurantId } });
            }

            assertRestaurantCanMakeAiCall(restaurant, AI_HARD_LIMIT_CALL_COUNT);

            const payload = await this._computePayload({
                text,
                language: lang,
                relatedEntityCollection: aiInteractionRelatedEntityCollection,
                type,
                restaurantName: restaurant.name,
                restaurantId: restaurantId.toString(),
            });

            const { aiResponse, aiInteractionDetails } = await this._getTranslateService(type).generateCompletion(payload);

            await this._restaurantsRepository.incrementRestaurantAiCallCount({ restaurantId: restaurant._id, feature: type });
            const updatedAiInteraction = this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(
                aiInteractionDetails?.[0],
                restaurant._id
            );

            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction.id },
                update: updatedAiInteraction,
                options: { new: true },
            });

            return typeof aiResponse === 'string' ? aiResponse : JSON.stringify(aiResponse);
        } catch (error: any) {
            logger.error('[AiUseCases] [translateText] Error', { error: error.stack, relatedEntityId, text, lang, restaurantId });
            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction.id },
                update: {
                    error: {
                        malouErrorCode: error?.malouErrorCode,
                        message: error?.message,
                        stack: error?.stack,
                    },
                },
            });
            throw error;
        }
    }

    private _getTranslateService(
        type: AiInteractionType
    ): AiReviewsService<TranslateTextPayload> | AiTextGenerationService<TranslateTextPayload> {
        switch (type) {
            case AiInteractionType.REVIEW_ANSWER_TRANSLATION:
            case AiInteractionType.REVIEW_TRANSLATION:
            case AiInteractionType.INTRODUCTIVE_TRANSLATION:
            case AiInteractionType.SIGNATURE_TRANSLATION:
                return this._aiReviewsService;
            default:
                return this._aiTextGenerationService;
        }
    }

    private async _computePayload({
        text,
        language,
        relatedEntityCollection,
        type,
        restaurantName,
        restaurantId,
    }: {
        text: string;
        language?: ApplicationLanguage | string;
        relatedEntityCollection: AiInteractionRelatedEntityCollection;
        type: AiInteractionType;
        restaurantName: string;
        restaurantId: string;
    }): Promise<TranslateTextPayload> {
        const languageDisplayedName = getApplicationLanguageDisplayName(language, 'en') ?? language ?? ApplicationLanguage.EN;
        switch (type) {
            case AiInteractionType.REVIEW_ANSWER_TRANSLATION:
                const restaurantAiSettings =
                    await this._restaurantAiSettingsRepository.getRestaurantAiSettingsByRestaurantIdWithTranslations(restaurantId);
                let catchphrase = restaurantAiSettings?.reviewSettings?.catchphrase ?? '';
                if (restaurantAiSettings?.reviewSettings?.shouldTranslateCatchphrase && catchphrase) {
                    catchphrase = restaurantAiSettings?.reviewSettings?.catchphraseTranslation?.[language ?? ApplicationLanguage.EN] ?? '';
                }

                let signature = sample(restaurantAiSettings?.reviewSettings?.signatures) ?? '';
                if (restaurantAiSettings?.reviewSettings?.shouldTranslateSignature && signature) {
                    const associatedSignature = restaurantAiSettings?.reviewSettings?.signatureTranslations?.find((translation) =>
                        translation.doesMatchOneOfTheTranslations(signature)
                    );
                    signature = associatedSignature?.[language ?? ApplicationLanguage.EN] ?? '';
                }

                return {
                    relatedEntityCollection,
                    type,
                    restaurantData: {
                        text,
                        language: languageDisplayedName,
                        restaurantName: restaurantAiSettings?.restaurantName ?? restaurantName,
                        introduction: catchphrase,
                        signature,
                    },
                };
            case AiInteractionType.SIGNATURE_TRANSLATION:
                const restaurantAiSettingsForSignature =
                    await this._restaurantAiSettingsRepository.getRestaurantAiSettingsByRestaurantIdWithTranslations(restaurantId);
                return {
                    relatedEntityCollection,
                    type,
                    restaurantData: {
                        text,
                        restaurantName: restaurantAiSettingsForSignature?.restaurantName ?? restaurantName,
                    },
                };
            case AiInteractionType.INTRODUCTIVE_TRANSLATION:
                return {
                    relatedEntityCollection,
                    type,
                    restaurantData: {
                        text,
                    },
                };
            default:
                return {
                    relatedEntityCollection,
                    type,
                    restaurantData: {
                        text,
                        language: languageDisplayedName,
                    },
                };
        }
    }
}
