import { MalouErrorCode } from '@malou-io/package-utils';

import { metricsService } from ':services/metrics.service';

interface MalouErrorOptions {
    message?: string;
    metadata?: any;
}

const errorCounter = metricsService.getMeter().createCounter<{
    code: MalouErrorCode;
}>('app.errors.malou.count', {
    description: 'Number of MalouError',
});

export class MalouError extends Error {
    malouErrorCode: MalouErrorCode;
    metadata?: any;

    constructor(malouErrorCode: MalouErrorCode, options?: MalouErrorOptions) {
        super(options?.message ?? malouErrorCode);
        this.malouErrorCode = malouErrorCode;
        this.metadata = options?.metadata;

        errorCounter.add(1, { code: malouErrorCode });
    }

    static isMalouError(error: unknown): error is MalouError {
        return error instanceof MalouError && !!error.malouErrorCode;
    }
}
