import { chunk } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { FacebookCredentialsRepository } from ':modules/credentials/platforms/facebook/facebook.repository';
import {
    FbPostInsightsByIdsResponse,
    FbReelInsightsByIdsResponse,
} from ':providers/meta/facebook/post-insights/facebook-post-insights.interface';
import { MetaApiProvider } from ':providers/meta/meta-api-provider';

@singleton()
export class FacebookPostInsightsApiProvider {
    constructor(
        private readonly _metaApiProvider: MetaApiProvider,
        private readonly _facebookCredentialsRepository: FacebookCredentialsRepository
    ) {}

    async getPagePostInsightsByIds({
        pageId,
        socialPostIds,
        credentialId,
    }: {
        pageId: string;
        credentialId: string;
        socialPostIds: string[];
    }): Promise<FbPostInsightsByIdsResponse> {
        const chunkSize = 10; // Max IDs per query
        const chunks = chunk(socialPostIds, chunkSize);

        const credential = await this._facebookCredentialsRepository.getCredentialById(credentialId);
        const { access_token: pageAccessToken } = await this._metaApiProvider.refreshPageAccessToken(pageId, credential.userAccessToken);
        assert(pageAccessToken, 'Missing page access token');

        const requests = chunks.map((socialIdsChunk) =>
            this._metaApiProvider.callWithPageAccessToken({
                credential,
                pageId,
                endpoint: '',
                method: 'get',
                params: {
                    ids: socialIdsChunk.join(','),
                    fields: `id,insights.metric(post_impressions),shares,likes.summary(true).limit(0),comments.summary(true).limit(0)`,
                },
                pageAccessToken,
            })
        );

        const results = await Promise.all(requests);
        const combinedResults = results.reduce((acc, res) => ({ ...acc, ...res }), {});

        return combinedResults;
    }

    async getPageReelInsightsByIds({
        pageId,
        socialPostIds,
        credentialId,
    }: {
        pageId: string;
        credentialId: string;
        socialPostIds: string[];
    }): Promise<FbReelInsightsByIdsResponse> {
        const chunkSize = 10; // Max IDs per query
        const chunks = chunk(socialPostIds, chunkSize);

        const credential = await this._facebookCredentialsRepository.getCredentialById(credentialId);
        const { access_token: pageAccessToken } = await this._metaApiProvider.refreshPageAccessToken(pageId, credential.userAccessToken);
        assert(pageAccessToken, 'Missing page access token');

        const requests = chunks.map((socialIdsChunk) =>
            this._metaApiProvider.callWithPageAccessToken({
                credential,
                pageId,
                endpoint: '',
                method: 'get',
                params: {
                    ids: socialIdsChunk.join(','),
                    fields: `id,video_insights.metric(
                        post_video_likes_by_reaction_type,
                        post_impressions_unique,
                        post_video_social_actions,
                        fb_reels_total_plays
                    )`,
                },
                pageAccessToken,
            })
        );

        const results = await Promise.all(requests);
        const combinedResults = results.reduce((acc, res) => ({ ...acc, ...res }), {});

        return combinedResults;
    }
}
