import { FacebookApiTypes } from ':modules/credentials/platforms/facebook/facebook.types';
import { FbPostData } from ':modules/posts/platforms/facebook/facebook-post.interface';

export interface FbPostInsightsByIdsResponse {
    [postId: string]: {
        id: string;
        insights: FbPostData['insights'];
        likes: FbPostData['likes'];
        comments: FbPostData['comments'];
        shares: FbPostData['shares'];
    };
}

export interface FbReelInsightsByIdsResponse {
    [reelId: string]: {
        id: string;
        video_insights: FacebookApiTypes.Reels.GetReelWithInsightsResponse['video_insights'];
    };
}
