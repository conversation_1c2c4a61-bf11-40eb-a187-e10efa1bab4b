import { chunk } from 'lodash';
import { singleton } from 'tsyringe';

import { FacebookCredentialsRepository } from ':modules/credentials/platforms/facebook/facebook.repository';
import * as facebookCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { InstagramPostInsightsByIdsResponse } from ':providers/meta/instagram/post-insights/instagram-post-insights.interface';
import { MetaApiProvider } from ':providers/meta/meta-api-provider';

@singleton()
export class InstagramPostInsightsApiProvider {
    constructor(
        private readonly _metaApiProvider: MetaApiProvider,
        private readonly _facebookCredentialsRepository: FacebookCredentialsRepository
    ) {}

    async getPagePostInsightsByIds({
        pageId,
        socialPostIds,
        credentialId,
    }: {
        pageId: string;
        credentialId: string;
        socialPostIds: string[];
    }): Promise<InstagramPostInsightsByIdsResponse> {
        const chunkSize = 10; // Max IDs per query
        const chunks = chunk(socialPostIds, chunkSize);

        const credential = await this._facebookCredentialsRepository.getCredentialById(credentialId);
        const pageAccessToken = await facebookCredentialsUseCases.getPageAccessTokenBySocialId(credential, pageId);

        const requests = chunks.map((socialIdsChunk) =>
            this._metaApiProvider.callWithPageAccessToken({
                credential,
                pageId,
                endpoint: '',
                method: 'get',
                params: {
                    ids: socialIdsChunk.join(','),
                    fields: `id,insights.metric(impressions,plays,video_views,views,reach,likes,comments,shares,saved),like_count`,
                },
                pageAccessToken,
            })
        );

        const results = await Promise.all(requests);
        const combinedResults = results.reduce((acc, res) => ({ ...acc, ...res }), {});

        return combinedResults;
    }
}
