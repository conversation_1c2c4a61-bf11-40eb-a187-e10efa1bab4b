import { FB as Meta } from 'fb';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { facebookPageAccessTokenErrorCodes } from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { FacebookCredential } from ':modules/credentials/platforms/facebook/entities/facebook-credential.entity';
import { FacebookCredentialsRepository } from ':modules/credentials/platforms/facebook/facebook.repository';
import { RefreshTokenResponse } from ':providers/meta/facebook/facebook-api-provider.interface';
import { MetaApiCallParams } from ':providers/meta/meta-api-provider.interface';

@singleton()
export class MetaApiProvider {
    // 2108006 -> "Contenu multimédia publié avant la conversion en compte business"
    // This error subcode only show up when the endpoint return only bad post
    // If our research contains bad post and good post, this error does not show up, so we can just ignore it
    private readonly MEDIA_ERROR_SUBCODE_TO_IGNORE = 2108006;
    constructor(private readonly _facebookCredentialsRepository: FacebookCredentialsRepository) {
        Meta.options({
            version: Config.platforms.facebook.api.apiVersion,
            appId: Config.platforms.facebook.api.appId,
            redirectUri: Config.platforms.facebook.api.redirectUri,
            timeout: Config.platforms.facebook.api.timeoutThresholdInMs,
        });
    }

    callWithPageAccessToken = async ({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    }: {
        credential: FacebookCredential;
        pageId: string;
        endpoint: string;
        method: string;
        params: Record<string, any>;
        pageAccessToken: string;
    }) => {
        try {
            const res = await this._callMetaApi({
                endpoint,
                method,
                params,
                token: pageAccessToken,
            });
            await this._facebookCredentialsRepository.findOneAndUpdate({
                filter: {
                    _id: toDbId(credential.id),
                    'pageAccess.pageAccessToken': pageAccessToken,
                },
                update: {
                    'pageAccess.$.lastSeenWorking': new Date(),
                },
            });
            return res;
        } catch (error: Error | any) {
            if (this.MEDIA_ERROR_SUBCODE_TO_IGNORE === error.error_subcode) {
                return { data: [] };
            }
            if (!facebookPageAccessTokenErrorCodes.includes(error.code)) {
                try {
                    const res = await this._callMetaApi({
                        // try using directly userAccessToken, it seems that for most endpoints userAccessToken is enough
                        // and it can happen that pageAccessToken does not work even though we have the right authorizations
                        endpoint,
                        method,
                        params,
                        token: credential.userAccessToken,
                    });
                    return res;
                } catch (e: Error | any) {
                    logger.info('[FB_CREDENTIAL] Failed with backup user access token strategy - ', {
                        error: e?.message ?? e,
                        pageId,
                        endpoint,
                        method,
                        params,
                    });
                }
                throw error;
            }

            // if pageId is a fbPageId it will just return the same id
            const fbPageId = credential.getFbPageIdBySocialId(pageId);
            assert(fbPageId, 'Missing fbPageId');

            const res = await this.refreshPageAccessToken(fbPageId, credential.userAccessToken);
            const newPageAccessToken = res.access_token || pageAccessToken;
            if (!res.access_token) {
                logger.error('[FB_CREDENTIAL] Cannot refresh page access token');
            }
            await this._facebookCredentialsRepository.findOneAndUpdate({
                filter: {
                    _id: toDbId(credential.id),
                    'pageAccess.pageAccessToken': pageAccessToken,
                },
                update: {
                    'pageAccess.$.pageAccessToken': newPageAccessToken,
                    'pageAccess.$.lastSeenWorking': new Date(),
                },
            });
            return this._callMetaApi({
                endpoint,
                method,
                params,
                token: newPageAccessToken,
            });
        }
    };

    async refreshPageAccessToken(pageId: string, userAccessToken: string): Promise<RefreshTokenResponse> {
        return this._callMetaApi({
            endpoint: pageId,
            method: 'get',
            params: { fields: 'access_token' },
            token: userAccessToken,
        });
    }

    private _callMetaApi({ endpoint, method, params, token }: MetaApiCallParams): Promise<any> {
        return new Promise((resolve, reject) => {
            Meta.api(endpoint, method, { ...params, access_token: token }, (res) => {
                if (res.error) {
                    reject(res.error);
                }
                resolve(res);
            });
        });
    }
}
