import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';

import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
class UpdateShouldNotHaveStoreLocatorPage {
    constructor(private readonly _restaurantsRepository: RestaurantsRepository) {}

    async execute(): Promise<void> {
        const { modifiedCount } = await this._restaurantsRepository.updateMany({
            filter: {},
            update: { shouldNotHaveStoreLocatorPage: false },
        });

        console.log(`Updated ${modifiedCount} restaurants to have shouldNotHaveStoreLocatorPage set to false.`);

        const rollrollRestaurants = await this._restaurantsRepository.find({
            filter: { organizationId: toDbId('67cf1ef531d778287af0d2ef'), name: /.*ROLLROLL.*/ },
            projection: { _id: 1 },
            options: { lean: true },
        });

        await this._restaurantsRepository.updateMany({
            filter: { _id: { $in: rollrollRestaurants.map((r) => r._id) } },
            update: { $set: { shouldNotHaveStoreLocatorPage: true } },
        });
        console.log(`Updated ${rollrollRestaurants.length} RollRoll restaurants to have shouldNotHaveStoreLocatorPage set to true.`);
    }
}

const task = container.resolve(UpdateShouldNotHaveStoreLocatorPage);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
