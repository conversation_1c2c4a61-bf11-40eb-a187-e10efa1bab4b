import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
class UpdateFaqBlockTask {
    constructor(
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _storeLocatorOrganizationConfigurationRepository: StoreLocatorOrganizationConfigRepository
    ) {}

    async execute(): Promise<void> {
        // We will need it later
        // await this._storeLocatorRestaurantPageRepository.updateMany({
        //     filter: {
        //         organizationId: toDbId(''),
        //     },
        //     update: {
        //         $set: {
        //             'blocks.faq': {
        //                 title: 'Foire aux questions',
        //                 items: [
        //                     {
        //                         question: 'Comment puis-je trouver un restaurant  près de chez moi ?',
        //                         answer: "Vous pouvez utiliser notre outil de localisation de restaurants sur notre site web ou application mobile. Il vous suffit d'entrer votre adresse ou d'activer la géolocalisation pour trouver le restaurant le plus proche.",
        //                     },
        //                     {
        //                         question: "Quels sont les horaires d'ouverture des restaurants ?",
        //                         answer: "Les horaires d'ouverture peuvent varier selon les restaurants. En général, nos restaurants sont ouverts de 11h à 22h du lundi au dimanche. Pour des informations précises, veuillez consulter la page de chaque restaurant sur notre site.",
        //                     },
        //                     {
        //                         question: 'Puis-je réserver une table dans un restaurant ?',
        //                         answer: "Oui, vous pouvez réserver une table dans nos restaurants via notre site web ou application mobile. Il vous suffit de sélectionner le restaurant, la date et l'heure souhaitées, puis de confirmer votre réservation.",
        //                     },
        //                 ],
        //             },
        //         },
        //     },
        //     options: { lean: true },
        // });

        await this._storeLocatorOrganizationConfigurationRepository.updateMany({
            filter: {},
            update: {
                $set: {
                    'styles.pages.store.faq-wrapper': ['bg-primary'],
                    'styles.pages.store.faq-title': ['text-tertiary'],
                    'styles.pages.store.faq-item': ['bg-primary'],
                    'styles.pages.store.faq-item-question': ['text-tertiary'],
                    'styles.pages.store.faq-item-answer': ['text-tertiary'],
                    'styles.pages.store.faq-icon-wrapper': ['bg-white'],
                    'styles.pages.store.faq-icon': ['fill-primary'],
                },
            },
        });
    }
}

const task = container.resolve(UpdateFaqBlockTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
