import { MediaType, PlatformKey, PostInsightEntityType, PostType } from '@malou-io/package-utils';

export interface PostInsightProps {
    id: string;
    platformKey: PlatformKey;
    socialId: string;
    entityType: PostInsightEntityType;
    platformSocialId: string;
    postSocialCreatedAt: string;
    data: {
        impressions: number;
        likes: number;
        comments: number;
        shares: number;
        reach: number | null;
        plays: number | null;
        saved: number | null;
    };
    post: {
        postType: PostType;
        socialLink?: string;
        attachments: {
            socialId: string | null;
            thumbnailUrl: string | null;
            type: MediaType | undefined;
            urls: {
                original: string;
            };
        }[];
    };
}

export class PostInsight {
    id: string;
    platformKey: PlatformKey;
    socialId: string;
    entityType: PostInsightEntityType;
    platformSocialId: string;

    impressions: number;
    likes: number;
    comments: number;
    shares: number;
    reach: number | null;
    plays: number | null;
    saved: number | null;

    postCreatedAt: Date;
    postType: PostType;
    permalink: string;
    thumbnail: string;
    firstAttachmentType: MediaType | undefined;
    mediaUrl: string;

    constructor(data: PostInsightProps) {
        this.id = data.id;
        this.platformKey = data.platformKey;
        this.socialId = data.socialId;
        this.entityType = data.entityType;
        this.platformSocialId = data.platformSocialId;

        this.impressions = data.data.impressions;
        this.likes = data.data.likes;
        this.comments = data.data.comments;
        this.shares = data.data.shares;
        this.reach = data.data.reach;
        this.plays = data.data.plays;
        this.saved = data.data.saved;

        this.postCreatedAt = new Date(data.postSocialCreatedAt);
        this.postType = data.post.postType;
        this.mediaUrl = data.post.attachments[0]?.urls.original || '';
        this.thumbnail = data.post.attachments[0]?.thumbnailUrl || data.post.attachments[0]?.urls.original;
        this.permalink = data.post.socialLink || '';
        this.firstAttachmentType = data.post.attachments[0]?.type;
    }
}
