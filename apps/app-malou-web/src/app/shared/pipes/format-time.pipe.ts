import { DatePipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { DateTime } from 'luxon';

@Pipe({
    name: 'formatTime',
    standalone: true,
})
export class FormatTimePipe implements PipeTransform {
    constructor(
        private readonly _translateService: TranslateService,
        private readonly _datePipe: DatePipe
    ) {}

    transform(timeString: string, shouldConvert?: boolean): string {
        if (shouldConvert === false || this._translateService.currentLang !== 'en') {
            return timeString;
        }

        const [hours, minutes] = timeString.split(':');

        const now = DateTime.now().set({ hour: parseInt(hours, 10), minute: parseInt(minutes, 10) });
        return this._datePipe.transform(now.toJSDate(), 'shortTime') ?? '--:--';
    }
}
