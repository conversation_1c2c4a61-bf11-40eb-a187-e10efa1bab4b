import { Pipe, PipeTransform } from '@angular/core';
import { capitalize } from 'lodash';

@Pipe({
    name: 'StringPipe',
    standalone: true,
})
export class StringPipe implements PipeTransform {
    transform(value: string | undefined, options: { lowerCase?: boolean; upperCase?: boolean; capitalizeFirst?: boolean }): string {
        if (!value) {
            return '';
        }

        let finalValue = value;

        if (options.lowerCase) {
            finalValue = finalValue.toLowerCase();
        }

        if (options.upperCase) {
            finalValue = finalValue.toUpperCase();
        }

        if (options.capitalizeFirst) {
            finalValue = capitalize(finalValue);
        }

        return finalValue;
    }
}
