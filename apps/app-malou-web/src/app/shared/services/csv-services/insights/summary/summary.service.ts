import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { DateTime } from 'luxon';
import { combineLatest, filter, forkJoin, map, Observable, of, switchMap } from 'rxjs';

import { getDateRangeFromMalouComparisonPeriod, isNotNil, MalouComparisonPeriod, PlatformKey } from '@malou-io/package-utils';

import { RestaurantsService } from ':core/services/restaurants.service';
import * as PlatformsReducer from ':modules/platforms/store/platforms.reducer';
import { StatisticsFiltersContext } from ':modules/statistics/filters/filters.context';
import { LightNfc, Restaurant } from ':shared/models';
import { AbstractCsvService, CsvAsStringArrays } from ':shared/services/csv-services/csv-service.abstract';
import { DataToCsvRowMapperService } from ':shared/services/csv-services/insights/summary/data-csv-row-mapper';
import { SummaryCsvInsightsBoosterSectionService } from ':shared/services/csv-services/insights/summary/summary-sections/booster-data-section.service';
import { SummaryCsvInsightsEReputationSectionService } from ':shared/services/csv-services/insights/summary/summary-sections/e-reputation-data-section.service';
import { SummaryCsvInsightsRoiSectionService } from ':shared/services/csv-services/insights/summary/summary-sections/roi-data-section.service';
import { SummaryCsvInsightsSeoSectionService } from ':shared/services/csv-services/insights/summary/summary-sections/seo-data-section.service';
import { SummaryCsvInsightsSocialMediaSectionService } from ':shared/services/csv-services/insights/summary/summary-sections/social-media-data-section.service';
import { SummaryCsvData, SummarySectionsDataFilters } from ':shared/services/csv-services/insights/summary/summary.interface';

interface SummaryCsvInsightsOptions {
    startDate: Date;
    endDate: Date;
    comparisonPeriod: MalouComparisonPeriod;
}

@Injectable({ providedIn: 'root' })
export class SummaryCsvInsightsService extends AbstractCsvService<SummaryCsvData, SummaryCsvInsightsOptions> {
    constructor(
        private readonly _store: Store,
        private readonly _dataToCsvRowMapperService: DataToCsvRowMapperService,
        private readonly _restaurantsService: RestaurantsService,
        private readonly _summaryCsvInsightsSeoSectionService: SummaryCsvInsightsSeoSectionService,
        private readonly _summaryCsvInsightsEReputationSectionService: SummaryCsvInsightsEReputationSectionService,
        private readonly _summaryCsvInsightsBoosterSectionService: SummaryCsvInsightsBoosterSectionService,
        private readonly _statisticsFiltersContext: StatisticsFiltersContext,
        private readonly _summaryCsvInsightsSocialMediaSectionService: SummaryCsvInsightsSocialMediaSectionService,
        private readonly _summaryCsvInsightsRoiSectionService: SummaryCsvInsightsRoiSectionService
    ) {
        super();
    }

    protected _isDataValid(): boolean {
        return true;
    }

    protected override _getData$(options: SummaryCsvInsightsOptions): Observable<SummaryCsvData> {
        const { comparisonPeriod, endDate, startDate } = options;

        return combineLatest([
            this._restaurantsService.restaurantSelected$,
            this._store.select(PlatformsReducer.selectCurrentPlatformKeys),
            this._statisticsFiltersContext.restaurantLightTotems$,
        ]).pipe(
            filter(([restaurant]) => isNotNil(restaurant)),
            map(([restaurant, platformKeys, lightNfcs]: NonNullable<[Restaurant, PlatformKey[], LightNfc[]]>) =>
                this._getFiltersForSummarySections({
                    restaurant,
                    platformKeys,
                    lightNfcs,
                    startDate,
                    endDate,
                    comparisonPeriod,
                })
            ),
            switchMap((summarySectionsFilters) =>
                forkJoin([
                    this._summaryCsvInsightsSeoSectionService.execute(summarySectionsFilters.seo),
                    this._summaryCsvInsightsEReputationSectionService.execute(summarySectionsFilters.eReputation),
                    this._summaryCsvInsightsBoosterSectionService.execute(summarySectionsFilters.booster),
                    this._summaryCsvInsightsSocialMediaSectionService.execute(summarySectionsFilters.socialMedia),
                    this._summaryCsvInsightsRoiSectionService.execute(summarySectionsFilters.roi),
                    of(summarySectionsFilters),
                ])
            ),
            map(([seoSectionData, eReputationSectionData, boosterSectionData, socialMediaSectionData, roiSectionData, filters]) => {
                const formattedDates = this._getFormattedDates(filters);
                return {
                    startDate: formattedDates.startDate,
                    endDate: formattedDates.endDate,
                    previousStartDate: formattedDates.previousStartDate,
                    previousEndDate: formattedDates.previousEndDate,
                    restaurantName: filters.restaurantName,
                    current: {
                        seo: seoSectionData.current,
                        eReputation: eReputationSectionData.current,
                        booster: boosterSectionData.current,
                        socialMedia: socialMediaSectionData.current,
                        roi: roiSectionData.current,
                    },
                    previous: {
                        seo: seoSectionData.previous,
                        eReputation: eReputationSectionData.previous,
                        booster: boosterSectionData.previous,
                        socialMedia: socialMediaSectionData.previous,
                        roi: roiSectionData.previous,
                    },
                };
            })
        );
    }
    protected override _getCsvHeaderRow(data: SummaryCsvData): CsvAsStringArrays[0] {
        return ['Data', `${data.startDate} - ${data.endDate}`, `${data.previousStartDate} - ${data.previousEndDate}`, 'Evolution'];
    }

    protected override _getCsvDataRows(data: SummaryCsvData): CsvAsStringArrays {
        const seoRows = this._dataToCsvRowMapperService.mapSeoSectionDataToCsvRows(data.current.seo, data.previous.seo);
        const eReputationRows = this._dataToCsvRowMapperService.mapEReputationSectionDataToCsvRows(
            data.current.eReputation,
            data.previous.eReputation
        );
        const boosterRows = this._dataToCsvRowMapperService.mapBoosterSectionDataToCsvRows(data.current.booster, data.previous.booster);
        const socialMediaRows = this._dataToCsvRowMapperService.mapSocialMediaSectionDataToCsvRows(
            data.current.socialMedia,
            data.previous.socialMedia
        );
        const roiRows = this._dataToCsvRowMapperService.mapRoiSectionDataToCsvRows(data.current.roi);
        return [[data.restaurantName, '', '', ''], ...seoRows, ...eReputationRows, ...boosterRows, ...socialMediaRows, ...roiRows];
    }

    private _getFiltersForSummarySections({
        restaurant,
        platformKeys,
        startDate,
        lightNfcs,
        endDate,
        comparisonPeriod,
    }: {
        restaurant: Restaurant;
        platformKeys: PlatformKey[];
        lightNfcs: LightNfc[];
        startDate: Date;
        endDate: Date;
        comparisonPeriod: MalouComparisonPeriod;
    }): SummarySectionsDataFilters {
        const startMonthYear = {
            month: startDate.getMonth() + 1,
            year: startDate.getFullYear(),
        };
        const endMonthYear = {
            month: endDate.getMonth() + 1,
            year: endDate.getFullYear(),
        };
        const nbMonths = DateTime.fromJSDate(endDate).diff(DateTime.fromJSDate(startDate), 'months').months;

        const { endDate: comparisonPeriodEndDate, startDate: comparisonPeriodStartDate } = getDateRangeFromMalouComparisonPeriod({
            comparisonPeriod,
            dateFilters: {
                startDate,
                endDate,
            },
            restaurantStartDate: new Date(restaurant.createdAt),
        });

        if (!comparisonPeriodEndDate || !comparisonPeriodStartDate) {
            throw new Error('Invalid comparison period dates');
        }

        const basicFilters = {
            startDate,
            endDate,
            restaurantId: restaurant.id,
            comparisonPeriod,
            comparisonPeriodStartDate,
            comparisonPeriodEndDate,
        };

        return {
            restaurantName: restaurant?.getDisplayName() ?? '',
            startDate,
            endDate,
            comparisonPeriodStartDate,
            comparisonPeriodEndDate,
            seo: {
                ...basicFilters,
                endDate: DateTime.fromJSDate(endDate).minus({ days: 4 }).toJSDate(),
                endMonthYear,
                startMonthYear,
            },
            eReputation: {
                ...basicFilters,
                platformKeys,
            },
            booster: {
                ...basicFilters,
                nfcs: lightNfcs,
            },
            socialMedia: {
                ...basicFilters,
                // for now we only deal with instagram [@hamza]
                platformKeys: [PlatformKey.INSTAGRAM],
                restaurantCreatedAt: new Date(restaurant!.createdAt),
            },
            roi: {
                ...basicFilters,
                nbMonths,
            },
        };
    }

    private _getFormattedDates(
        filters: SummarySectionsDataFilters
    ): Pick<SummaryCsvData, 'startDate' | 'endDate' | 'previousStartDate' | 'previousEndDate'> {
        const { comparisonPeriodStartDate, comparisonPeriodEndDate, startDate, endDate } = filters;
        const startDateLuxon = DateTime.fromJSDate(startDate);
        const endDateLuxon = DateTime.fromJSDate(endDate);
        const comparisonPeriodStartDateLuxon = DateTime.fromJSDate(comparisonPeriodStartDate);
        const comparisonPeriodEndDateLuxon = DateTime.fromJSDate(comparisonPeriodEndDate);
        return {
            startDate: startDateLuxon.toFormat('yyyy-MM-dd'),
            endDate: endDateLuxon.toFormat('yyyy-MM-dd'),
            previousStartDate: comparisonPeriodStartDateLuxon.toFormat('yyyy-MM-dd'),
            previousEndDate: comparisonPeriodEndDateLuxon.toFormat('yyyy-MM-dd'),
        };
    }
}
