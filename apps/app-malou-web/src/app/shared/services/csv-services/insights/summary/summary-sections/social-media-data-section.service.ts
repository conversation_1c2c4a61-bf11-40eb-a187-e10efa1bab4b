import { Injectable } from '@angular/core';
import { compact, last, mean, sum, uniq } from 'lodash';
import { catchError, forkJoin, map, Observable, of } from 'rxjs';

import { DailyPlatformInsights } from '@malou-io/package-dto';
import { AggregationTimeScale, isNotNil, MalouComparisonPeriod, MalouMetric, PlatformKey, PostType } from '@malou-io/package-utils';

import { PostsService } from ':core/services/posts.service';
import { InsightsService } from ':modules/statistics/insights.service';
import { CommunityInsightsChartData } from ':modules/statistics/social-networks/models/community-insight-chart-data';
import { EngagementInsightsChartData } from ':modules/statistics/social-networks/models/engagement-insight-chart-data';
import { getDayMonthYearFromDate } from ':shared/helpers';
import { SocialNetworkInsights } from ':shared/interfaces/social-network-insights.interface';
import { PostsWithInsightsByPlatforms, PostWithInsights } from ':shared/models';
import { SummarySectionsData, SummarySectionsDataFilters } from ':shared/services/csv-services/insights/summary/summary.interface';

@Injectable({ providedIn: 'root' })
export class SummaryCsvInsightsSocialMediaSectionService {
    constructor(
        private readonly _insightsService: InsightsService,
        private readonly _postsService: PostsService
    ) {}

    execute(
        filters: SummarySectionsDataFilters['socialMedia']
    ): Observable<{ current: SummarySectionsData['socialMedia'] | null; previous: SummarySectionsData['socialMedia'] | null }> {
        const { restaurantId, platformKeys, startDate, endDate, comparisonPeriodStartDate, comparisonPeriodEndDate, comparisonPeriod } =
            filters;
        const requestBody = {
            restaurantIds: [restaurantId],
            platformKeys,
            startDate: getDayMonthYearFromDate(startDate),
            endDate: getDayMonthYearFromDate(endDate),
            metrics: [MalouMetric.FOLLOWERS],
        };

        return forkJoin([
            this._insightsService.getStoredInsights(requestBody),
            this._insightsService.getStoredInsights({ ...requestBody, comparisonPeriod }),
            // posts insights
            this._postsService.getPostsWithInsights(restaurantId, platformKeys, startDate, endDate),
            this._postsService.getPostsWithInsights(restaurantId, platformKeys, comparisonPeriodStartDate, comparisonPeriodEndDate),
        ]).pipe(
            map(
                ([
                    currentInsights,
                    previousInsights,
                    currentPostsWithInsightsByPlatformResponseData,
                    previousPostsWithInsightsByPlatformResponseData,
                ]) => {
                    const currentRestaurantInsights = currentInsights.data?.[restaurantId];
                    const previousRestaurantInsights = previousInsights.data?.[restaurantId];

                    const platformInsightsInError = platformKeys.filter(
                        (platform) => currentInsights?.[restaurantId]?.[platform]?.malouErrorCode
                    );
                    if (platformKeys.length === platformInsightsInError.length) {
                        return {
                            current: null,
                            previous: null,
                        };
                    } else {
                        platformKeys.forEach((platform) => {
                            if (platformInsightsInError.includes(platform)) {
                                if (!currentRestaurantInsights?.[platform]?.hasData) {
                                    delete currentRestaurantInsights?.[platform];
                                }
                                if (!previousRestaurantInsights?.[platform]?.hasData) {
                                    delete previousRestaurantInsights?.[platform];
                                }
                            }
                        });

                        const insightsData = this._getInsightsChartDataByTimeScale(
                            currentInsights.data,
                            restaurantId,
                            AggregationTimeScale.BY_DAY
                        );

                        const previousInsightsData = this._getInsightsChartDataByTimeScale(
                            previousInsights.data,
                            restaurantId,
                            AggregationTimeScale.BY_DAY,
                            MalouComparisonPeriod.PREVIOUS_PERIOD
                        );

                        let engagementRate: number | null = null;
                        let impressionCount: number | null = null;

                        let previousEngagementRate: number | null = null;
                        let previousImpressionCount: number | null = null;

                        const currentPostsWithInsightsByPlatform = currentPostsWithInsightsByPlatformResponseData.data;
                        const previousPostsWithInsightsByPlatform = previousPostsWithInsightsByPlatformResponseData.data;

                        const platformsInError: PlatformKey[] = this._getPlatformsInError(currentPostsWithInsightsByPlatform, platformKeys);

                        if (platformKeys.length !== platformsInError.length) {
                            const engagementInsightsChartData = new EngagementInsightsChartData({
                                data: {
                                    postsWithInsightsByPlatforms: currentPostsWithInsightsByPlatform,
                                    followersCountByPlatformAndDay: currentInsights.data[restaurantId]!,
                                },
                                previousData:
                                    previousPostsWithInsightsByPlatform && previousInsights
                                        ? {
                                              postsWithInsightsByPlatforms: previousPostsWithInsightsByPlatform,
                                              followersCountByPlatformAndDay: previousInsights.data[restaurantId]!,
                                          }
                                        : null,
                                startDate: startDate.toString(),
                                endDate: endDate.toString(),
                            });

                            // kpi data
                            engagementRate = this._computeTotalEngagementRate(
                                engagementInsightsChartData.postsWithInsights,
                                currentInsights.data[restaurantId]!
                            );
                            impressionCount = this._computeTotalImpressions(engagementInsightsChartData.postsWithInsights);

                            if (previousPostsWithInsightsByPlatform && previousInsights) {
                                previousEngagementRate = this._computeTotalEngagementRate(
                                    engagementInsightsChartData.previousPostsWithInsights,
                                    previousInsights.data[restaurantId]!
                                );
                                previousImpressionCount = this._computeTotalImpressions(
                                    engagementInsightsChartData.previousPostsWithInsights
                                );
                            }
                        }

                        const followerCount = last(insightsData.followers.filter(isNotNil)) ?? null;
                        const previousFollowerCount = last(previousInsightsData.followers.filter(isNotNil)) ?? null;
                        return {
                            current: {
                                followerCount,
                                engagementRate,
                                impressionCount,
                                postCount: currentPostsWithInsightsByPlatform[0][PlatformKey.INSTAGRAM]?.data.length ?? 0,
                            },
                            previous: {
                                followerCount: previousFollowerCount,
                                engagementRate: previousEngagementRate,
                                impressionCount: previousImpressionCount,
                                postCount: previousPostsWithInsightsByPlatform[0][PlatformKey.INSTAGRAM]?.data.length ?? 0,
                            },
                        };
                    }
                }
            ),
            catchError(() =>
                of({
                    current: null,
                    previous: null,
                })
            )
        );
    }

    private _getInsightsChartDataByTimeScale(
        data: SocialNetworkInsights,
        restaurantId: string,
        aggregationTimeScale: AggregationTimeScale,
        comparisonPeriod?: MalouComparisonPeriod
    ): CommunityInsightsChartData {
        let startDate = data.startDate;
        let endDate = data.endDate;
        if (comparisonPeriod && comparisonPeriod !== MalouComparisonPeriod.PREVIOUS_PERIOD) {
            startDate = data[restaurantId]!.dateFilters!.startDate;
            endDate = data[restaurantId]!.dateFilters!.endDate;
        }
        return new CommunityInsightsChartData({
            data: {
                [PlatformKey.FACEBOOK]: data[restaurantId]?.[PlatformKey.FACEBOOK]?.insights,
                [PlatformKey.INSTAGRAM]: data[restaurantId]?.[PlatformKey.INSTAGRAM]?.insights,
            },
            startDate,
            endDate,
            aggregationTimeScale,
            comparisonPeriod,
        });
    }

    private _getPlatformsInError(
        postsWithInsightsByPlatforms: PostsWithInsightsByPlatforms,
        filteredPlatforms: PlatformKey[]
    ): PlatformKey[] {
        const postsWithInsightsPlatformsError = postsWithInsightsByPlatforms
            .map((postsWithInsightsByPlatform) =>
                Object.entries(postsWithInsightsByPlatform).map(([key, value]) =>
                    value.error && filteredPlatforms.includes(key as PlatformKey) ? (key as PlatformKey) : null
                )
            )
            .flat()
            .filter(Boolean);
        return compact(uniq([...postsWithInsightsPlatformsError]));
    }

    private _computeTotalEngagementRate(
        posts: PostWithInsights[],
        followersInsights: Partial<Record<PlatformKey, DailyPlatformInsights>>
    ): number {
        const engagementRateByPlatforms: number[] = [];
        const platforms = Object.keys(followersInsights) as PlatformKey[];
        for (const platform of platforms) {
            if (!followersInsights[platform]?.hasData || followersInsights[platform]?.malouErrorCode) {
                continue;
            }
            const platformPosts = posts.filter((post) => post.key === platform);
            const platformLastFollowersCount = Object.values(followersInsights[platform]?.insights?.followers!).slice(-1)[0] ?? 0;
            engagementRateByPlatforms.push(this._computeTotalEngagementRateByPlatform(platformPosts, platformLastFollowersCount));
        }
        return mean(engagementRateByPlatforms);
    }

    private _computeTotalEngagementRateByPlatform(platformPosts: PostWithInsights[], platformLastFollowersCount: number): number {
        if (platformPosts.length === 0 || platformLastFollowersCount === 0) {
            return 0;
        }
        const currentTotalEngagement = sum(platformPosts.map((post) => post.getEngagement()));
        return (currentTotalEngagement * 100) / platformLastFollowersCount / platformPosts.length;
    }

    private _computeTotalImpressions(posts: PostWithInsights[]): number {
        const postsImpressions = sum(posts.filter((post) => post.postType !== PostType.REEL).map((post) => post.impressions));
        const reelsImpressions = sum(posts.filter((post) => post.postType === PostType.REEL).map((post) => post.plays));
        return postsImpressions + reelsImpressions;
    }
}
