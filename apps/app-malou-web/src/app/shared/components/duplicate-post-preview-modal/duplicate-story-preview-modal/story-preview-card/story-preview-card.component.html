<form class="flex items-end justify-between gap-3" [formGroup]="postForm()">
    <div class="shrink-1 mb-1 flex min-w-[200px]">
        <ng-container [ngTemplateOutlet]="restaurantInformationsTemplate" [ngTemplateOutletContext]="{ restaurant }"></ng-container>
    </div>

    <div class="flex gap-4">
        @if (!hasPlatformsConnected) {
            <span class="malou-text-10--regular text-malou-color-state-warn">{{
                'posts.duplicate_post_modal.no_platform_connected' | translate
            }}</span>
        } @else {
            <ng-container [ngTemplateOutlet]="platformKeysTemplate"></ng-container>
            <ng-container [ngTemplateOutlet]="postPublicationStatusFormTemplate"></ng-container>
        }
    </div>
</form>

<ng-template #postPublicationStatusFormTemplate>
    <div class="flex flex-col gap-1">
        <div class="flex items-center gap-2" [formGroup]="postForm()">
            <!-- STATUS -->
            <div class="flex h-full flex-col justify-between gap-2" [ngClass]="{ '!justify-end': !shouldDisplayColumnHeader() }">
                @if (shouldDisplayColumnHeader()) {
                    <span class="malou-text-10--regular text-malou-color-text-1">
                        {{ 'stories.duplicate_story_preview_modal.status' | translate }}
                    </span>
                }
                <app-select
                    formControlName="status"
                    [displayWith]="statusDisplayWith"
                    [values]="postDateStatuses"
                    [inputReadOnly]="true"
                    (selectChange)="onStatusChange($event)">
                </app-select>
            </div>

            <!-- DATE -->
            <div class="flex h-full flex-col justify-between gap-2" [ngClass]="{ '!justify-end': !shouldDisplayColumnHeader() }">
                @if (shouldDisplayColumnHeader()) {
                    <span class="malou-text-10--regular text-malou-color-text-1">
                        {{ 'stories.duplicate_story_preview_modal.date' | translate }}
                    </span>
                }
                <app-input-date-picker
                    class="flex w-[130px] cursor-pointer items-baseline gap-x-2 rounded-[10px] border border-malou-color-border-primary text-malou-color-text-1"
                    formControlName="plannedPublicationDate"
                    [ownInputValidation]="true"
                    [min]="MIN_DATE"
                    [shouldDisplayBorder]="false"
                    [manualInputAvailable]="false"></app-input-date-picker>
            </div>

            <!-- TIME -->
            <div class="flex flex-col gap-2">
                @if (shouldDisplayColumnHeader()) {
                    <span class="malou-text-10--regular text-malou-color-text-1">
                        {{ 'stories.duplicate_story_preview_modal.time' | translate }}
                    </span>
                }
                <mat-select
                    class="!m-0 mt-8 !h-0 bg-malou-color-background-light !opacity-0"
                    panelClass="malou-select-panel"
                    formControlName="postTime"
                    [hideSingleSelectionIndicator]="true"
                    (selectionChange)="onChangeTime($event)">
                    @for (time of TIMES; track time) {
                        <mat-option
                            [value]="time"
                            [disabled]="
                                isPastHour
                                    | applyPure
                                        : {
                                              hourWithMinute: time,
                                              date: postForm().get('plannedPublicationDate')?.value,
                                          }
                            ">
                            {{ time | formatTime: currentLang() === 'en' }}
                        </mat-option>
                    }
                </mat-select>
                <app-input-text class="grow" formControlName="postTime" inputType="time" [svgIcon]="SvgIcon.CLOCK"></app-input-text>
            </div>
        </div>

        @if (postForm().errors?.publicationDateInTheFuture) {
            <div class="malou-text-10 malou-color-state-error py-1 italic">{{ 'common.date_past' | translate }}</div>
        }
    </div>
</ng-template>

<ng-template let-restaurant="restaurant" #restaurantInformationsTemplate>
    <div class="flex items-center gap-x-2">
        <div class="shrink-0 self-start">
            <div class="relative">
                <img
                    class="malou-avatar--small !rounded-md"
                    [src]="(restaurant.logo | applySelfPure: 'getMediaUrl' : 'small') || ('default_logo' | imagePathResolver)" />
            </div>
        </div>
        <div class="min-w-0 grow">
            <div class="flex flex-col">
                <div
                    class="malou-text-13--semibold malou-color-text-1 truncate"
                    [matTooltip]="restaurant.internalName || restaurant.name"
                    [matTooltipShowDelay]="700">
                    {{ restaurant.internalName || restaurant.name }}
                </div>
                <div
                    class="malou-text-10--regular malou-color-text-2 truncate italic"
                    [matTooltip]="(restaurant | applySelfPure: 'getFullFormattedAddress') ?? ''"
                    [matTooltipShowDelay]="700">
                    {{ restaurant | applySelfPure: 'getFullFormattedAddress' }}
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #platformKeysTemplate>
    <div class="flex flex-col justify-between gap-2" [ngClass]="{ '!justify-end': !shouldDisplayColumnHeader() }">
        @if (shouldDisplayColumnHeader()) {
            <span class="malou-text-10--regular text-malou-color-text-1">
                {{ 'stories.duplicate_story_preview_modal.platforms' | translate }}
            </span>
        }
        <div class="mb-[10px] flex gap-1">
            @for (platformKey of keys; track platformKey; let index = $index) {
                <app-platform-logo imgClasses="h-7 w-7 max-w-fit" [logo]="platformKey"></app-platform-logo>
            }
            @for (hiddenPlatformKey of hiddenPlatformKeys(); track hiddenPlatformKey; let index = $index) {
                <app-platform-logo imgClasses="h-7 w-7 max-w-fit invisible" [logo]="hiddenPlatformKey"></app-platform-logo>
            }
        </div>
    </div>
</ng-template>
