<div class="flex h-full p-7">
    @if (isLoading()) {
        <div class="flex h-full w-full items-center justify-center">
            <app-malou-spinner></app-malou-spinner>
        </div>
    } @else {
        <div class="flex w-full flex-col gap-4">
            @for (
                postForm of previewCaptionPostForms.controls;
                track postForm;
                let isLast = $last;
                let isFirst = $first;
                let index = $index
            ) {
                <app-story-preview-card
                    [postForm]="postForm"
                    [shouldDisplayColumnHeader]="isFirst"
                    [numberOfPlatformKeysToDisplay]="maxNumberOfPlatformKeys()"></app-story-preview-card>
                @if (!isLast) {
                    <mat-divider class="!-mb-2 !border-malou-color-border-primary"></mat-divider>
                }
            }
        </div>
    }
</div>
