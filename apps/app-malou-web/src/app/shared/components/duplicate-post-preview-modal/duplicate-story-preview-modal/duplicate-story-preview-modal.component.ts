import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';
import { forkJoin, of } from 'rxjs';

import { PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { RestaurantsService } from ':core/services/restaurants.service';
import { PostDateStatus } from ':modules/posts/new-post-modal/types';
import { StoryToDuplicate } from ':modules/stories/v2/models/story-to-duplicate';
import { StoriesService } from ':modules/stories/v2/stories.service';
import { StoryPreviewCardComponent } from ':shared/components/duplicate-post-preview-modal/duplicate-story-preview-modal/story-preview-card/story-preview-card.component';
import { roundUpTimeToNextQuarterHour } from ':shared/components/duplicate-post-preview-modal/helpers/plus-15-minutes-date';
import { onlyFutureDate } from ':shared/components/duplicate-post-preview-modal/validators/publication-date-in-the-future';
import { BaseStepComponent } from ':shared/components/stepper-modal/base-step.component';
import { isPastHour } from ':shared/helpers/date';
import { Restaurant } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { Illustration } from ':shared/pipes/illustration-path-resolver.pipe';

export interface DuplicateStoryInputData {
    selectedRestaurants: Restaurant[];
    index: number;
}

interface SharedData {
    storiesToDuplicate: StoryToDuplicate[];
    isDirectlyAfterUpsertPostModal?: boolean;
}

export interface DuplicateStoryForm {
    plannedPublicationDate: FormControl<Date>;
    postTime: FormControl<string>;
    keys: FormControl<PlatformKey[]>;
    status: FormControl<PostDateStatus>;
    // wont be visible in the UI
    restaurant: FormControl<Restaurant>;
    hasPlatformsConnected: FormControl<boolean>;
}

const BaseStepDuplicateStoryPreviewComponent = BaseStepComponent<DuplicateStoryInputData, SharedData>;

export interface DuplicateStoryPreviewModalSubmitData {
    restaurant: Restaurant;
    status: PostPublicationStatus;
    plannedPublicationDate?: Date;
    platformKeys: PlatformKey[];
}

@Component({
    selector: 'app-duplicate-story-preview-modal',
    imports: [
        MatTooltipModule,
        MatDividerModule,
        MatIconModule,
        TranslateModule,
        ReactiveFormsModule,
        MatButtonModule,
        MatButtonModule,
        MatIconModule,
        FormsModule,
        MatProgressBarModule,
        MatProgressSpinnerModule,
        MatAutocompleteModule,
        MatOptionModule,
        MatFormFieldModule,
        MatMenuModule,
        MatCheckboxModule,
        MatRadioModule,
        MatTooltipModule,
        MatSelectModule,
        LazyLoadImageModule,
        StoryPreviewCardComponent,
        MalouSpinnerComponent,
    ],
    templateUrl: './duplicate-story-preview-modal.component.html',
    styleUrl: './duplicate-story-preview-modal.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DuplicateStoryPreviewModalComponent extends BaseStepDuplicateStoryPreviewComponent implements OnInit {
    readonly Illustration = Illustration;

    readonly restaurant = inject(RestaurantsService).restaurantSelected$.value;
    private readonly _destroyRef = inject(DestroyRef);
    private readonly _storiesService = inject(StoriesService);

    readonly previewCaptionPostForms = new FormArray<FormGroup<DuplicateStoryForm>>([]);

    readonly isStepValid = signal(false);

    readonly isLoading = signal(true);
    readonly maxNumberOfPlatformKeys = signal(0);

    readonly SvgIcon = SvgIcon;

    constructor() {
        super();
    }

    ngOnInit(): void {
        super.ngOnInit();
        this._initializeFormsValidation();
        this._initializePostForm();
    }

    protected _submitData(): DuplicateStoryPreviewModalSubmitData[] {
        return this.previewCaptionPostForms.controls
            .filter((form) => form.get('hasPlatformsConnected')?.value)
            .map((form) => {
                const { status, plannedPublicationDate } = this._buildPostStatusAndDate(form);

                return {
                    restaurant: form.get('restaurant')!.value,
                    status,
                    plannedPublicationDate,
                    platformKeys: form.get('keys')!.value,
                };
            });
    }

    private _buildPostStatusAndDate(form: FormGroup<DuplicateStoryForm>): { status: PostPublicationStatus; plannedPublicationDate: Date } {
        const plannedPublicationDate = this._buildDate({
            plannedPublicationDate: form.get('plannedPublicationDate')!.value,
            postTime: form.get('postTime')!.value,
        });
        const status = form.get('status')!.value === PostDateStatus.DRAFT ? PostPublicationStatus.DRAFT : PostPublicationStatus.PENDING;

        return { status, plannedPublicationDate };
    }

    protected _isValid(): boolean {
        return this.isStepValid();
    }

    private _buildDate({ plannedPublicationDate, postTime }: { plannedPublicationDate: Date; postTime: string }): Date {
        const date = new Date(plannedPublicationDate);
        const [hours, minutes] = postTime.split(':').map((n) => parseInt(n, 10));
        date.setHours(hours);
        date.setMinutes(minutes);
        return date;
    }

    private _initializePostForm(): void {
        const storyToDuplicate = this.sharedData.storiesToDuplicate[this.inputData.index];
        const postDate = roundUpTimeToNextQuarterHour(storyToDuplicate.plannedPublicationDate ?? undefined);

        const status = storyToDuplicate.published === PostPublicationStatus.PENDING ? PostDateStatus.LATER : PostDateStatus.DRAFT;
        const postTime = postDate.toFormat('HH:mm');
        const plannedPublicationDate = postDate.toJSDate();

        forkJoin(
            this.inputData.selectedRestaurants.map((restaurant) =>
                forkJoin([of(restaurant), this._storiesService.getConnectedStoriesPlatformsForRestaurant$(restaurant._id)])
            )
        ).subscribe((restaurantsWithKeys) => {
            this.maxNumberOfPlatformKeys.set(Math.max(...restaurantsWithKeys.map((data) => data[1].length)));

            this._buildPreviewStoryForms(
                restaurantsWithKeys.map((data) => ({ restaurantId: data[0]._id, keys: data[1] })),
                plannedPublicationDate,
                postTime,
                status
            );
            this.isLoading.set(false);
        });
    }

    private _initializeFormsValidation(): void {
        this.previewCaptionPostForms.valueChanges.pipe(takeUntilDestroyed(this._destroyRef)).subscribe(() => {
            this.isStepValid.set(this._areFormsValid());
            this.valid.emit(this.isStepValid());
        });
    }

    private _areFormsValid(): boolean {
        return this.previewCaptionPostForms.controls.every((postForm) => {
            const plannedPublicationDate = postForm.controls['plannedPublicationDate'].value;
            const postTime = postForm.controls['postTime'].value;
            return !isPastHour({ hourWithMinute: postTime, date: plannedPublicationDate });
        });
    }

    private _buildPreviewStoryForms(
        restaurantIdsWithKeys: { keys: PlatformKey[]; restaurantId: string }[],
        plannedPublicationDate: Date,
        postTime: string,
        status: PostDateStatus
    ): void {
        const restaurantsWithKeys = restaurantIdsWithKeys
            .map((data) => {
                const restaurant = this.inputData.selectedRestaurants.find((rest) => rest._id === data.restaurantId);
                return {
                    restaurant,
                    keys: data.keys,
                };
            })
            .filter((data) => data.restaurant);

        for (const restaurantWithKeys of restaurantsWithKeys) {
            const keys: PlatformKey[] = restaurantWithKeys.keys;
            const hasPlatformsConnected = keys.length > 0;

            const form = new FormGroup(
                {
                    plannedPublicationDate: new FormControl<Date>(plannedPublicationDate, {
                        nonNullable: true,
                    }),
                    postTime: new FormControl(postTime, { nonNullable: true }),
                    keys: new FormControl<PlatformKey[]>(keys, { nonNullable: true }),
                    status: new FormControl<PostDateStatus>(status, { nonNullable: true }),
                    // wont be visible in the UI
                    restaurant: new FormControl<Restaurant>(restaurantWithKeys.restaurant!, { nonNullable: true }),
                    hasPlatformsConnected: new FormControl<boolean>(hasPlatformsConnected, { nonNullable: true }),
                },
                {
                    validators: [onlyFutureDate()],
                }
            );
            this.previewCaptionPostForms.push(form);
            if (!hasPlatformsConnected) {
                form.disable();
            }
        }
    }
}
