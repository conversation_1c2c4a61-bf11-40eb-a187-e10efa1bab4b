import { Async<PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, DestroyRef, effect, inject, OnInit, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DateTime } from 'luxon';
import { LazyLoadImageModule } from 'ng-lazyload-image';
import { Observable, skip } from 'rxjs';

import {
    ApplicationLanguage,
    CallToActionType,
    callToActionTypesWithDefaultUrls,
    PostPublicationStatus,
    TimeInMilliseconds,
} from '@malou-io/package-utils';

import { times } from ':core/constants';
import { PostsService } from ':core/services/posts.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { PostDateStatus } from ':modules/posts/new-post-modal/types';
import { OriginalPostPreviewComponent } from ':shared/components/duplicate-post-preview-modal/components/original-post-preview/original-post-preview.component';
import { SchedulePostFormComponent } from ':shared/components/duplicate-post-preview-modal/components/schedule-post-form/schedule-post-form.component';
import {
    DuplicationInputChangeType,
    SeoPostCaptionPreviewCardComponent,
} from ':shared/components/duplicate-post-preview-modal/duplicate-seo-post-with-text-generation-preview-modal/seo-post-caption-preview-card/seo-post-caption-preview-card.component';
import { roundUpTimeToNextQuarterHour } from ':shared/components/duplicate-post-preview-modal/helpers/plus-15-minutes-date';
import { onlyFutureDate } from ':shared/components/duplicate-post-preview-modal/validators/publication-date-in-the-future';
import { KeepSamePostCaptionToggleComponent } from ':shared/components/keep-same-post-caption-toggle/keep-same-post-caption-toggle.component';
import { MultipleStepsLoaderComponent } from ':shared/components/multiple-steps-loader/multiple-steps-loader.component';
import { BaseStepComponent } from ':shared/components/stepper-modal/base-step.component';
import { getCallToActionDefaultUrl } from ':shared/helpers/call-to-action-url';
import { highlightKeywordsInText, Keyword, PostWithJob, Restaurant } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';

interface DuplicatePostPreviewModalInputData {
    selectedRestaurants: Restaurant[];
}

interface DuplicatePostPreviewModalSharedData {
    restaurantKeywords$: Observable<Keyword[]>;
    restaurantKeywords: Keyword[];
    postToDuplicate: PostWithJob;
}

export interface DuplicatePostRestaurantData {
    restaurant: Restaurant;
    hasCallToActionUrl: boolean;
    postCallToActionUrl: string | null;
    postCaption?: string;
    keywords?: Keyword[];
}

export type DuplicatePostData = {
    restaurantId: string;
    postCaption: string;
    postCallToActionUrl: string | null;
    status: PostPublicationStatus.PENDING | PostPublicationStatus.DRAFT;
    plannedPublicationDate?: Date;
}[];

export interface DuplicatePostPreviewModalSubmitData {
    newPosts: DuplicatePostData;
    hasRemainingCallToActionErrors: boolean;
}

const BaseStepDuplicatePostPreviewComponent = BaseStepComponent<DuplicatePostPreviewModalInputData, DuplicatePostPreviewModalSharedData>;

@Component({
    imports: [
        NgTemplateOutlet,
        MatTooltipModule,
        MatDividerModule,
        MatIconModule,
        TranslateModule,
        ReactiveFormsModule,
        MatButtonModule,
        NgTemplateOutlet,
        MatButtonModule,
        MatIconModule,
        FormsModule,
        AsyncPipe,
        MatProgressBarModule,
        MatProgressSpinnerModule,
        MatAutocompleteModule,
        MatOptionModule,
        MatFormFieldModule,
        MatMenuModule,
        MatCheckboxModule,
        MatRadioModule,
        MatTooltipModule,
        MatSelectModule,
        MultipleStepsLoaderComponent,
        LazyLoadImageModule,
        IllustrationPathResolverPipe,
        SchedulePostFormComponent,
        OriginalPostPreviewComponent,
        SeoPostCaptionPreviewCardComponent,
        KeepSamePostCaptionToggleComponent,
    ],
    templateUrl: './duplicate-seo-post-with-text-generation-preview-modal.component.html',
    styleUrl: './duplicate-seo-post-with-text-generation-preview-modal.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DuplicateSeoPostWithTextGenerationPreviewModalComponent extends BaseStepDuplicatePostPreviewComponent implements OnInit {
    // todo use form builder to have cleaner validation built in
    private readonly _translateService = inject(TranslateService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _postsService = inject(PostsService);
    private readonly _destroyRef = inject(DestroyRef);
    readonly PostDateStatus = Object.values(PostDateStatus);
    readonly TIMES = times;
    readonly MIN_DATE = new Date();
    readonly SvgIcon = SvgIcon;
    readonly CallToActionType = CallToActionType;
    readonly currentLang = signal<ApplicationLanguage>(inject(TranslateService).currentLang as ApplicationLanguage);
    readonly willPostAllAtSameTime = signal(true);
    readonly shouldKeepSameCaptionForAllPosts = signal(false);
    readonly shouldKeepSameCaptionForAllPosts$ = toObservable(this.shouldKeepSameCaptionForAllPosts);
    readonly langForKeywords = signal<ApplicationLanguage | undefined>(undefined);
    readonly callToAction = signal<{ actionType: CallToActionType; url?: string } | null>(null);
    readonly shouldDisableCallToActionUrlInput = computed<boolean>(() => {
        const callToActionType = this.callToAction()?.actionType;
        return !callToActionType || callToActionTypesWithDefaultUrls.includes(callToActionType);
    });
    readonly isGenericCallToActionUrl = computed(() => {
        const callToActionType = this.callToAction()?.actionType;
        return callToActionType && this.callToAction()?.url === getCallToActionDefaultUrl(callToActionType, this.restaurant() ?? undefined);
    });
    readonly missingCallToActionUrlError = computed<(restaurant: Restaurant) => string | null>(
        () =>
            (restaurant: Restaurant): string | null => {
                const callToActionType = this.callToAction()?.actionType;
                return callToActionType && this.isGenericCallToActionUrl() && !getCallToActionDefaultUrl(callToActionType, restaurant)
                    ? this._translateService.instant('posts.duplicate_post_modal.failed_to_match_url', {
                          word: this._getCallToActionUrlWord(callToActionType),
                      })
                    : null;
            }
    );

    readonly loaderSteps = [
        this._translateService.instant('posts.duplicate_post_modal.loader_steps.step_1'),
        this._translateService.instant('posts.duplicate_post_modal.loader_steps.step_2'),
        this._translateService.instant('posts.duplicate_post_modal.loader_steps.step_3'),
    ];

    readonly customizedDatePostForm = new FormGroup(
        {
            status: new FormControl<PostDateStatus>(PostDateStatus.LATER),
            plannedPublicationDate: new FormControl<Date>(new Date()),
            postTime: new FormControl(''),
        },
        {
            validators: [onlyFutureDate()],
        }
    );

    // todo use form builder to have cleaner validation built in
    readonly restaurantsData: WritableSignal<DuplicatePostRestaurantData[]> = signal([]);

    newPostsDateForms: FormGroup[] = [];
    readonly restaurant = toSignal(this._restaurantsService.restaurantSelected$);

    readonly isLoading = signal(false);
    readonly isLoaderMinDurationReached = signal(false);
    readonly isCaptionsGenerationError = signal(false);
    readonly _LOADER_MIN_DURATION = 6 * TimeInMilliseconds.SECOND;

    readonly initialRestaurantsHtmlCaptions: WritableSignal<string[]> = signal([]);
    readonly postLang = signal<string | null>(null);

    readonly _isStepValid = signal(false);
    readonly captionsMap = new Map<string, string>();

    constructor() {
        super();

        // IDK why but i cant use combineLatest (or a computed) like combineLatest([toObservable(this.restaurantsData), this.customizedDatePostForm.valueChanges])
        // so i have to use effect & valueChanges.subscribe (see ngOnInit) to listen to changes and set the validity of the step
        effect(() => {
            // todo use form builder to have cleaner validation built in
            this._updateStepValidity(this.restaurantsData(), this.willPostAllAtSameTime());
        });

        this.shouldKeepSameCaptionForAllPosts$.pipe(skip(1)).subscribe({
            next: () => {
                this._handleKeepSameCaptionForAllPosts();
            },
        });
    }

    ngOnInit(): void {
        super.ngOnInit();
        this.callToAction.set(this.sharedData.postToDuplicate.callToAction ?? null);
        this.postLang.set(this.sharedData.postToDuplicate.language ?? null);
        // IDK why but i cant use combineLatest (or a computed) like combineLatest([toObservable(this.restaurantsData), this.customizedDatePostForm.valueChanges])
        // so i have to use effect (see constructor) & valueChanges.subscribe to listen to changes and set the validity of the step
        this.customizedDatePostForm.valueChanges.pipe(takeUntilDestroyed(this._destroyRef)).subscribe(() => {
            this._updateStepValidity(this.restaurantsData(), this.willPostAllAtSameTime());
        });

        this._initializePostForm();
        this._startSeoPostDuplication();
    }

    toggleWillPostAllAtSameTime(): void {
        this.willPostAllAtSameTime.update((willPostAllAtSameTime) => !willPostAllAtSameTime);
        this._updateStepValidity(this.restaurantsData(), this.willPostAllAtSameTime());
    }

    onUpdateRestaurantsData({
        newValue,
        index,
        changeType,
    }: {
        newValue: string;
        index: number;
        changeType: DuplicationInputChangeType;
    }): void {
        if (changeType === DuplicationInputChangeType.URL) {
            return this._updateCallToActionUrl({ url: newValue, index });
        } else if (changeType === DuplicationInputChangeType.CAPTION) {
            return this._updatePostCaption({ caption: newValue, index });
        }
        return;
    }

    private _updateCallToActionUrl({ url, index }: { url?: string; index: number }): void {
        this.restaurantsData.update((restaurantsData) => {
            restaurantsData[index].postCallToActionUrl = url ?? null;
            return [...restaurantsData];
        });
    }

    private _handleKeepSameCaptionForAllPosts(): void {
        const shouldKeepSameCaptionForAllPosts = this.shouldKeepSameCaptionForAllPosts();
        if (shouldKeepSameCaptionForAllPosts) {
            this.restaurantsData.update((restaurantsData) =>
                restaurantsData.map((restaurantPost) => ({
                    ...restaurantPost,
                    postCaption: this.sharedData.postToDuplicate.text,
                }))
            );
        } else {
            this.restaurantsData.update((restaurantsData) =>
                restaurantsData.map((restaurantPost) => {
                    const caption = this.captionsMap.get(restaurantPost.restaurant._id) ?? '';
                    return {
                        ...restaurantPost,
                        postCaption: caption,
                    };
                })
            );
        }
        this.initialRestaurantsHtmlCaptions.set(
            this.restaurantsData().map((data) =>
                highlightKeywordsInText({
                    text: data.postCaption,
                    keywords: data.keywords,
                    restaurantName: data.restaurant.name,
                    currentLang: this.langForKeywords(),
                })
            )
        );
    }

    private _updatePostCaption({ caption, index }: { caption?: string; index: number }): void {
        if (!this.shouldKeepSameCaptionForAllPosts()) {
            const restaurantId = this.restaurantsData()[index].restaurant._id;
            this._saveCaptionInMap(caption ?? '', restaurantId);
        }
        this.restaurantsData.update((restaurantsData) => {
            restaurantsData[index] = {
                ...restaurantsData[index],
                postCaption: caption,
            };
            return [...restaurantsData];
        });
        this.initialRestaurantsHtmlCaptions.update((captions) => [
            ...captions.map((capt, i) =>
                i === index
                    ? highlightKeywordsInText({
                          text: caption,
                          keywords: this.restaurantsData()[index].keywords,
                          restaurantName: this.restaurantsData()[index].restaurant.name,
                          currentLang: this.langForKeywords(),
                      })
                    : capt
            ),
        ]);
    }

    private _saveCaptionInMap(caption: string, restaurantId: string): void {
        this.captionsMap.set(restaurantId, caption);
    }

    private _startSeoPostDuplication(): void {
        this.isLoading.set(true);
        setTimeout(() => {
            this.isLoaderMinDurationReached.set(true);
        }, this._LOADER_MIN_DURATION);
        const postId = this.sharedData.postToDuplicate._id;
        const restaurantIds = this.inputData.selectedRestaurants.map((restaurant) => restaurant._id.toString());
        this._postsService
            .duplicateSeoPostTextsForRestaurants({ postIdToDuplicate: postId, restaurantIds })
            .pipe(takeUntilDestroyed(this._destroyRef))
            .subscribe({
                next: (res) => {
                    this.restaurantsData.set(
                        this.inputData.selectedRestaurants
                            .map((restaurant) => {
                                const associatedData = res.data.find((d) => d.restaurantId === restaurant._id);
                                const keywords = associatedData?.restaurantKeywords.map((k) => Keyword.fromRestaurantKeywordDto(k)) ?? [];
                                const callToActionType = this.callToAction()?.actionType;
                                const callToActionUrl: string | null = callToActionType
                                    ? this.isGenericCallToActionUrl()
                                        ? getCallToActionDefaultUrl(callToActionType, restaurant)
                                        : (this.callToAction()?.url ?? null)
                                    : null;
                                this._saveCaptionInMap(associatedData?.postCaption ?? '', restaurant._id);
                                return {
                                    ...associatedData,
                                    restaurant,
                                    keywords,
                                    hasCallToActionUrl: !!callToActionUrl,
                                    postCallToActionUrl: callToActionUrl,
                                };
                            })
                            .sort((a, b) => {
                                const diffA = this._countDifferenceBetweenStrings(this.sharedData.postToDuplicate.text, a.postCaption);
                                const diffB = this._countDifferenceBetweenStrings(this.sharedData.postToDuplicate.text, b.postCaption);
                                return diffB - diffA;
                            })
                    );
                    this._initNewPostsDateForms();
                    this.initialRestaurantsHtmlCaptions.set(
                        this.restaurantsData().map((data) =>
                            highlightKeywordsInText({
                                text: data.postCaption,
                                keywords: data.keywords,
                                restaurantName: data.restaurant.name,
                                currentLang: this.langForKeywords(),
                            })
                        )
                    );
                    this.isLoading.set(false);
                },
                error: () => {
                    this.isLoading.set(false);
                    this.isCaptionsGenerationError.set(true);
                },
            });
    }

    protected _submitData(): DuplicatePostPreviewModalSubmitData {
        const commonMappedData = this._mapFormData({
            plannedPublicationDate: this.customizedDatePostForm.value.plannedPublicationDate!,
            postTime: this.customizedDatePostForm.value.postTime!,
            status: this.customizedDatePostForm.value.status!,
        });
        const newPosts = this.restaurantsData().map((data, index) => ({
            restaurantId: data.restaurant._id,
            postCaption: data.postCaption!,
            postCallToActionUrl: data.postCallToActionUrl ?? null,
            ...(this.willPostAllAtSameTime()
                ? commonMappedData
                : this._mapFormData({
                      plannedPublicationDate: this.newPostsDateForms[index].value.plannedPublicationDate!,
                      postTime: this.newPostsDateForms[index].value.postTime!,
                      status: this.newPostsDateForms[index].value.status!,
                  })),
        }));
        const hasRemainingCallToActionErrors = !!this.sharedData.postToDuplicate?.callToAction?.actionType
            ? this.restaurantsData().some((data) => this.callToAction()?.actionType !== CallToActionType.NONE && !data.postCallToActionUrl)
            : false;
        return { newPosts, hasRemainingCallToActionErrors };
    }

    private _mapFormData(formValue: { status: PostDateStatus; plannedPublicationDate: Date; postTime: string }): {
        status: PostPublicationStatus.DRAFT | PostPublicationStatus.PENDING;
        plannedPublicationDate: Date;
    } {
        switch (formValue.status) {
            case PostDateStatus.NOW:
                return {
                    status: PostPublicationStatus.PENDING,
                    plannedPublicationDate: new Date(),
                };
            case PostDateStatus.LATER:
            case PostDateStatus.DRAFT:
                const formDate = this._buildDate({
                    plannedPublicationDate: formValue.plannedPublicationDate,
                    postTime: formValue.postTime,
                });
                return {
                    status: formValue.status === PostDateStatus.LATER ? PostPublicationStatus.PENDING : PostPublicationStatus.DRAFT,
                    plannedPublicationDate: formDate,
                };
        }
    }

    protected _isValid(): boolean {
        return this._isStepValid();
    }

    private _buildDate({ plannedPublicationDate, postTime }: { plannedPublicationDate: Date; postTime: string }): Date {
        const date = new Date(plannedPublicationDate);
        const [hours, minutes] = postTime.split(':').map((n) => parseInt(n, 10));
        date.setHours(hours);
        date.setMinutes(minutes);
        return date;
    }

    private _countDifferenceBetweenStrings(str1?: string, str2?: string): number {
        str1 = str1 || '';
        str2 = str2 || '';

        const maxLength = Math.max(str1.length, str2.length);
        let differences = 0;

        for (let i = 0; i < maxLength; i++) {
            if (str1[i] !== str2[i]) {
                differences++;
            }
        }
        return differences;
    }

    private _initializePostForm(): void {
        const postDate = this.sharedData.postToDuplicate.plannedPublicationDate;
        const status =
            this.sharedData.postToDuplicate.published === PostPublicationStatus.PENDING ? PostDateStatus.LATER : PostDateStatus.DRAFT;
        if (postDate >= new Date()) {
            const postTime = DateTime.fromJSDate(postDate).toFormat('HH:mm');
            this.customizedDatePostForm.patchValue({
                status,
                plannedPublicationDate: postDate,
                postTime,
            });
        } else {
            const plannedPublicationDate = roundUpTimeToNextQuarterHour();
            this.customizedDatePostForm.patchValue({
                status,
                plannedPublicationDate: plannedPublicationDate.toJSDate(),
                postTime: plannedPublicationDate.toFormat('HH:mm'),
            });
        }
    }

    private _initNewPostsDateForms(): void {
        this.newPostsDateForms = this.restaurantsData().map(() => {
            const form = new FormGroup(
                {
                    status: new FormControl<PostDateStatus>(PostDateStatus.LATER),
                    plannedPublicationDate: new FormControl<Date>(
                        this.customizedDatePostForm.get('plannedPublicationDate')?.value ?? new Date()
                    ),
                    postTime: new FormControl(this.customizedDatePostForm.get('postTime')?.value),
                },
                {
                    validators: [onlyFutureDate()],
                }
            );
            form.valueChanges.pipe(takeUntilDestroyed(this._destroyRef)).subscribe(() => {
                this._updateStepValidity(this.restaurantsData(), this.willPostAllAtSameTime());
            });
            return form;
        });
    }

    private _updateStepValidity(restaurantsData: DuplicatePostRestaurantData[], willPostAllAtSameTime: boolean): void {
        const areAllFormsValid = willPostAllAtSameTime ? this.customizedDatePostForm.valid : this.newPostsDateForms.every((f) => f.valid);

        this._isStepValid.set(
            this.isLoaderMinDurationReached() &&
                !this.isLoading() &&
                areAllFormsValid &&
                (restaurantsData ?? this.restaurantsData()).every((data) => !!data.postCaption && data.postCaption.length > 0)
        );

        this.valid.emit(this._isValid());
    }

    private _getCallToActionUrlWord(callToActionType: CallToActionType): string {
        switch (callToActionType) {
            case CallToActionType.BOOK:
                return this._translateService.instant('posts.duplicate_post_modal.url_type.reservation_url');
            case CallToActionType.ORDER:
                return this._translateService.instant('posts.duplicate_post_modal.url_type.order_url');
            case CallToActionType.CALL:
                return this._translateService.instant('posts.duplicate_post_modal.url_type.phone');
            case CallToActionType.LEARN_MORE:
            case CallToActionType.SIGN_UP:
            default:
                return this._translateService.instant('posts.duplicate_post_modal.url_type.website');
        }
    }
}
