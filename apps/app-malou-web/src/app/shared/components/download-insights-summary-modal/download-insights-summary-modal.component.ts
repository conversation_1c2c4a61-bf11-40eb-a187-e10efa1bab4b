import { NgClass } from '@angular/common';
import { Component, computed, inject, signal, viewChild, WritableSignal } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { DateRange, MatCalendar } from '@angular/material/datepicker';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatActionList } from '@angular/material/list';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DateTime } from 'luxon';
import { map } from 'rxjs';

import {
    aggregatedInsightsTabs,
    capitalize,
    CsvInsightChart,
    HeapEventName,
    InsightsTab,
    MalouComparisonPeriod,
    MalouPeriod,
} from '@malou-io/package-utils';

import { UsersContext } from ':core/context/users.context';
import { HeapService } from ':core/services/heap.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ToastService } from ':core/services/toast.service';
import { LocalStorage } from ':core/storage/local-storage';
import { DownloadCsvInsightsSummaryService } from ':modules/statistics/download-insights/download-csv-insights-summary.service';
import { ButtonComponent } from ':shared/components/button/button.component';
import { CustomMatCalendarHeaderComponent } from ':shared/components/custom-mat-calendar-header/custom-mat-calendar-header.component';
import { DownloadInsightsFooterPopinComponent } from ':shared/components/download-insights-modal/download-insights-footer-popin/download-insights-footer-popin.component';
import { DownloadFormat, FileExtension } from ':shared/components/download-insights-modal/download-insights.interface';
import { DownloadInsightsSummaryModalParams } from ':shared/components/download-insights-summary-modal/download-insights-summary.interface';
import { FooterPopinService } from ':shared/components/footer-popin/footer-popin.service';
import { downloadFilesAsZip } from ':shared/helpers/download-files-as-zip';
import { linkedSignal } from ':shared/helpers/linked-signal';
import { DatesAndPeriod, MalouDateFilters } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';

@Component({
    selector: 'app-download-insights-summary-modal',
    templateUrl: './download-insights-summary-modal.component.html',
    styleUrls: ['./download-insights-summary-modal.component.scss'],
    imports: [
        MatButtonModule,
        TranslateModule,
        MatIconModule,
        ReactiveFormsModule,
        ButtonComponent,
        MatCalendar,
        MatActionList,
        NgClass,
        ApplyPurePipe,
    ],
})
export class DownloadInsightsSummaryModalComponent {
    readonly dateCalendar = viewChild<MatCalendar<DateTime>>('dateCalendar');

    private readonly _dialogRef = inject(MatDialogRef<DownloadInsightsSummaryModalComponent>);
    private readonly _restaurantService = inject(RestaurantsService);
    private readonly _translateService = inject(TranslateService);
    private readonly _toastService = inject(ToastService);
    private readonly _enumTranslatePipe = inject(EnumTranslatePipe);
    private readonly _footerPopinService = inject(FooterPopinService);
    private readonly _downloadCsvInsightsSummaryService = inject(DownloadCsvInsightsSummaryService);
    private readonly _heapService = inject(HeapService);
    private readonly _usersContext = inject(UsersContext);

    public readonly data: DownloadInsightsSummaryModalParams = inject(MAT_DIALOG_DATA);
    readonly SvgIcon = SvgIcon;
    readonly CustomMatCalendarHeaderComponent = CustomMatCalendarHeaderComponent;

    readonly ACCEPTED_PERIOD_OPTIONS = [
        MalouPeriod.LAST_SEVEN_DAYS,
        MalouPeriod.LAST_THIRTY_DAYS,
        MalouPeriod.LAST_THREE_MONTHS,
        MalouPeriod.LAST_TWELVE_MONTHS,
    ];

    readonly locale = LocalStorage.getLang();
    readonly now = signal(DateTime.now());
    readonly minDate = this.now().minus({ months: 18 }).toJSDate();

    readonly startDate: WritableSignal<Date> = signal(this.now().minus({ days: 7 }).toJSDate());
    readonly endDate: WritableSignal<Date | null> = signal(this.now().toJSDate());
    readonly selectedPeriod: WritableSignal<MalouPeriod> = signal(MalouPeriod.LAST_SEVEN_DAYS);
    readonly isDownloading = signal<boolean>(false);

    readonly selectedRange = linkedSignal<DateRange<DateTime> | null>(() => {
        const startDate = this.startDate();
        const endDate = this.endDate();
        if (!startDate) {
            return null;
        }
        if (!endDate) {
            return new DateRange(DateTime.fromJSDate(startDate), null);
        }
        return new DateRange(DateTime.fromJSDate(startDate), DateTime.fromJSDate(endDate));
    });

    readonly formattedDate = computed(() => {
        const startDate = this.startDate();
        const endDate = this.endDate();
        if (startDate && endDate) {
            return `${this.getFormattedDate(startDate)} - ${this.getFormattedDate(endDate)}`;
        }
        if (this.startDate()) {
            return this.getFormattedDate(this.startDate());
        }
    });

    readonly isDisabled = computed(() => {
        const startDate = this.startDate();
        const endDate = this.endDate();
        return !startDate || !endDate || startDate.getTime() > endDate.getTime();
    });

    maxLeftDate = (): DateTime | null => (this.endDate() ? DateTime.fromJSDate(this.endDate()!) : null);

    emitChangePeriod(lastKey: MalouPeriod): void {
        const { period, startDate, endDate } = this._getFilter(lastKey);
        this.selectedPeriod.set(period);
        const newStartDate = startDate ?? this.minDate;
        this.startDate.set(newStartDate);
        this.endDate.set(endDate ?? this.now().toJSDate());
        this._goToMonthDate();
    }

    changeDate(date: DateTime): void {
        this._resetSelectedRange();
        this.selectedPeriod.set(MalouPeriod.CUSTOM);
        const startDate = this.startDate();
        const endDate = this.endDate();
        const hasNoStartDate = !startDate;
        const alreadyHadFullRange = !!startDate && !!endDate;
        const isBeforePreviousStartDate = !!startDate && date.toMillis() < startDate.getTime();
        if (hasNoStartDate || alreadyHadFullRange || isBeforePreviousStartDate) {
            const newStartDate = date.toJSDate();
            this.startDate.set(newStartDate);
            this.endDate.set(null);
        } else {
            const newEndDate = date.toJSDate();
            this.endDate.set(newEndDate);
        }
    }

    getFormattedDate(date: Date): string {
        const formattedDate = DateTime.fromJSDate(date).setLocale(this.locale).toLocaleString(DateTime.DATE_HUGE);
        return capitalize(formattedDate);
    }

    close() {
        this._dialogRef.close(undefined);
    }

    showFooter(): string {
        return this._footerPopinService.open(DownloadInsightsFooterPopinComponent, {});
    }

    hideFooter(footerId: string): void {
        this._footerPopinService.close(footerId);
    }

    onClose(): void {
        this._dialogRef.close();
    }

    onDownload(): void {
        const footerId = this.showFooter();
        this.isDownloading.set(true);
        this._downloadCsvInsightsSummaryService
            .getCsvInsightsSummaryData$({
                csvChart: CsvInsightChart.INSIGHTS_SUMMARY,
                options: {
                    startDate: this.startDate(),
                    endDate: this.endDate()!,
                    comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                },
            })
            .pipe(
                map((csvStringData) => {
                    if (!csvStringData || !csvStringData.length) {
                        return null;
                    }
                    return new Blob([csvStringData], { type: 'text/csv;charset=UTF-8' });
                })
            )
            .subscribe({
                next: (res) => {
                    this.hideFooter(footerId);
                    this.isDownloading.set(false);
                    if (!res) {
                        this._toastService.openErrorToast(this._translateService.instant('common.error'));
                        return;
                    }
                    const suffix = this._getDatesSuffix();
                    const zipFilename = this._getInsightTabFilename(this.data.tab, suffix, FileExtension.ZIP);
                    void downloadFilesAsZip(
                        [res],
                        [this._getCsvInsightsChartFilename(CsvInsightChart.INSIGHTS_SUMMARY, suffix, FileExtension.CSV)],
                        zipFilename
                    );

                    this._heapService.track(HeapEventName.DOWNLOAD_INSIGHTS, {
                        tab: this.data.tab,
                        restaurantIds: this._getRestaurantIdsForHeapEvent(),
                        userEmail: this._usersContext.currentUser()?.email ?? '',
                        userId: this._usersContext.currentUser()?.id ?? '',
                        charts: [CsvInsightChart.INSIGHTS_SUMMARY],
                        format: DownloadFormat.CSV,
                    });
                },
                error: (err) => {
                    console.warn('err :>>', err);
                    this.hideFooter(footerId);
                    this.isDownloading.set(false);
                    this._toastService.openErrorToast(this._translateService.instant('common.unknown_error'));
                },
            });
        this.onClose();
    }

    private _resetSelectedRange(): void {
        this.selectedRange.set(null);
    }

    private _getFilter(period: MalouPeriod): DatesAndPeriod {
        const malouDateFilters = new MalouDateFilters();
        if (period === MalouPeriod.CUSTOM) {
            return malouDateFilters.getFilter({
                period: MalouPeriod.CUSTOM,
                startDate: this.startDate() as Date,
                endDate: this.endDate() as Date,
            });
        }
        return malouDateFilters.getFilter({ period });
    }

    private _goToMonthDate(): void {
        const startDate = this.startDate();
        if (startDate) {
            this.dateCalendar()?._goToDateInView(DateTime.fromJSDate(startDate), 'month');
        }
    }

    private _getInsightTabFilename(tab: InsightsTab, suffix: string, extension: string): string {
        const base = this._enumTranslatePipe.transform(tab, 'insights_tab_filename');
        if (aggregatedInsightsTabs.includes(tab)) {
            return `${base}${suffix}.${extension}`;
        } else {
            const restaurantName = this._restaurantService.currentRestaurant?.getDisplayName().replace(/ /g, '_') ?? '';
            return `${base}_${restaurantName}${suffix}.${extension}`;
        }
    }

    private _getCsvInsightsChartFilename(chart: CsvInsightChart, suffix: string, extension: string): string {
        const base = this._enumTranslatePipe.transform(chart, 'csv_insights_chart_filename');
        if (chart === CsvInsightChart.AGGREGATED_INSIGHTS_SUMMARY) {
            return `${base}${suffix}.${extension}`;
        }
        const restaurantName = this._restaurantService.currentRestaurant?.getDisplayName().replace(/ /g, '_') ?? '';
        return `${base}_${restaurantName}${suffix}.${extension}`;
    }

    private _getDatesSuffix(): string {
        const startDateFormatted = DateTime.fromJSDate(this.startDate()).toISODate();
        const endDateFormatted = DateTime.fromJSDate(this.endDate()!).toISODate();
        return `_${startDateFormatted}_${endDateFormatted}`;
    }

    private _getRestaurantIdsForHeapEvent(): string {
        // todo: to add later when we have the multi restaurant feature [@hamza]
        // const restaurantIds = this.data.filters.restaurantIds;
        // if (restaurantIds && restaurantIds.length > 0) {
        //     return restaurantIds.join(',');
        // }
        return this._restaurantService.currentRestaurant?._id ?? '';
    }
}
