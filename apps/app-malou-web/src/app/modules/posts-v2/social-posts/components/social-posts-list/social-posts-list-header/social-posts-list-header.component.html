@let userCanManagePost = CaslAction.MANAGE | caslAble: CaslSubject.SOCIAL_POST;

<app-posts-list-header
    [(selectedFilter)]="selectedFilter"
    [errorFilter]="SocialPostsListFilter.ERROR"
    [allFilter]="SocialPostsListFilter.ALL"
    [filterOptionsAndCount]="filterOptionsAndCount()"
    [atLeastOnePostInList]="atLeastOnePostInList()"
    [isSelecting]="isSelecting()"
    [duplicateTooltip]="duplicatePostsTooltip()"
    [deleteTooltip]="deletePostsTooltip()"
    [allPostsSelected]="allPostsSelected()"
    [userCanManagePost]="userCanManagePost"
    [deleteButtonId]="'tracking_social_posts_init_delete_bulk_v2'"
    [filterOptionsIdFn]="filterOptionsIdFn"
    [filterOptionDisplayFn]="filterOptionDisplayFn"
    (deleteSelection)="onDeleteSelection()"
    (setIsSelecting)="setIsSelecting($event)"
    (toggleSelectAll)="toggleSelectAll($event)">
    <div createButton>
        <app-create-social-post-menu-button
            (createPost)="onCreatePost()"
            (createReelOrTikTok)="onCreateReelOrTikTok()"></app-create-social-post-menu-button>
    </div>

    <div duplicateActions>
        @for (actionButton of duplicateActionButtons(); track actionButton.id) {
            <app-action-button [data]="actionButton"></app-action-button>
        }
    </div>
</app-posts-list-header>
