import { DestroyRef, inject, Injectable, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin, of } from 'rxjs';
import { catchError, filter, switchMap, tap } from 'rxjs/operators';

import {
    CheckForStoreLocatorRestaurantPagesResponseDto,
    GetStoreLocatorOrganizationJobResponseDto,
    StoreLocatorOrganizationKeywordDto,
} from '@malou-io/package-dto';
import { isNotNil } from '@malou-io/package-utils';

import { KeywordsService } from ':core/services/keywords.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ToastService } from ':core/services/toast.service';
import { StoreLocatorJobContext } from ':modules/store-locator/contexts/store-locator-jobs.context';
import { StoreLocatorOrganizationsContext } from ':modules/store-locator/contexts/store-locator-organizations.context';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/models/store-locator-organization-config';
import { StoreLocatorOrganizationKeyword } from ':modules/store-locator/models/store-locator-organization-keyword';
import { StoreLocatorOrganizationRestaurant } from ':modules/store-locator/models/store-locator-organization-restaurant';
import { StoreLocatorOrganizationPagesState } from ':modules/store-locator/models/store-locator.interface';
import { StoreLocatorService } from ':modules/store-locator/store-locator.service';
import { Organization } from ':shared/models/organization';

@Injectable({
    providedIn: 'root',
})
export class StoreLocatorContext {
    private readonly _storeLocatorService = inject(StoreLocatorService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _keywordsService = inject(KeywordsService);
    private readonly _storeLocatorJobContext = inject(StoreLocatorJobContext);
    private readonly _storeLocatorOrganizationsContext = inject(StoreLocatorOrganizationsContext);
    private readonly _destroyRef = inject(DestroyRef);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);

    readonly isLoading = signal<boolean>(false);
    readonly storeLocatorOrganizationConfiguration = signal<StoreLocatorOrganizationConfiguration | null>(null);
    readonly storeLocatorOrganizationRestaurants = signal<StoreLocatorOrganizationRestaurant[]>([]);
    readonly organizationRestaurantKeywords = signal<StoreLocatorOrganizationKeyword[]>([]);
    readonly storeLocatorOrganizationPagesState = signal<StoreLocatorOrganizationPagesState>({
        organizationId: '',
        hasMissingRestaurantPages: false,
        hasUpdatedPages: false,
        hasAtLeastOnePageGenerated: false,
    });

    constructor() {
        this._storeLocatorOrganizationsContext.selectedOrganization$
            .pipe(
                filter(isNotNil),
                tap(() => this._reset()),
                switchMap((organization: Organization) =>
                    forkJoin([of(organization), this._restaurantsService.getStoreLocatorOrganizationRestaurants(organization._id)])
                ),
                switchMap(([organization, organizationRestaurants]: [Organization, StoreLocatorOrganizationRestaurant[]]) => {
                    const restaurantIds = organizationRestaurants.map((restaurant) => restaurant.id);
                    return forkJoin([
                        this._storeLocatorService.getOrganizationConfiguration(organization._id).pipe(
                            catchError((error) => {
                                console.error('Error fetching organization configuration:', error);
                                return of(null);
                            })
                        ),
                        this._storeLocatorService.checkForStoreLocatorRestaurantPages(organization._id),
                        of(organizationRestaurants),
                        this._keywordsService.getStoreLocatorOrganizationKeywords({ restaurantIds }),
                        this._storeLocatorService.getStoreLocatorOrganizationJobs(organization._id),
                    ]);
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: ([
                    organizationConfiguration,
                    storeLocatorOrganizationPagesState,
                    organizationRestaurants,
                    organizationRestaurantKeywords,
                    organizationJobs,
                ]: [
                    StoreLocatorOrganizationConfiguration,
                    CheckForStoreLocatorRestaurantPagesResponseDto,
                    StoreLocatorOrganizationRestaurant[],
                    StoreLocatorOrganizationKeywordDto[],
                    GetStoreLocatorOrganizationJobResponseDto[],
                ]) => {
                    this.storeLocatorOrganizationConfiguration.set(organizationConfiguration);
                    this.storeLocatorOrganizationRestaurants.set(organizationRestaurants);
                    this.organizationRestaurantKeywords.set(organizationRestaurantKeywords.map(StoreLocatorOrganizationKeyword.fromDto));
                    this.storeLocatorOrganizationPagesState.set(storeLocatorOrganizationPagesState);
                    if (organizationJobs.length > 0) {
                        this._storeLocatorJobContext.updateStoreLocatorJobState(
                            {
                                name: organizationConfiguration.organizationId,
                                id: organizationConfiguration.organizationId,
                            },
                            organizationJobs
                        );
                    }
                    this.isLoading.set(false);
                },
                error: () => {
                    this.isLoading.set(false);
                },
            });
    }

    private _reset(): void {
        this.isLoading.set(true);
        this.storeLocatorOrganizationConfiguration.set(null);
        this.storeLocatorOrganizationRestaurants.set([]);
        this.organizationRestaurantKeywords.set([]);
        this.storeLocatorOrganizationPagesState.set({
            organizationId: '',
            hasMissingRestaurantPages: false,
            hasUpdatedPages: false,
            hasAtLeastOnePageGenerated: false,
        });
    }

    updateStoreLocatorOrganizationConfiguration(storeLocatorOrganizationConfiguration: StoreLocatorOrganizationConfiguration): void {
        this.storeLocatorOrganizationConfiguration.set(storeLocatorOrganizationConfiguration);
    }
}
