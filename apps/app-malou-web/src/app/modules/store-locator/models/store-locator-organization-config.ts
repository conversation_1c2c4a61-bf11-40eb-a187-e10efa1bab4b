import { StoreLocatorOrganizationConfigurationResponseDto } from '@malou-io/package-dto';
import {
    EntityConstructor,
    PlatformKey,
    StoreLocatorAiSettingsLanguageStyle,
    StoreLocatorCentralizationPageElementIds,
    StoreLocatorLanguage,
    StoreLocatorRestaurantPageElementIds,
} from '@malou-io/package-utils';

type StoreLocatorOrganizationConfigurationProps = EntityConstructor<StoreLocatorOrganizationConfiguration> & {
    id: string;
};

export class StoreLocatorOrganizationConfiguration {
    id: string;
    organizationId: string;
    cloudfrontDistributionId: string;
    organization: {
        id: string;
        name: string;
    };
    baseUrl: string;
    isLive: boolean;
    styles: {
        fonts: Array<{
            class: string;
            src: string;
            weight?: string;
            style?: string;
        }>;
        colors: Array<{
            class: string;
            value: string;
        }>;
        pages: {
            store: Record<string, string[]>;
            map: Record<string, string[]>;
            mapDraft: Record<string, string[]>;
            storeDraft: Record<string, string[]>;
        };
    };
    plugins?: {
        googleAnalytics?: {
            trackingId: string;
        };
    };
    languages: {
        primary: StoreLocatorLanguage;
        secondary: StoreLocatorLanguage[];
    };
    aiSettings: {
        tone: string[];
        languageStyle: StoreLocatorAiSettingsLanguageStyle;
        attributeIds: string[];
        restaurantKeywordIds: string[];
        specialAttributes: Array<{
            restaurantId: string;
            text: string;
        }>;
        attributes: Array<{
            id: string;
            attributeId: string;
            platformKey: PlatformKey;
            attributeName: {
                fr: string;
                en?: string;
                es?: string;
                it?: string;
            };
        }>;
        keywords: {
            restaurantKeywordId: string;
            text: string;
            restaurantId: string;
            keywordId: string;
        }[];
    };
    shouldDisplayWhiteMark: boolean;

    constructor(props: StoreLocatorOrganizationConfigurationProps) {
        this.id = props.id;
        this.organizationId = props.organizationId;
        this.organization = {
            id: props.organization.id,
            name: props.organization.name,
        };
        this.cloudfrontDistributionId = props.cloudfrontDistributionId;
        this.baseUrl = props.baseUrl;
        this.isLive = props.isLive;
        this.styles = props.styles;
        this.plugins = props.plugins;
        this.languages = props.languages;
        this.aiSettings = props.aiSettings;
        this.shouldDisplayWhiteMark = props.shouldDisplayWhiteMark ?? false;
    }

    static fromDto(dto: StoreLocatorOrganizationConfigurationResponseDto): StoreLocatorOrganizationConfiguration {
        return new StoreLocatorOrganizationConfiguration({
            id: dto.id,
            organizationId: dto.organizationId,
            organization: {
                id: dto.organization.id,
                name: dto.organization.name,
            },
            cloudfrontDistributionId: dto.cloudfrontDistributionId,
            baseUrl: dto.baseUrl,
            isLive: dto.isLive,
            styles: dto.styles,
            plugins: dto.plugins,
            languages: {
                primary: dto.languages.primary,
                secondary: dto.languages.secondary || [],
            },
            aiSettings: dto.aiSettings,
            shouldDisplayWhiteMark: dto.shouldDisplayWhiteMark,
        });
    }

    static factory(): StoreLocatorOrganizationConfiguration {
        return new StoreLocatorOrganizationConfiguration({
            id: '789b0c1d-2e3f-4a5b-6c7d-8e9f0a1b2c3d',
            organizationId: '12345678-1234-1234-1234-123456789012',
            organization: {
                id: '12345678-1234-1234-1234-123456789012',
                name: 'Example Organization',
            },
            cloudfrontDistributionId: 'GH1234567890',
            baseUrl: 'https://example.com',
            isLive: false,
            styles: {
                fonts: [],
                colors: [],
                pages: {
                    store: {},
                    map: {},
                    storeDraft: {},
                    mapDraft: {},
                },
            },
            plugins: undefined,
            languages: {
                primary: StoreLocatorLanguage.UNDETERMINED,
                secondary: [],
            },
            aiSettings: {
                tone: ['inspiring', 'friendly', 'custom tone 1', 'custom tone 2'],
                languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                attributeIds: ['attribute-id-1', 'attribute-id-2'],
                restaurantKeywordIds: ['keyword1-rk-id', 'keyword11-rk-id'],
                specialAttributes: [],
                attributes: [
                    {
                        id: 'db-attribute1-id',
                        attributeId: 'attribute-id-1',
                        platformKey: PlatformKey.GMB,
                        attributeName: {
                            fr: 'Attribute 1',
                            en: 'Attribute 1 EN',
                            es: 'Attribute 1 ES',
                            it: 'Attribute 1 IT',
                        },
                    },
                    {
                        id: 'db-attribute2-id',
                        attributeId: 'attribute-id-2',
                        platformKey: PlatformKey.GMB,
                        attributeName: {
                            fr: 'Attribute 2',
                            en: 'Attribute 2 EN',
                            es: 'Attribute 2 ES',
                            it: 'Attribute 2 IT',
                        },
                    },
                ],
                keywords: [
                    {
                        restaurantKeywordId: 'keyword1-rk-id',
                        text: 'Keyword1',
                        restaurantId: 'keyword1-r-id',
                        keywordId: 'keyword1-id',
                    },
                    {
                        restaurantKeywordId: 'keyword11-rk-id',
                        text: 'Keyword11',
                        restaurantId: 'keyword11-r-id',
                        keywordId: 'keyword11-id',
                    },
                ],
            },
            shouldDisplayWhiteMark: false,
        });
    }

    isAiSettingsConfigured(): boolean {
        return this.aiSettings.tone.length > 0 && this.aiSettings.restaurantKeywordIds.length > 0;
    }

    isStylesConfigured(): boolean {
        // TODO: complete the logic to determine if the styles are fully configured
        return true;
    }

    isLanguagesConfigured(): boolean {
        return this.languages.primary !== StoreLocatorLanguage.UNDETERMINED;
    }

    isConfigured(): boolean {
        return this.isAiSettingsConfigured() && this.isStylesConfigured() && this.isLanguagesConfigured();
    }

    updateStyles({
        storePageStyles,
        centralizationPageStyles,
    }: {
        storePageStyles: Partial<Record<StoreLocatorRestaurantPageElementIds, string[]>>;
        centralizationPageStyles: Partial<Record<StoreLocatorCentralizationPageElementIds, string[]>>;
    }): void {
        this.styles.pages.storeDraft = {
            ...this.styles.pages.storeDraft,
            ...storePageStyles,
        };
        this.styles.pages.mapDraft = {
            ...this.styles.pages.mapDraft,
            ...centralizationPageStyles,
        };
    }
}
