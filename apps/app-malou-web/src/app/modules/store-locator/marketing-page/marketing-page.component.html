<div class="flex flex-col gap-6 px-12 py-8">
    <div class="flex flex-col lg:gap-2 lg:px-[8%]">
        <div class="malou-text-30--bold lg:malou-text-20--bold text-center text-malou-color-text-1">
            <span class="lg:hidden">🚀</span>
            {{ 'store_locator.marketing_page.title' | translate }}
        </div>
        <div class="malou-text-13--medium lg:malou-text-12--medium text-center text-malou-color-text-2">
            {{ 'store_locator.marketing_page.subtitle' | translate }}
        </div>
    </div>

    <div class="flex lg:flex-col">
        <div class="flex w-full justify-center overflow-hidden rounded-l-[10px] lg:min-w-full lg:max-w-full lg:rounded-none">
            <img class="h-[450px] w-full object-cover" [src]="'marketing' | imagePathResolver: { folder: 'store-locator' }" />
        </div>

        <div
            class="flex w-full flex-col justify-between rounded-r-[10px] border border-malou-color-border-primary bg-white px-12 py-10 lg:gap-3 lg:rounded-none lg:px-8 lg:py-6">
            <div class="flex flex-col gap-2">
                <div class="flex items-center gap-2">
                    <mat-icon class="!h-3 !w-3" color="primary" [svgIcon]="SvgIcon.ARROW_RIGHT"></mat-icon>
                    <span class="malou-text-13--bold text-malou-color-text-1">{{
                        'store_locator.marketing_page.features.visibility.title' | translate
                    }}</span>
                </div>
                <div class="malou-text-12 ml-5 text-malou-color-text-2">
                    {{ 'store_locator.marketing_page.features.visibility.description' | translate }}
                </div>
            </div>

            <div class="flex flex-col gap-2">
                <div class="flex items-center gap-2">
                    <mat-icon class="!h-3 !w-3" color="primary" [svgIcon]="SvgIcon.ARROW_RIGHT"></mat-icon>
                    <span class="malou-text-13--bold text-malou-color-text-1">{{
                        'store_locator.marketing_page.features.conversion.title' | translate
                    }}</span>
                </div>
                <div class="malou-text-12 ml-5 text-malou-color-text-2">
                    {{ 'store_locator.marketing_page.features.conversion.description' | translate }}
                </div>
            </div>

            <div class="flex flex-col gap-2">
                <div class="flex items-center gap-2">
                    <mat-icon class="!h-3 !w-3" color="primary" [svgIcon]="SvgIcon.ARROW_RIGHT"></mat-icon>
                    <span class="malou-text-13--bold text-malou-color-text-1">{{
                        'store_locator.marketing_page.features.reliability.title' | translate
                    }}</span>
                </div>
                <div class="malou-text-12 ml-5 text-malou-color-text-2">
                    {{ 'store_locator.marketing_page.features.reliability.description' | translate }}
                </div>
            </div>

            <div class="flex flex-col gap-2">
                <div class="flex items-center gap-2">
                    <mat-icon class="!h-3 !w-3" color="primary" [svgIcon]="SvgIcon.ARROW_RIGHT"></mat-icon>
                    <span class="malou-text-13--bold text-malou-color-text-1">{{
                        'store_locator.marketing_page.features.simplicity.title' | translate
                    }}</span>
                </div>
                <div class="malou-text-12 ml-5 text-malou-color-text-2">
                    {{ 'store_locator.marketing_page.features.simplicity.description' | translate }}
                </div>
            </div>

            <div class="flex gap-x-3">
                <div class="flex w-1/2 flex-col gap-2">
                    <app-button
                        buttonClasses="h-11 w-full"
                        [disabled]="isRequestSent()"
                        [loading]="isSendingRequest()"
                        [text]="
                            isRequestSent()
                                ? ('boosters.presentation.request_sent_btn' | translate)
                                : ('boosters.presentation.get_now' | translate)
                        "
                        [tooltip]="isRequestSent() ? ('wheel_of_fortune.subscription_request_modal.request_sent' | translate) : ''"
                        (buttonClick)="sendRequest()"></app-button>
                    <div class="malou-text-10 text-center italic text-malou-color-text-2">
                        {{ 'boosters.presentation.you_will_be_contacted' | translate }}
                    </div>
                </div>

                <button
                    class="malou-btn-raised--secondary h-11 w-1/2"
                    id="tracking_boosters_more_information"
                    mat-raised-button
                    (click)="redirectToMoreInformation()">
                    {{ 'boosters.presentation.learn_more' | translate }}
                </button>
            </div>
        </div>
    </div>

    <div class="flex rounded-[5px] border border-malou-color-border-primary bg-white lg:mx-6 lg:flex-col">
        <div class="flex gap-2 bg-malou-color-background-dark px-2 py-2 lg:items-center lg:py-3">
            <div class="malou-text-30 flex items-center border-r border-malou-color-text-white pr-2">🏆</div>
            <div class="flex flex-col justify-center gap-1 pl-2">
                <div class="mb-2">
                    <div class="malou-text-10--bold text-malou-color-text-1">
                        {{ 'store_locator.marketing_page.case_study.intro' | translate }}
                    </div>
                    <div class="malou-text-10--medium italic text-malou-color-text-2">
                        {{ 'store_locator.marketing_page.case_study.result_period' | translate }}
                    </div>
                </div>
                <div class="flex flex-col">
                    <div class="mb-2 mt-1 flex">
                        <img class="h-full object-cover" [src]="'bioburger-logo' | imagePathResolver: { folder: 'store-locator' }" />
                    </div>
                    <button class="malou-btn-flat !h-[10px] !pl-0" mat-button (click)="redirectToStudyCaseStoreLocator()">
                        {{ 'store_locator.marketing_page.case_study.see_store_locator' | translate }}
                    </button>
                </div>
            </div>
        </div>
        <div class="flex grow py-3 lg:flex-col lg:px-3 lg:py-0">
            <div
                class="flex grow basis-0 flex-col items-center justify-center border-r border-malou-color-border-primary px-3 py-2 lg:border-b lg:border-r-0 lg:px-10 lg:py-5">
                <div class="malou-text-40--bold leading-[40px] text-malou-color-chart-purple">+15%</div>
                <div class="malou-text-12--semibold text-center text-malou-color-text-1">
                    {{ 'store_locator.marketing_page.case_study.metrics.impressions' | translate }}
                </div>
            </div>
            <div
                class="flex grow basis-0 flex-col items-center justify-center border-r border-malou-color-border-primary px-3 py-2 lg:border-b lg:border-r-0 lg:px-10 lg:py-5">
                <div class="malou-text-40--bold leading-[40px] text-malou-color-chart-purple">+340%</div>
                <div class="malou-text-12--semibold text-center text-malou-color-text-1">
                    {{ 'store_locator.marketing_page.case_study.metrics.clicks' | translate }}
                </div>
                <div class="malou-text-10--medium text-center italic text-malou-color-text-2">
                    {{ 'store_locator.marketing_page.case_study.metrics.clicks_subtitle' | translate }}
                </div>
            </div>
            <div
                class="flex grow basis-0 flex-col items-center justify-center border-r border-malou-color-border-primary px-3 py-2 lg:border-b lg:border-r-0 lg:px-10 lg:py-5">
                <div class="malou-text-40--bold leading-[40px] text-malou-color-chart-purple">+32%</div>
                <div class="malou-text-12--semibold text-center text-malou-color-text-1">
                    {{ 'store_locator.marketing_page.case_study.metrics.visit_duration' | translate }}
                </div>
                <div class="malou-text-10--medium text-center italic text-malou-color-text-2">
                    {{ 'store_locator.marketing_page.case_study.metrics.visit_duration_subtitle' | translate }}
                </div>
            </div>
            <div class="flex grow basis-0 flex-col items-center justify-center px-3 py-2 lg:px-10 lg:py-5">
                <div class="malou-text-40--bold leading-[40px] text-malou-color-chart-purple">-23%</div>
                <div class="malou-text-12--semibold text-center text-malou-color-text-1">
                    {{ 'store_locator.marketing_page.case_study.metrics.bounce_rate' | translate }}
                </div>
            </div>
        </div>
    </div>
</div>
