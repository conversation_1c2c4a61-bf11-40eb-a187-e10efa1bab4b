import {
    CentralizationStylesConfigurationUpdate,
    PropertyType,
    StoreStylesConfigurationUpdate,
} from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import { ColorHexValue, CssKeys, ExtraColor } from ':modules/store-locator/shared/edit-store-locator/edit-store-locator.interface';
import { StoreLocatorOrganizationStylesConfiguration } from ':modules/store-locator/shared/edit-store-locator/models/store-locator-organization-styles-configuration';
import { getFontFamily } from ':modules/store-locator/shared/edit-store-locator/utils/inject-font-family';

export function parseConfigurationStyleClassesToCssStyle(
    classes: string[],
    design: {
        colors: StoreLocatorOrganizationStylesConfiguration['colors'];
        fonts: StoreLocatorOrganizationStylesConfiguration['fonts'];
    }
): Record<CssKeys, string> {
    const style: Record<CssKeys, string> = {
        backgroundColor: '',
        color: '',
        fill: '',
        borderColor: '',
        fontWeight: '',
        fontFamily: '',
        borderRadius: '',
        hoverBg: '',
        hoverText: '',
    };

    for (const className of classes) {
        if (className.startsWith('bg-')) {
            const key = className.replace('bg-', '');
            const value = design.colors.find((c: any) => c.class === key)?.value;
            if (value) {
                style.backgroundColor = value;
            } else if (key === ExtraColor.WHITE) {
                style.backgroundColor = ColorHexValue.white;
            }
        }

        if (className.startsWith('hover:')) {
            const hoverClass = className.replace('hover:', '');
            const hoverStyle = parseConfigurationStyleClassesToCssStyle([hoverClass], design);
            if (hoverStyle.backgroundColor) {
                style.hoverBg = `hover:bg-[${hoverStyle.backgroundColor}]`;
            }
            if (hoverStyle.color) {
                style.hoverText = `hover:text-[${hoverStyle.color}]`;
            }
        }

        if (className.startsWith('text-')) {
            const key = className.replace('text-', '');
            const value = design.colors.find((c: any) => c.class === key)?.value;
            if (value) {
                style.color = value;
            } else if (key === ExtraColor.WHITE) {
                style.color = ColorHexValue.white;
            }
        }

        if (className.startsWith('fill-')) {
            const key = className.replace('fill-', '');
            const value = design.colors.find((c: any) => c.class === key)?.value;
            if (value) {
                style.fill = value;
            }
        }

        if (className.startsWith('border-')) {
            const key = className.replace('border-', '');
            const value = design.colors.find((c: any) => c.class === key)?.value;
            if (value) {
                style.borderColor = value;
            }
        }

        if (className.startsWith('font-')) {
            const key = className.replace('font-', '');
            const fontProperties = design.fonts.find((f: any) => f.class === key);
            if (fontProperties) {
                const weight = fontProperties.weight;
                if (weight) {
                    style.fontWeight = weight;
                }
                style.fontFamily = getFontFamily(key);
            }
        }

        if (className.startsWith('rounded-')) {
            const key = className.replace('rounded-', '');
            switch (key) {
                case 'full':
                    style.borderRadius = '100px';
                    break;
                case 'lg':
                    style.borderRadius = '8px';
                    break;
                case 'md':
                    style.borderRadius = '6px';
                    break;
                case 'sm':
                    style.borderRadius = '2px';
                    break;
                default:
                    const pxMatch = key.match(/^\[(\d+)px\]$/);
                    if (pxMatch) {
                        style.borderRadius = `${pxMatch[1]}px`;
                        break;
                    }
                    style.borderRadius = '10px';
                    break;
            }
        }
    }

    return style;
}

export function parseColorToConfigurationStyleClass(
    color: string,
    colors: StoreLocatorOrganizationStylesConfiguration['colors'],
    propertyType: PropertyType
): string {
    const colorClass = colors.find((c) => c.value === color)?.class || ExtraColor.WHITE;
    switch (propertyType) {
        case PropertyType.BackgroundColor:
            return `bg-${colorClass}`;
        case PropertyType.Color:
            return `text-${colorClass}`;
        case PropertyType.Fill:
            return `fill-${colorClass}`;
        case PropertyType.BorderColor:
            return `border-${colorClass}`;
        default:
            return '';
    }
}

export function mapUpdateStylesConfiguration(
    organizationStyleConfiguration: StoreLocatorOrganizationStylesConfiguration,
    newStyles: StoreStylesConfigurationUpdate[]
): Record<string, string[]> {
    const storePageStyles = organizationStyleConfiguration.storePages || {};
    const storePageColors = organizationStyleConfiguration.colors || [];
    return mapNewStylesToConfiguration(newStyles as StoreStylesConfigurationUpdate[], storePageStyles, storePageColors);
}

export function mapUpdateCentralizationStylesConfiguration(
    organizationStyleConfiguration: StoreLocatorOrganizationStylesConfiguration,
    newStyles: CentralizationStylesConfigurationUpdate[]
): Record<string, string[]> {
    const centralizationPageStyles = organizationStyleConfiguration.mapPages || {};
    const storePageColors = organizationStyleConfiguration.colors || [];
    return mapNewStylesToConfiguration(newStyles as CentralizationStylesConfigurationUpdate[], centralizationPageStyles, storePageColors);
}

export function getRadiusValue(stringValue: string | undefined): number {
    if (!stringValue) {
        return 10;
    }
    const value = parseInt(stringValue.replace('px', ''), 10);
    if (isNaN(value)) {
        return 10;
    }
    return value;
}

function mapNewStylesToConfiguration(
    newStyles: StoreStylesConfigurationUpdate[] | CentralizationStylesConfigurationUpdate[],
    oldStyles: Record<string, string[]>,
    colors: StoreLocatorOrganizationStylesConfiguration['colors']
): Record<string, string[]> {
    const mappedStylesConfigurationUpdate: Record<string, string[]> = newStyles.reduce((acc, style) => {
        const { elementId, data } = style;
        const classNames = parseTailwindToConfigurationStyleClass(data, colors);
        if (!acc[elementId]) {
            acc[elementId] = [];
        }
        acc[elementId] = updateConfigurationClasses(oldStyles[elementId], classNames);
        return acc;
    }, {});
    return mappedStylesConfigurationUpdate;
}

// This function updates the configuration classes by removing old classes that match the new classes' properties
// and adding the new classes. It ensures that the final list contains unique classes.
function updateConfigurationClasses(oldClasses: string[], newClasses: string[]): string[] {
    const newClassesProperties = newClasses.map((attr) => attr.split('-')[0]);
    const classesToKeep = oldClasses.filter((attr) => !newClassesProperties.includes(attr.split('-')[0]));
    return [...classesToKeep, ...newClasses];
}

function parseTailwindToConfigurationStyleClass(
    data: { value: string; propertyType: PropertyType }[],
    colors: StoreLocatorOrganizationStylesConfiguration['colors']
): string[] {
    return data.map(({ value, propertyType }) => {
        if (propertyType === PropertyType.BorderRadius) {
            return `rounded-[${value}px]`;
        }
        return parseColorToConfigurationStyleClass(value, colors, propertyType);
    });
}
