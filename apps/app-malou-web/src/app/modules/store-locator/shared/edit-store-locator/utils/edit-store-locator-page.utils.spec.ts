import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { PropertyType } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import { ColorHexValue, ExtraColor } from ':modules/store-locator/shared/edit-store-locator/edit-store-locator.interface';
import { StoreLocatorOrganizationStylesConfiguration } from ':modules/store-locator/shared/edit-store-locator/models/store-locator-organization-styles-configuration';
import {
    mapUpdateStylesConfiguration,
    parseColorToConfigurationStyleClass,
    parseConfigurationStyleClassesToCssStyle,
} from ':modules/store-locator/shared/edit-store-locator/utils/edit-store-locator-page.utils';

const mockDesign = {
    colors: [
        { class: 'primary', value: '#123456' },
        { class: 'secondary', value: '#abcdef' },
        { class: ExtraColor.WHITE, value: ColorHexValue.white },
    ],
    fonts: [
        { class: 'main', src: 'main.woff', weight: '700' },
        { class: 'alt', src: 'alt.woff', weight: '400' },
    ],
};

describe('parseConfigurationStyleClassesToCssStyle', () => {
    it('should parse bg- and text- classes to CSS', () => {
        const classes = ['bg-primary', 'text-secondary'];
        const style = parseConfigurationStyleClassesToCssStyle(classes, mockDesign);
        expect(style.backgroundColor).toBe('#123456');
        expect(style.color).toBe('#abcdef');
    });

    it('should handle ExtraColor.WHITE for text', () => {
        const classes = ['text-white'];
        const style = parseConfigurationStyleClassesToCssStyle(classes, mockDesign);
        expect(style.color).toBe(ColorHexValue.white);
    });

    it('should parse fill- and border- classes to CSS', () => {
        const classes = ['fill-primary', 'border-secondary'];
        const style = parseConfigurationStyleClassesToCssStyle(classes, mockDesign);
        expect(style.fill).toBe('#123456');
        expect(style.borderColor).toBe('#abcdef');
    });

    it('should parse font- classes to fontWeight and fontFamily', () => {
        const classes = ['font-main', 'font-main-bold'];
        const style = parseConfigurationStyleClassesToCssStyle(classes, mockDesign);
        expect(style.fontWeight).toBe('700');
        expect(style.fontFamily).toBeDefined();
    });

    it('should parse rounded- classes to borderRadius', () => {
        expect(parseConfigurationStyleClassesToCssStyle(['rounded-full'], mockDesign).borderRadius).toBe('100px');
        expect(parseConfigurationStyleClassesToCssStyle(['rounded-lg'], mockDesign).borderRadius).toBe('8px');
        expect(parseConfigurationStyleClassesToCssStyle(['rounded-md'], mockDesign).borderRadius).toBe('6px');
        expect(parseConfigurationStyleClassesToCssStyle(['rounded-sm'], mockDesign).borderRadius).toBe('2px');
    });
});

describe('parseColorToConfigurationStyleClass', () => {
    it('should return correct class for background color', () => {
        expect(parseColorToConfigurationStyleClass('#123456', mockDesign.colors, PropertyType.BackgroundColor)).toBe('bg-primary');
    });
    it('should return correct class for text color', () => {
        expect(parseColorToConfigurationStyleClass('#abcdef', mockDesign.colors, PropertyType.Color)).toBe('text-secondary');
    });
    it('should return correct class for fill', () => {
        expect(parseColorToConfigurationStyleClass('#123456', mockDesign.colors, PropertyType.Fill)).toBe('fill-primary');
    });
    it('should return correct class for border color', () => {
        expect(parseColorToConfigurationStyleClass('#abcdef', mockDesign.colors, PropertyType.BorderColor)).toBe('border-secondary');
    });
});

describe('mapUpdateStylesConfiguration', () => {
    // Use elementId values that are likely valid for StoreLocatorRestaurantPageElementIds
    const orgConfig = new StoreLocatorOrganizationStylesConfiguration({
        colors: mockDesign.colors,
        fonts: mockDesign.fonts,
        pages: {
            store: {
                [StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE]: ['bg-primary', 'text-secondary'],
                [StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]: ['bg-secondary'],
                [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1]: ['bg-primary', 'rounded-full'],
            },
            map: {},
            storeDraft: {
                [StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE]: ['bg-primary', 'text-secondary'],
                [StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]: ['bg-secondary'],
            },
            mapDraft: {},
        },
    });

    it('should update styles for given elements', () => {
        const newStyles = [
            {
                elementId: StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE,
                data: [
                    { value: '#abcdef', propertyType: PropertyType.BackgroundColor },
                    { value: '#123456', propertyType: PropertyType.Color },
                ],
            },
        ];
        const result = mapUpdateStylesConfiguration(orgConfig, newStyles);
        expect(result[StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE]).toContain('bg-secondary');
        expect(result[StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE]).toContain('text-primary');
    });

    it('should add new element if not present', () => {
        const newStyles = [
            {
                elementId: StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER,
                data: [{ value: '#abcdef', propertyType: PropertyType.Color }],
            },
        ];
        const result = mapUpdateStylesConfiguration(orgConfig, newStyles);
        expect(result[StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]).toContain('bg-secondary');
        expect(result[StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]).toContain('text-secondary');
    });

    it('should update styles for multiple given elements', () => {
        const newStyles = [
            {
                elementId: StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER,
                data: [{ value: '#abcdef', propertyType: PropertyType.Color }],
            },
            {
                elementId: StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE,
                data: [{ value: '#123456', propertyType: PropertyType.BackgroundColor }],
            },
        ];
        const result = mapUpdateStylesConfiguration(orgConfig, newStyles);
        expect(result[StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]).toContain('bg-secondary');
        expect(result[StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]).toContain('text-secondary');
        expect(result[StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE]).toContain('bg-primary');
        expect(result[StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE]).toContain('text-secondary');
    });

    it('should manage radius style', () => {
        const newStyles = [
            {
                elementId: StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER,
                data: [{ value: '12', propertyType: PropertyType.BorderRadius }],
            },
            {
                elementId: StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1,
                data: [{ value: '20', propertyType: PropertyType.BorderRadius }],
            },
        ];
        const result = mapUpdateStylesConfiguration(orgConfig, newStyles);
        expect(result[StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]).toContain('bg-secondary');
        expect(result[StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER]).toContain('rounded-[12px]');
        expect(result[StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1]).toContain('bg-primary');
        expect(result[StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1]).toContain('rounded-[20px]');
    });
});
