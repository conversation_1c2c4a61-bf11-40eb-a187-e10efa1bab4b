export class ArrayMap<T, K> extends Map<T, K[]> {
    findIn(key: T, predicate: (item: K) => boolean): K | null {
        const array = this.get(key);
        return array?.find(predicate) ?? null;
    }
    filterAll(predicate: (item: K) => boolean): K[] {
        const results: K[] = [];
        this.forEach((array) => {
            array.forEach((item) => {
                if (predicate(item)) {
                    results.push(item);
                }
            });
        });
        return results;
    }
}
