import { signal, WritableSignal } from '@angular/core';

import { UpdateOrganizationConfigurationPagesBodyDto } from '@malou-io/package-dto';
import { StoreLocatorCentralizationPageElementIds, StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/models/store-locator-organization-config';

export class StoreLocatorOrganizationStylesConfiguration {
    colors: StoreLocatorOrganizationConfiguration['styles']['colors'];
    fonts: StoreLocatorOrganizationConfiguration['styles']['fonts'];
    storePages: StoreLocatorOrganizationConfiguration['styles']['pages']['store'];
    mapPages: StoreLocatorOrganizationConfiguration['styles']['pages']['map'];

    readonly isDirty: WritableSignal<boolean> = signal(false);

    readonly updatedStorePages: WritableSignal<Partial<Record<StoreLocatorRestaurantPageElementIds, string[]>>> = signal({});
    readonly updatedCentralizationPages: WritableSignal<Partial<Record<StoreLocatorCentralizationPageElementIds, string[]>>> = signal({});

    constructor(init: StoreLocatorOrganizationConfiguration['styles']) {
        this.colors = init.colors;
        this.fonts = init.fonts;
        this.storePages = { ...init.pages.store, ...(init.pages.storeDraft ?? {}) };
        this.mapPages = { ...init.pages.map, ...(init.pages.mapDraft ?? {}) };
    }

    getStorePageElementStyle(elementId: StoreLocatorRestaurantPageElementIds): string[] {
        const updatedStyleClasses = this.updatedStorePages()[elementId];
        if (updatedStyleClasses) {
            return updatedStyleClasses;
        }
        return this.storePages[elementId];
    }

    getMapPageElementStyle(elementId: StoreLocatorCentralizationPageElementIds): string[] {
        const updatedStyleClasses = this.updatedCentralizationPages()[elementId];
        if (updatedStyleClasses) {
            return updatedStyleClasses;
        }
        return this.mapPages[elementId];
    }

    updateStyle(data: Partial<Record<StoreLocatorRestaurantPageElementIds, string[]>>): void {
        const currentStorePages = this.updatedStorePages();
        this.updatedStorePages.set({
            ...currentStorePages,
            ...data,
        });
        if (!this.isDirty()) {
            this.isDirty.set(true);
        }
    }

    updateCentralizationStyle(data: Partial<Record<StoreLocatorCentralizationPageElementIds, string[]>>): void {
        const currentCentralizationPage = this.updatedCentralizationPages();
        this.updatedCentralizationPages.set({
            ...currentCentralizationPage,
            ...data,
        });
        if (!this.isDirty()) {
            this.isDirty.set(true);
        }
    }

    toDto(): UpdateOrganizationConfigurationPagesBodyDto | null {
        if (Object.keys(this.updatedStorePages()).length === 0 && Object.keys(this.updatedCentralizationPages()).length === 0) {
            return null;
        }
        return {
            store: this.updatedStorePages(),
            centralization: this.updatedCentralizationPages(),
        };
    }
}
