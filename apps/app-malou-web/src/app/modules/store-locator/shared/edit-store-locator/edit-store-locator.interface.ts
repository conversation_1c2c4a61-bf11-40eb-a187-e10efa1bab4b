import { TimeInMilliseconds } from '@malou-io/package-utils';

// WATCHER
export const DEFAULT_PUBLICATION_ESTIMATED_TIME = 5 * TimeInMilliseconds.MINUTE;

export enum ExtraColor {
    WHITE = 'white',
}

export const ColorHexValue = {
    white: '#ffffff',
};

export const EXTRA_COLORS = [ColorHexValue.white];

export type CssKeys =
    | 'backgroundColor'
    | 'color'
    | 'fill'
    | 'borderColor'
    | 'fontWeight'
    | 'fontFamily'
    | 'borderRadius'
    | 'hoverBg'
    | 'hoverText';
