import { NgClass } from '@angular/common';
import {
    AfterContentInit,
    ChangeDetectionStrategy,
    Component,
    computed,
    ContentChild,
    DestroyRef,
    inject,
    input,
    signal,
    WritableSignal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AbstractControl } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { GenerateStoreLocatorStorePageContentBodyDto } from '@malou-io/package-dto';
import { GenerateStoreLocatorContentType } from '@malou-io/package-utils';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { AiSuggestionExtraParams } from ':modules/store-locator/edit-store-locator-page/blocks/shared/ai-suggestion/ai-suggestion.interface';
import { EditStoreLocatorPageContext } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.context';
import { StoreLocatorStorePageState } from ':modules/store-locator/edit-store-locator-page/models/store-locator-store-page-state';
import { StoreLocatorService } from ':modules/store-locator/store-locator.service';
import { ButtonComponent } from ':shared/components/button/button.component';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { TextAreaComponent } from ':shared/components/text-area/text-area.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-store-locator-edit-page-ai-suggestion',
    templateUrl: './ai-suggestion.component.html',
    styleUrls: ['./ai-suggestion.component.scss'],
    imports: [LazyLoadImageModule, MatIconModule, MatTooltipModule, MalouSpinnerComponent, NgClass, ButtonComponent, TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditStoreLocatorPageAiSuggestionComponent implements AfterContentInit {
    @ContentChild(InputTextComponent) inputTextComponent!: InputTextComponent;
    @ContentChild(TextAreaComponent) textAreaComponent!: TextAreaComponent;

    readonly title = input<string | null>(null);
    readonly control = input<AbstractControl | null>(null);
    readonly extraGenerationParams = input<AiSuggestionExtraParams | null>(null);
    readonly generateStoreLocatorContentType = input.required<GenerateStoreLocatorContentType>();
    readonly optimizeStoreLocatorContentType = input.required<GenerateStoreLocatorContentType>();
    readonly isDisabled = input.required<boolean>();
    readonly forceHideButtons = input<boolean>(false);
    readonly forceHideMagicWand = input<boolean>(false);

    private readonly _editStoreLocatorPageContext = inject(EditStoreLocatorPageContext);
    private readonly _storeLocatorService = inject(StoreLocatorService);
    private readonly _destroyRef = inject(DestroyRef);

    readonly isLoadingGeneration: WritableSignal<boolean> = signal(false);
    readonly isLoadingOptimization: WritableSignal<boolean> = signal(false);

    readonly shouldHideMagicWand: WritableSignal<boolean> = signal(true);
    readonly isFocused: WritableSignal<boolean> = signal(false);

    readonly storePageState = computed(() => this._editStoreLocatorPageContext.selectedRestaurantStorePageState());

    readonly SvgIcon = SvgIcon;

    ngAfterContentInit(): void {
        this.control()
            ?.valueChanges.pipe(takeUntilDestroyed(this._destroyRef))
            .subscribe((valueChanges) => {
                if (valueChanges === null || valueChanges === undefined || valueChanges.trim() === '') {
                    this.shouldHideMagicWand.set(false);
                } else {
                    this.shouldHideMagicWand.set(true);
                }
            });
        if (this.inputTextComponent) {
            this.inputTextComponent.onFocus.subscribe((focusEvent) => this.isFocused.set(focusEvent.type === 'focus'));
        }
        if (this.textAreaComponent) {
            this.textAreaComponent.onFocus.subscribe((focusEvent) => this.isFocused.set(focusEvent.type === 'focus'));
        }
    }

    generateStoreLocatorContent(event?: Event): void {
        event?.stopPropagation();
        const updates = this.storePageState()?.toDto({
            shouldIgnoreCheck: true,
        });
        if (!updates) {
            return;
        }
        this.isLoadingGeneration.set(true);
        this._storeLocatorService
            .generateStoreLocatorContent({
                organizationId: this._editStoreLocatorPageContext.organizationData().id,
                body: {
                    type: this._getGenerationContentType(),
                    updates,
                    params: { context: this._getGenerationContext() },
                },
            })
            .pipe(takeUntilDestroyed(this._destroyRef))
            .subscribe({
                next: (result) => {
                    this.control()?.setValue(result.text);
                    this.isLoadingGeneration.set(false);
                },
                error: () => {
                    this.isLoadingGeneration.set(false);
                },
            });
    }

    optimizeStoreLocatorContent(): void {
        const updates = this.storePageState()?.toDto({
            shouldIgnoreCheck: true,
        });
        if (!updates) {
            return;
        }
        this.isLoadingOptimization.set(true);
        this._storeLocatorService
            .generateStoreLocatorContent({
                organizationId: this._editStoreLocatorPageContext.organizationData().id,
                body: {
                    type: this.optimizeStoreLocatorContentType(),
                    updates,
                    params: { currentContent: this._getGenerationCurrentContent(), context: this._getGenerationContext() },
                },
            })
            .pipe(takeUntilDestroyed(this._destroyRef))
            .subscribe({
                next: (result) => {
                    this.control()?.setValue(result.text);
                    this.isLoadingOptimization.set(false);
                },
                error: () => {
                    this.isLoadingOptimization.set(false);
                },
            });
    }

    private _getGenerationContentType(): GenerateStoreLocatorContentType {
        if (this.generateStoreLocatorContentType() === GenerateStoreLocatorContentType.FAQ_BLOCK_QUESTION_GENERATION) {
            const storePageState = this.storePageState();
            if (!storePageState) {
                return GenerateStoreLocatorContentType.FAQ_BLOCK_QUESTION_GENERATION;
            }
            const item = storePageState.updatedFaqBlock().data?.items[this.extraGenerationParams()?.descriptionItemData?.itemIndex ?? 0];
            if (!item?.answer) {
                return GenerateStoreLocatorContentType.FAQ_BLOCK_SINGULAR_GENERATION;
            }
        }
        return this.generateStoreLocatorContentType();
    }

    private _getGenerationCurrentContent(): NonNullable<GenerateStoreLocatorStorePageContentBodyDto['params']>['currentContent'] {
        const storePageState = this.storePageState();
        if (!storePageState) {
            return undefined;
        }
        switch (this.generateStoreLocatorContentType()) {
            case GenerateStoreLocatorContentType.FAQ_BLOCK_QUESTION_OPTIMIZATION:
            case GenerateStoreLocatorContentType.FAQ_BLOCK_ANSWER_OPTIMIZATION:
                const item =
                    storePageState.updatedFaqBlock().data?.items[this.extraGenerationParams()?.descriptionItemData?.itemIndex ?? 0];
                return `question:${item?.question} answer:${item?.answer}`;
            default:
                return this.control()?.value || '';
        }
    }

    private _getGenerationContext(): NonNullable<GenerateStoreLocatorStorePageContentBodyDto['params']>['context'] {
        const storePageState = this.storePageState();
        if (!storePageState) {
            return [];
        }

        switch (this.generateStoreLocatorContentType()) {
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_TITLE_GENERATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_TITLE_OPTIMIZATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_SUBTITLE_GENERATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_SUBTITLE_OPTIMIZATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_CONTENT_GENERATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_CONTENT_OPTIMIZATION:
                return this._getDescriptionsGenerationContext(storePageState);
            case GenerateStoreLocatorContentType.FAQ_BLOCK_QUESTION_GENERATION:
            case GenerateStoreLocatorContentType.FAQ_BLOCK_ANSWER_GENERATION:
            case GenerateStoreLocatorContentType.FAQ_BLOCK_QUESTION_OPTIMIZATION:
            case GenerateStoreLocatorContentType.FAQ_BLOCK_ANSWER_OPTIMIZATION:
                return this._getFaqGenerationContext(storePageState);
            default:
                return undefined;
        }
    }

    private _getDescriptionsGenerationContext(
        storePageState: StoreLocatorStorePageState
    ): NonNullable<GenerateStoreLocatorStorePageContentBodyDto['params']>['context'] {
        const itemIndex = this.extraGenerationParams()?.descriptionItemData?.itemIndex ?? 0;
        const controlIndex = this.extraGenerationParams()?.descriptionItemData?.controlIndex ?? 0;
        switch (this.optimizeStoreLocatorContentType()) {
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_TITLE_GENERATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_TITLE_OPTIMIZATION:
                return [
                    {
                        [GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_SUBTITLE_GENERATION]:
                            storePageState.updatedDescriptionsBlock().data?.items[0].blocks[0].title || '',
                        [GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_CONTENT_GENERATION]:
                            storePageState.updatedDescriptionsBlock().data?.items[0].blocks[0].text || '',
                    },
                ];
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_SUBTITLE_GENERATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_SUBTITLE_OPTIMIZATION:
                return [
                    {
                        [GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_TITLE_GENERATION]:
                            storePageState.updatedDescriptionsBlock().data?.items[itemIndex].title || '',
                        [GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_CONTENT_GENERATION]:
                            storePageState.updatedDescriptionsBlock().data?.items[itemIndex].blocks[controlIndex].text || '',
                    },
                ];
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_CONTENT_GENERATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_CONTENT_OPTIMIZATION:
                return [
                    {
                        [GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_SUBTITLE_GENERATION]:
                            storePageState.updatedDescriptionsBlock().data?.items[itemIndex].blocks[
                                this.extraGenerationParams()?.descriptionItemData?.controlIndex ?? 0
                            ].title || '',
                        [GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_TITLE_GENERATION]:
                            storePageState.updatedDescriptionsBlock().data?.items[itemIndex].title || '',
                    },
                ];
            default:
                return undefined;
        }
    }

    private _getFaqGenerationContext(
        storePageState: StoreLocatorStorePageState
    ): NonNullable<GenerateStoreLocatorStorePageContentBodyDto['params']>['context'] {
        const questionIndex = this.extraGenerationParams()?.questionItemData?.itemIndex ?? 0;
        const questions = storePageState.updatedFaqBlock().data?.items;
        const questionsContext = questions?.splice(questionIndex, 1);
        return [
            {
                [GenerateStoreLocatorContentType.FAQ_BLOCK_GENERATION]: questionsContext
                    ?.map((item) => `question:${item.question} answer:${item.answer}`)
                    .join('\n'),
            },
        ];
    }
}
