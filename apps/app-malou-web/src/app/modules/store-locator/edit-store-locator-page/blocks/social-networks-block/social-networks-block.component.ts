import { CommonModule, NgStyle } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';

import { GetStoreLocatorDraftStoreDto } from '@malou-io/package-dto';
import { DEFAULT_PLACEHOLDER_IMAGE_URL, ensureAllKeysAreDefined, FacebookApiMediaType, NonNullableAllKeys } from '@malou-io/package-utils';

import { StoreLocatorPageBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-store-locator-edit-page-social-networks-block',
    templateUrl: './social-networks-block.component.html',
    styleUrls: ['./social-networks-block.component.scss'],
    imports: [NgStyle, CommonModule, MatIconModule, ImagePathResolverPipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageSocialNetworksBlockComponent extends StoreLocatorPageBlockComponent {
    readonly socialNetworksBlockStaticData = computed(() =>
        this.storeLocatorPageState()?.getBlockStaticData(StoreLocatorPageBlockType.SOCIAL_NETWORKS)
    );

    readonly socialNetworksBlockUpdatedData = computed(
        () => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.SOCIAL_NETWORKS)?.()?.data
    );

    readonly socialNetworks = computed(() => {
        try {
            return ensureAllKeysAreDefined(this.socialNetworksBlockStaticData()?.socialNetworks);
        } catch (error) {
            return this.getSocialNetworksPlaceholders();
        }
    });

    readonly title = computed(() => this.socialNetworksBlockUpdatedData()?.title ?? this.socialNetworksBlockStaticData()?.title);

    readonly isBlockSelected = computed(
        () => this.editStoreLocatorPageContext.currentEditingBlock() === StoreLocatorPageBlockType.SOCIAL_NETWORKS
    );

    readonly socialNetworksBlockTranslation = computed(() => this.storeLocatorPageTranslation().socialNetworksBlock);

    constructor() {
        super(StoreLocatorPageBlockType.SOCIAL_NETWORKS);
    }

    getSocialNetworksPlaceholders(): NonNullableAllKeys<GetStoreLocatorDraftStoreDto['socialNetworksBlock']['socialNetworks']> {
        return {
            instagram: {
                socialAccount: {
                    name: this._translate.instant('store_locator.blocks.socialNetworks.username_placeholder'),
                    logoUrl: DEFAULT_PLACEHOLDER_IMAGE_URL,
                    url: 'https://www.instagram.com/',
                    followersCount: '0',
                    publicationsCount: '0',
                },
                publications: Array.from({ length: 5 }, () => ({
                    imageUrl: DEFAULT_PLACEHOLDER_IMAGE_URL,
                    caption: this._translate.instant('store_locator.blocks.socialNetworks.caption_placeholder'),
                    publishedAt: new Date().toISOString(),
                    likesCount: '0',
                    commentsCount: 0,
                    mediaCount: Math.floor(Math.random() * 5) + 1,
                    mediaType: Math.random() < 0.5 ? FacebookApiMediaType.IMAGE : FacebookApiMediaType.VIDEO,
                    isFirstMediaVideo: Math.random() < 0.5,
                    url: 'https://www.instagram.com',
                    imageDescription: 'Default description for post',
                })),
            },
        };
    }
}
