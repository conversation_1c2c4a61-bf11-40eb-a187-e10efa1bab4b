<app-edit-store-locator-page-block-form-wrapper [contentFormTemplate]="contentFormTemplate" [styleFormTemplate]="styleFormTemplate" />

<ng-template #contentFormTemplate>
    <div class="flex flex-col gap-5">
        <div class="flex flex-col gap-5" [formGroup]="contentForm">
            <div class="flex flex-col gap-3 rounded-[10px] !bg-malou-color-background-light" formArrayName="items">
                @for (content of items.controls; let index = $index; track index) {
                    <div class="expansion-header malou-expansion-panel px-5" [formGroupName]="index">
                        <mat-accordion>
                            <mat-expansion-panel class="!border-none" hideToggle [expanded]="false">
                                <mat-expansion-panel-header class="group !pl-0" (click)="toggleControlPanelExpanded(index)">
                                    <div class="flex w-full items-center justify-between">
                                        <div class="malou-text-13--bold text-malou-color-text-1">
                                            {{ 'store_locator.edit_modal.faq.question_order' | translate: { order: index + 1 } }}
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <div class="flex items-center gap-2">
                                                @if (index > 0) {
                                                    <mat-icon
                                                        class="invisible !w-4 cursor-pointer !fill-malou-color-chart-pink--accent group-hover:visible"
                                                        [svgIcon]="SvgIcon.MINUS_CIRCLE"
                                                        (click)="removeQuestionItem($event, index)"></mat-icon>
                                                }
                                                <mat-icon
                                                    class="invisible !w-4 cursor-pointer group-hover:visible"
                                                    color="primary"
                                                    [svgIcon]="SvgIcon.ADD"
                                                    (click)="addQuestionItem($event, index)"></mat-icon>
                                            </div>
                                            <div class="flex items-center">
                                                <mat-icon
                                                    class="!w-3 transition-all"
                                                    color="primary"
                                                    [svgIcon]="SvgIcon.CHEVRON_DOWN"
                                                    [class.rotate-180]="getControlPanelExpanded(index)"></mat-icon>
                                            </div>
                                        </div>
                                    </div>
                                </mat-expansion-panel-header>

                                <ng-template matExpansionPanelContent>
                                    <div class="flex flex-col gap-4 !bg-malou-color-background-light">
                                        <div class="flex flex-col gap-1">
                                            <app-store-locator-edit-page-ai-suggestion
                                                [title]="'store_locator.edit_modal.faq.question.title' | translate"
                                                [generateStoreLocatorContentType]="
                                                    GenerateStoreLocatorContentType.FAQ_BLOCK_QUESTION_GENERATION
                                                "
                                                [optimizeStoreLocatorContentType]="
                                                    GenerateStoreLocatorContentType.FAQ_BLOCK_QUESTION_OPTIMIZATION
                                                "
                                                [extraGenerationParams]="{
                                                    questionItemData: {
                                                        itemIndex: index,
                                                    },
                                                }"
                                                [isDisabled]="shouldDisableModal()"
                                                [control]="content.get('question')">
                                                <app-text-area
                                                    class="malou-text-14--bold!"
                                                    formControlName="question"
                                                    [theme]="InputTextAreaTheme.EDIT_STORE_LOCATOR"
                                                    [defaultValue]="content.get('question')?.value"
                                                    [placeholder]="'store_locator.edit_modal.faq.question.placeholder' | translate"
                                                    [disabled]="false"
                                                    [errorMessage]="
                                                        content.get('question')?.errors &&
                                                        (content.get('question')?.errors?.minlength ||
                                                            content.get('question')?.errors?.maxlength)
                                                            ? ('store_locator.edit_modal.faq.question.length_error'
                                                              | translate
                                                                  : {
                                                                        maxLength: FaqBlockContentFormInputValidation.QUESTION_MAX_LENGTH,
                                                                    })
                                                            : ''
                                                    "
                                                    [defaultErrorMessage]="DefaultErrorMessage.REQUIRED"
                                                    [showCounterInTitle]="false"
                                                    [isEmojiPickerEnabled]="false"
                                                    [showMaxLength]="true"
                                                    [maxLength]="FaqBlockContentFormInputValidation.QUESTION_MAX_LENGTH"
                                                    [autocapitalize]="'none'">
                                                </app-text-area
                                            ></app-store-locator-edit-page-ai-suggestion>
                                        </div>
                                        <div class="flex flex-col gap-1">
                                            <app-store-locator-edit-page-ai-suggestion
                                                [title]="'store_locator.edit_modal.faq.answer.title' | translate"
                                                [generateStoreLocatorContentType]="
                                                    GenerateStoreLocatorContentType.FAQ_BLOCK_ANSWER_GENERATION
                                                "
                                                [optimizeStoreLocatorContentType]="
                                                    GenerateStoreLocatorContentType.FAQ_BLOCK_ANSWER_OPTIMIZATION
                                                "
                                                [extraGenerationParams]="{
                                                    questionItemData: {
                                                        itemIndex: index,
                                                    },
                                                }"
                                                [isDisabled]="shouldDisableModal()"
                                                [control]="content.get('answer')">
                                                <app-text-area
                                                    class="malou-text-14--bold!"
                                                    formControlName="answer"
                                                    [theme]="InputTextAreaTheme.EDIT_STORE_LOCATOR"
                                                    [defaultValue]="content.get('answer')?.value"
                                                    [placeholder]="'store_locator.edit_modal.faq.answer.placeholder' | translate"
                                                    [disabled]="false"
                                                    [errorMessage]="
                                                        content.get('answer')?.errors &&
                                                        (content.get('answer')?.errors?.minlength ||
                                                            content.get('answer')?.errors?.maxlength)
                                                            ? ('store_locator.edit_modal.faq.answer.length_error'
                                                              | translate
                                                                  : {
                                                                        maxLength: FaqBlockContentFormInputValidation.ANSWER_MAX_LENGTH,
                                                                    })
                                                            : ''
                                                    "
                                                    [showCounterInTitle]="false"
                                                    [defaultErrorMessage]="DefaultErrorMessage.REQUIRED"
                                                    [isEmojiPickerEnabled]="false"
                                                    [showMaxLength]="true"
                                                    [maxLength]="FaqBlockContentFormInputValidation.ANSWER_MAX_LENGTH"
                                                    [autocapitalize]="'none'">
                                                </app-text-area
                                            ></app-store-locator-edit-page-ai-suggestion>
                                        </div>
                                    </div>
                                </ng-template>
                            </mat-expansion-panel>
                        </mat-accordion>
                    </div>
                    @if (index !== items.controls.length - 1) {
                        <div class="border border-malou-color-background-dark"></div>
                    }
                }
            </div>
        </div>
    </div>
</ng-template>

<ng-template #styleFormTemplate>
    <form class="flex flex-col gap-5" [formGroup]="styleForm">
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="general">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit_modal.style.general' | translate }}
            </div>
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.backgroundColor')" />
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.title' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.titleColor')" />
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.faq.question' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.questionTextColor')" />
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.faq.answers' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.answerTextColor')" />
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.faq.question_background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.questionBackgroundColor')" />
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.faq.button' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.iconBackgroundColor')" />
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.icon' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.iconColor')" />
        </div>
    </form>
</ng-template>
