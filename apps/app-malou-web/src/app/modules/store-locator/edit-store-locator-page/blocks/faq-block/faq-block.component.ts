import { Ng<PERSON><PERSON>, NgStyle } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed } from '@angular/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { MyMaterialModule } from ':core/my-material-module/my-material.module';
import { StoreLocatorPageBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';

@Component({
    selector: 'app-store-locator-edit-page-faq-block',
    templateUrl: './faq-block.component.html',
    styleUrls: ['./faq-block.component.scss'],
    imports: [NgStyle, LazyLoadImageModule, NgClass, MyMaterialModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageFaqBlockComponent extends StoreLocatorPageBlockComponent {
    readonly faqsBlockStaticData = computed(() => this.storeLocatorPageState()?.getBlockStaticData(StoreLocatorPageBlockType.FAQ));

    readonly faqsBlockUpdatedData = computed(
        () => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.FAQ)?.()?.data
    );

    readonly isBlockSelected = computed(() => this.editStoreLocatorPageContext.currentEditingBlock() === StoreLocatorPageBlockType.FAQ);

    readonly items = computed(() => this.faqsBlockStaticData()?.items || []);
    readonly title = computed(() => this.faqsBlockStaticData()?.title);

    constructor() {
        super(StoreLocatorPageBlockType.FAQ);
    }
}
