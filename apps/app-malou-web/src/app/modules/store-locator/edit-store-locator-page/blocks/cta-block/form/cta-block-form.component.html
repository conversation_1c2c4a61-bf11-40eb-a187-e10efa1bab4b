<app-edit-store-locator-page-block-form-wrapper [contentFormTemplate]="contentFormTemplate" [styleFormTemplate]="styleFormTemplate" />

<ng-template #contentFormTemplate>
    <div class="flex flex-col gap-5" [formGroup]="contentForm">
        <div class="flex flex-col gap-1 px-5">
            <app-store-locator-edit-page-ai-suggestion
                [title]="'store_locator.edit_modal.controls.title.name' | translate"
                [generateStoreLocatorContentType]="GenerateStoreLocatorContentType.CTA_BLOCK_TITLE_GENERATION"
                [optimizeStoreLocatorContentType]="GenerateStoreLocatorContentType.CTA_BLOCK_TITLE_OPTIMIZATION"
                [isDisabled]="shouldDisableModal()"
                [control]="titleControl">
                <app-input-text
                    class="!malou-text-14--bold"
                    formControlName="title"
                    [defaultValue]="title()"
                    [disabled]="false"
                    [errorMessage]="
                        titleControl?.errors && (titleControl?.errors?.minlength || titleControl?.errors?.maxlength)
                            ? ('store_locator.edit_modal.controls.title.length_error'
                              | translate
                                  : {
                                        maxLength: CtaBlockContentFormInputValidation.TITLE_MAX_LENGTH,
                                    })
                            : ''
                    "
                    [defaultErrorMessage]="DefaultErrorMessage.REQUIRED"
                    [isEmojiPickerEnabled]="false"
                    [showMaxLength]="true"
                    [maxLength]="CtaBlockContentFormInputValidation.TITLE_MAX_LENGTH"
                    [placeholder]="'store_locator.edit_modal.controls.title.placeholder_text' | translate"
                    [autocapitalize]="'none'">
                </app-input-text
            ></app-store-locator-edit-page-ai-suggestion>
        </div>

        <div class="border border-malou-color-background-dark"></div>

        <div class="expansion-header malou-expansion-panel px-5">
            <mat-accordion>
                <mat-expansion-panel class="!border-none" hideToggle [expanded]="false">
                    <mat-expansion-panel-header class="!pl-0" (click)="isButtonsPanelExpanded.set(!isButtonsPanelExpanded())">
                        <div class="flex w-full items-center justify-between">
                            <div class="malou-text-13--bold text-malou-color-text-1">
                                {{ 'store_locator.edit_modal.controls.cta.header' | translate }}
                            </div>
                            <div class="flex items-center">
                                <mat-icon
                                    class="!w-3 transition-all"
                                    color="primary"
                                    [svgIcon]="SvgIcon.CHEVRON_DOWN"
                                    [class.rotate-180]="isButtonsPanelExpanded()"></mat-icon>
                            </div>
                        </div>
                    </mat-expansion-panel-header>

                    <ng-template matExpansionPanelContent>
                        <div class="flex flex-col gap-6 !bg-malou-color-background-light" formArrayName="ctaButtons">
                            @for (ctaButton of ctaButtons.controls; let index = $index; track index) {
                                <div class="group flex gap-2" [formGroupName]="index">
                                    <div class="flex w-[85%] flex-col gap-2">
                                        @if (ctaButton.get('text') && ctaButton.get('url')) {
                                            <app-store-locator-edit-page-call-to-action
                                                [urlOptions]="callToActionsSuggestions()"
                                                [canBeDisabled]="false"
                                                [ctaTextControl]="getCtaTextControl(index)!"
                                                [ctaUrlControl]="getCtaUrlControl(index)!"></app-store-locator-edit-page-call-to-action>
                                        }
                                    </div>
                                    <div class="flex items-start gap-2 pt-3">
                                        @if (index > 0) {
                                            <mat-icon
                                                class="invisible !w-4 cursor-pointer !fill-malou-color-chart-pink--accent group-hover:visible"
                                                [svgIcon]="SvgIcon.MINUS_CIRCLE"
                                                (click)="removeCtaButton(index)"></mat-icon>
                                        }
                                        <mat-icon
                                            class="invisible !w-4 cursor-pointer group-hover:visible"
                                            color="primary"
                                            [svgIcon]="SvgIcon.ADD"
                                            (click)="addCtaButton(index)"></mat-icon>
                                    </div>
                                </div>
                            }
                        </div>
                    </ng-template>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
    </div>
</ng-template>

<ng-template #styleFormTemplate>
    <form class="flex flex-col gap-5" [formGroup]="styleForm">
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="general">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit_modal.style.general' | translate }}
            </div>
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.backgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.title' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.titleColor')" />
        </div>
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="buttons">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit_modal.style.buttons.title' | translate }}
            </div>
            <app-store-locator-edit-page-radius-slider
                [title]="'store_locator.edit_modal.style.buttons.radius' | translate"
                [control]="styleForm.get('buttons.radius')"></app-store-locator-edit-page-radius-slider>

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.buttons.title' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.backgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.buttons.border' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.borderColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit_modal.style.buttons.text' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.textColor')" />
        </div>
    </form>
</ng-template>
