import { StoreLocatorCentralizationPageElementIds, StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

export enum PropertyType {
    BackgroundColor = 'backgroundColor',
    Color = 'color',
    Fill = 'fill',
    BorderColor = 'borderColor',
    BorderRadius = 'borderRadius',
}

export interface StylesConfigurationUpdate {
    data: {
        value: string;
        propertyType: PropertyType;
    }[];
}

export interface StoreStylesConfigurationUpdate extends StylesConfigurationUpdate {
    elementId: StoreLocatorRestaurantPageElementIds;
}

export interface CentralizationStylesConfigurationUpdate extends StylesConfigurationUpdate {
    elementId: StoreLocatorCentralizationPageElementIds;
}

export enum StoreLocatorInputType {
    TITLE = 'title',
    SUBTITLE = 'subtitle',
    CTA = 'cta',
    PHOTO = 'photo',
    TEXT = 'text',
    QUESTION = 'question',
    ANSWER = 'answer',
}
