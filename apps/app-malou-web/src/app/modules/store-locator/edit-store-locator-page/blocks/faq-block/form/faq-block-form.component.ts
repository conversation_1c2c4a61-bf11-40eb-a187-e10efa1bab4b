import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, inject, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { skip } from 'rxjs';

import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { StoreLocatorPageBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block-form.component';
import {
    PropertyType,
    StoreLocatorInputType,
} from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import {
    FaqBlockContentFormInputValidation,
    FaqBlockForm,
    FaqBlockItem,
    FaqBlockStyleData,
    FaqBlockStyleForm,
} from ':modules/store-locator/edit-store-locator-page/blocks/faq-block/faq-block.interface';
import { EditStoreLocatorPageAiSuggestionComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/ai-suggestion/ai-suggestion.component';
import { EditStoreLocatorPageColorSelectorComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/color-selector/color-selector.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { mapUpdateStylesConfiguration } from ':modules/store-locator/shared/edit-store-locator/utils/edit-store-locator-page.utils';
import { TextAreaComponent } from ':shared/components/text-area/text-area.component';

@Component({
    selector: 'app-store-locator-edit-page-faq-block-form',
    templateUrl: './faq-block-form.component.html',
    styleUrls: ['./faq-block-form.component.scss'],
    imports: [
        TranslateModule,
        MatTabsModule,
        MatIconModule,
        MatTooltipModule,
        MatButtonModule,
        StoreLocatorPageBlockFormComponent,
        ReactiveFormsModule,
        MatExpansionModule,
        EditStoreLocatorPageColorSelectorComponent,
        TextAreaComponent,
        EditStoreLocatorPageAiSuggestionComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageFaqBlockFormComponent extends StoreLocatorPageBlockFormComponent {
    private readonly _formBuilder = inject(FormBuilder);
    private readonly _cdr = inject(ChangeDetectorRef);

    readonly FaqBlockContentFormInputValidation = FaqBlockContentFormInputValidation;

    contentForm: FormGroup<FaqBlockForm>;
    styleForm: FormGroup<FaqBlockStyleForm>;

    readonly isControlPanelExpanded: WritableSignal<boolean[]> = signal([]);

    readonly faqBlockData = computed(() => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.FAQ)?.()?.data);

    constructor() {
        super();
        toObservable(this.currentEditingRestaurant)
            .pipe(skip(1), takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this._patchContentForm();
            });
        this._initContentForm();
        this._initStyleForm();
    }

    get items(): FaqBlockForm['items'] {
        return this.contentForm.controls.items as FaqBlockForm['items'];
    }

    getControlPanelExpanded(index: number): boolean {
        return this.isControlPanelExpanded()?.[index] ?? false;
    }

    toggleControlPanelExpanded(index: number): void {
        this.isControlPanelExpanded.update((currentState) => {
            currentState[index] = !currentState[index];
            return [...currentState];
        });
    }

    addQuestionItem(event: Event, index: number): void {
        event.stopPropagation();
        const newQuestionItem = this._createFaqItemGroupForm({
            answer: '',
            question: '',
        });
        this.items.insert(index + 1, newQuestionItem);
        this._cdr.detectChanges();
    }

    removeQuestionItem(event: Event, index: number): void {
        event.stopPropagation();
        if (index > 0) {
            this.items.removeAt(index);
        }
    }

    private _checkIfBlockInError(): void {
        const contentControls = this.contentForm.controls.items as FormArray;
        const isContentInError = contentControls.controls.some((contentControl) => {
            const questionControl = contentControl.get('question');
            const answerControl = contentControl.get('answer');
            return (!!questionControl?.errors && questionControl.dirty) || (!!answerControl?.errors && answerControl.dirty);
        });
        this.editStoreLocatorPageContext.isBlockInError.set({
            blockType: StoreLocatorPageBlockType.FAQ,
            isError: isContentInError,
        });
    }

    private _initContentForm(): void {
        const faqBlockData = this.faqBlockData();

        if (!this.contentForm) {
            this.contentForm = this._formBuilder.group({
                items: this._formBuilder.array(faqBlockData?.items.map((item) => this._createFaqItemGroupForm(item)) || []),
            });

            this.isControlPanelExpanded.set(faqBlockData?.items.map(() => false) ?? []);

            this.contentForm.valueChanges.subscribe((value: { items: FaqBlockItem[] }) => {
                const blockData = this.faqBlockData();
                this._checkIfBlockInError();
                if (blockData && blockData?.items.length !== 0) {
                    this._trackEditContentChanges(value.items);
                    this.storeLocatorPageState()?.updateBlock({
                        blockType: StoreLocatorPageBlockType.FAQ,
                        blockData: {
                            items: value.items.map((item) => ({
                                question: item.question,
                                answer: item.answer,
                            })),
                        },
                    });
                }
            });
        }
    }

    private _createFaqItemGroupForm(data: FaqBlockItem): FormGroup {
        return this._formBuilder.group({
            question: this._formBuilder.control(data.question, {
                validators: [Validators.required, Validators.maxLength(FaqBlockContentFormInputValidation.QUESTION_MAX_LENGTH)],
                nonNullable: true,
            }),
            answer: this._formBuilder.control(data.answer, {
                validators: [Validators.required, Validators.maxLength(FaqBlockContentFormInputValidation.ANSWER_MAX_LENGTH)],
                nonNullable: true,
            }),
        });
    }

    private _patchContentForm(): void {
        const faqBlockData = this.faqBlockData();
        if (!faqBlockData) {
            return;
        }
        this.items.clear();
        this.isControlPanelExpanded.set(faqBlockData.items.map(() => false));
        (faqBlockData.items ?? []).forEach((item) => {
            this.items.push(this._createFaqItemGroupForm(item));
        });
        this.contentForm.markAsPristine();
    }

    private _initStyleForm(): void {
        const styleMap = this.getStyleMap([
            StoreLocatorRestaurantPageElementIds.FAQ_WRAPPER,
            StoreLocatorRestaurantPageElementIds.FAQ_TITLE,
            StoreLocatorRestaurantPageElementIds.FAQ_ITEM,
            StoreLocatorRestaurantPageElementIds.FAQ_ICON_WRAPPER,
            StoreLocatorRestaurantPageElementIds.FAQ_ICON,
            StoreLocatorRestaurantPageElementIds.FAQ_ITEM_QUESTION,
            StoreLocatorRestaurantPageElementIds.FAQ_ITEM_ANSWER,
        ]);

        const faqWrapper = styleMap[StoreLocatorRestaurantPageElementIds.FAQ_WRAPPER] ?? {};
        const faqTitle = styleMap[StoreLocatorRestaurantPageElementIds.FAQ_TITLE] ?? {};
        const faqItem = styleMap[StoreLocatorRestaurantPageElementIds.FAQ_ITEM] ?? {};
        const faqIconWrapper = styleMap[StoreLocatorRestaurantPageElementIds.FAQ_ICON_WRAPPER] ?? {};
        const faqIcon = styleMap[StoreLocatorRestaurantPageElementIds.FAQ_ICON] ?? {};
        const faqItemQuestion = styleMap[StoreLocatorRestaurantPageElementIds.FAQ_ITEM_QUESTION] ?? {};
        const faqItemAnswer = styleMap[StoreLocatorRestaurantPageElementIds.FAQ_ITEM_ANSWER] ?? {};

        this.styleForm = this._formBuilder.group({
            general: this._formBuilder.group({
                backgroundColor: this._formBuilder.control(faqWrapper.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                titleColor: this._formBuilder.control(faqTitle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                questionTextColor: this._formBuilder.control(faqItemQuestion.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                answerTextColor: this._formBuilder.control(faqItemAnswer.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                questionBackgroundColor: this._formBuilder.control(faqItem.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                iconBackgroundColor: this._formBuilder.control(faqIconWrapper.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                iconColor: this._formBuilder.control(faqIcon.fill, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
        });

        this.styleForm.valueChanges.subscribe((value) => {
            this._updateStyleConfiguration(value as FaqBlockStyleData);
        });
    }

    private _updateStyleConfiguration(value: FaqBlockStyleData): void {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();

        if (organizationStyleConfiguration) {
            const dataToUpdate = mapUpdateStylesConfiguration(organizationStyleConfiguration, [
                {
                    elementId: StoreLocatorRestaurantPageElementIds.FAQ_WRAPPER,
                    data: [
                        {
                            value: value.general.backgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.FAQ_TITLE,
                    data: [
                        {
                            value: value.general.titleColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.FAQ_ITEM,
                    data: [
                        {
                            value: value.general.questionTextColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.FAQ_ITEM_ANSWER,
                    data: [
                        {
                            value: value.general.answerTextColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.FAQ_ITEM_QUESTION,
                    data: [
                        {
                            value: value.general.questionBackgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.FAQ_ICON_WRAPPER,
                    data: [
                        {
                            value: value.general.iconBackgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.FAQ_ICON,
                    data: [
                        {
                            value: value.general.iconColor,
                            propertyType: PropertyType.Fill,
                        },
                    ],
                },
            ]);
            this.trackEditStyleActivity({
                block: StoreLocatorPageBlockType.FAQ,
                changes: dataToUpdate,
            });
            organizationStyleConfiguration.updateStyle(dataToUpdate);
        }
    }

    private _trackEditContentChanges(changes: FaqBlockItem[]): void {
        const faqBlockData = this.faqBlockData();
        if (faqBlockData) {
            changes.forEach((content, index) => {
                const currentContent = faqBlockData.items[index];
                if (currentContent) {
                    if (currentContent.question !== content.question) {
                        this.trackEditContentActivity({
                            block: StoreLocatorPageBlockType.FAQ,
                            element: StoreLocatorInputType.QUESTION,
                        });
                    }
                    if (currentContent.answer !== content.answer) {
                        this.trackEditContentActivity({
                            block: StoreLocatorPageBlockType.FAQ,
                            element: StoreLocatorInputType.ANSWER,
                        });
                    }
                }
            });
        }
    }
}
