<div
    class="relative flex h-fit flex-col"
    [ngClass]="{
        'cursor-not-allowed': shouldShowCursorNotAllowed(),
        'cursor-pointer opacity-60 hover:opacity-100': !isBlockSelected(),
    }"
    [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_WRAPPER]"
    (click)="handleShowFormBlock(StoreLocatorPageBlockType.SOCIAL_NETWORKS)">
    <div class="flex w-full flex-col items-center justify-center px-10 py-14 sm:mx-auto">
        <h2
            class="max-w-[95%] break-words pb-8 text-center text-3xl font-extrabold uppercase sm:text-4xl"
            [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_TITLE]">
            {{ title() }}
        </h2>

        <div class="flex items-center justify-center gap-4">
            <a
                class="flex items-center gap-4"
                target="_blank"
                [href]="socialNetworks()!.instagram.socialAccount.url"
                [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE]">
                <div class="relative h-[80px] w-[80px] overflow-hidden rounded-full">
                    <img
                        class="h-full w-full object-cover object-center"
                        [src]="socialNetworks()!.instagram.socialAccount.logoUrl"
                        [alt]="socialNetworks()!.instagram.socialAccount.name" />
                </div>

                <div class="flex flex-col">
                    <span
                        class="text-xl font-bold"
                        [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.SOCIAL_NETWORKS_PROFILE_NAME]"
                        >{{ socialNetworks()!.instagram.socialAccount.name }}</span
                    >
                    <span class="text-sm"
                        >{{ socialNetworks()!.instagram.socialAccount.followersCount }} {{ socialNetworksBlockTranslation().followers }} -
                        {{ socialNetworks()!.instagram.socialAccount.publicationsCount }}
                        {{ socialNetworksBlockTranslation().publications }}</span
                    >
                </div>
            </a>
        </div>

        <div class="no-scrollbar mx-auto flex w-full flex-nowrap justify-center gap-3 overflow-x-auto py-12 3xl:justify-normal">
            @for (publication of socialNetworks()!.instagram.publications; track $index) {
                <a class="flex items-stretch gap-4" target="_blank" [href]="publication.url">
                    <div class="w-[250px] flex-shrink-0 overflow-hidden rounded-md bg-white shadow-xl">
                        <div class="relative h-[360px] w-[250px]">
                            <img
                                class="h-full w-full object-cover object-center"
                                width="250"
                                height="360"
                                [src]="publication.imageUrl"
                                [alt]="publication.imageDescription" />
                            @if (publication.isFirstMediaVideo) {
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <mat-icon class="absolute inset-0 m-auto h-8 w-8" [svgIcon]="SvgIcon.PLAY"></mat-icon>
                                </div>
                            }
                        </div>
                        <div class="flex flex-col gap-3 p-4">
                            <div class="relative flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <img
                                        class="h-4 w-4"
                                        [src]="
                                            'like' | imagePathResolver: { folder: 'social-post-previews/instagram', extensionFormat: 'svg' }
                                        " />
                                    <img
                                        class="h-4 w-4"
                                        [src]="
                                            'comment'
                                                | imagePathResolver: { folder: 'social-post-previews/instagram', extensionFormat: 'svg' }
                                        " />
                                    <img
                                        class="h-4 w-4"
                                        [src]="
                                            'send' | imagePathResolver: { folder: 'social-post-previews/instagram', extensionFormat: 'svg' }
                                        " />
                                </div>

                                @if (publication.mediaCount > 1) {
                                    <div class="absolute inset-0 m-auto flex items-center justify-center gap-1">
                                        @for (media of [].constructor(publication.mediaCount); track $index) {
                                            <div
                                                class="rounded-full"
                                                [ngClass]="
                                                    $index === 0
                                                        ? 'h-[8px] w-[8px] bg-[#2C86FD]'
                                                        : 'h-[6px] w-[6px] bg-[#0a2540] opacity-50'
                                                "></div>
                                        }
                                    </div>
                                }
                                <div>
                                    <img
                                        class="h-4 w-4"
                                        [src]="
                                            'save' | imagePathResolver: { folder: 'social-post-previews/instagram', extensionFormat: 'svg' }
                                        " />
                                </div>
                            </div>
                            @if (publication.likesCount !== '0') {
                                <p class="text-xs font-extrabold">
                                    {{ publication.likesCount }} {{ socialNetworksBlockTranslation().likes }}
                                </p>
                            }

                            <p
                                class="line-clamp-8 overflow-hidden text-xs text-gray-700"
                                style="display: -webkit-box; -webkit-box-orient: vertical; line-clamp: 8; -webkit-line-clamp: 8">
                                {{ publication.caption }}
                            </p>
                        </div>
                    </div>
                </a>
            }
        </div>
    </div>
    <div
        class="absolute left-0 top-0 h-full w-full bg-transparent hover:border-4 hover:border-malou-color-primary"
        [class.border-malou-color-primary]="isBlockSelected()"
        [class.border-4]="isBlockSelected()"></div>
</div>
