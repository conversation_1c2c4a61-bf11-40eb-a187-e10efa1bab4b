import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { StoreLocatorPageBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { StoreLocatorContext } from ':modules/store-locator/store-locator.context';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-store-locator-edit-page-footer-block',
    templateUrl: './footer-block.component.html',
    styleUrls: ['./footer-block.component.scss'],
    imports: [ImagePathResolverPipe, TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageFooterBlockComponent extends StoreLocatorPageBlockComponent {
    private readonly _storeLocatorContext = inject(StoreLocatorContext);

    readonly shouldDisplayWhiteMark = computed(
        () => this._storeLocatorContext.storeLocatorOrganizationConfiguration()?.shouldDisplayWhiteMark
    );

    readonly footerTranslation = computed(() => this.storeLocatorPageTranslation().footer);

    constructor() {
        super(StoreLocatorPageBlockType.FOOTER);
    }
}
