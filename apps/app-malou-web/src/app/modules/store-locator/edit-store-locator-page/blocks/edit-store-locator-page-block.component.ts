import { ChangeDetectionStrategy, Component, computed, inject, output, signal, Signal, WritableSignal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { StoreLocatorLanguage, StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { ToastService } from ':core/services/toast.service';
import { EditStoreLocatorPageContext } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.context';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { StoreLocatorOrganizationStylesConfiguration } from ':modules/store-locator/shared/edit-store-locator/models/store-locator-organization-styles-configuration';
import { parseConfigurationStyleClassesToCssStyle } from ':modules/store-locator/shared/edit-store-locator/utils/edit-store-locator-page.utils';
import { getStoreLocatorPageTranslation } from ':modules/store-locator/translation/store-locator.translation';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { Illustration } from ':shared/pipes/illustration-path-resolver.pipe';
import { ImageAssets } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-edit-store-locator-page-block',
    template: '',
    imports: [MatButtonModule, MatTabsModule, TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageBlockComponent {
    readonly emitChangeFormBlock = output<StoreLocatorPageBlockType>();

    readonly _translate = inject(TranslateService);
    readonly editStoreLocatorPageContext = inject(EditStoreLocatorPageContext);
    readonly toastService = inject(ToastService);

    readonly ImageAssets = ImageAssets;
    readonly StoreLocatorPageBlockType = StoreLocatorPageBlockType;
    readonly Illustration = Illustration;
    readonly SvgIcon = SvgIcon;
    readonly StoreLocatorRestaurantPageElementIds = StoreLocatorRestaurantPageElementIds;

    readonly organizationStyleConfiguration: Signal<StoreLocatorOrganizationStylesConfiguration | null> = computed(() =>
        this.editStoreLocatorPageContext.organizationStyleConfiguration()
    );

    readonly blockType: WritableSignal<StoreLocatorPageBlockType> = signal(StoreLocatorPageBlockType.INFORMATION);
    readonly shouldShowCursorNotAllowed = computed(
        () =>
            this.editStoreLocatorPageContext.isBlockInError().blockType !== this.blockType() &&
            this.editStoreLocatorPageContext.isBlockInError().isError
    );
    readonly shouldDisableModal = computed(() => this.editStoreLocatorPageContext.shouldDisableModal());

    readonly storeLocatorPageState = computed(() => this.editStoreLocatorPageContext.selectedRestaurantStorePageState());

    readonly stylesMap = computed(() => {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();
        if (!organizationStyleConfiguration) {
            return {};
        }
        const elementIds = Object.values(StoreLocatorRestaurantPageElementIds);
        const styleMap: Record<StoreLocatorRestaurantPageElementIds, Record<string, string>> = {} as Record<
            StoreLocatorRestaurantPageElementIds,
            Record<string, string>
        >;
        elementIds.forEach((key) => {
            const blockElementStyleClasses = organizationStyleConfiguration.getStorePageElementStyle(key);
            if (blockElementStyleClasses) {
                styleMap[key] = parseConfigurationStyleClassesToCssStyle(blockElementStyleClasses, {
                    colors: organizationStyleConfiguration.colors,
                    fonts: organizationStyleConfiguration.fonts,
                });
            } else {
                styleMap[key] = {};
            }
        });
        return styleMap;
    });

    readonly storeLocatorPageTranslation = computed(() => {
        const storeLocatorPageLanguage = this.storeLocatorPageState()?.getLanguage();
        if (storeLocatorPageLanguage) {
            return getStoreLocatorPageTranslation(storeLocatorPageLanguage);
        }
        return getStoreLocatorPageTranslation(StoreLocatorLanguage.FR);
    });

    constructor(blockType: StoreLocatorPageBlockType) {
        this.blockType.set(blockType);
    }

    handleShowFormBlock(blockType: StoreLocatorPageBlockType): void {
        if (!this.editStoreLocatorPageContext.isBlockInError().isError && !this.shouldDisableModal()) {
            this.editStoreLocatorPageContext.currentEditingBlock.set(blockType);
            this.storeLocatorPageState()?.initBlockUpdateState(blockType);
            this.emitChangeFormBlock.emit(blockType);
            return;
        } else if (
            this.editStoreLocatorPageContext.isBlockInError().isError &&
            this.editStoreLocatorPageContext.isBlockInError().blockType !== blockType
        ) {
            this.toastService.openErrorToast(this._translate.instant('store_locator.edit_modal.common.block_in_error'));
        }
    }
}
