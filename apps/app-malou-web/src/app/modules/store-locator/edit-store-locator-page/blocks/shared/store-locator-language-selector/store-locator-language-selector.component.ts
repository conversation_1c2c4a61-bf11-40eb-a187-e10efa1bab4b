import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { StoreLocatorLanguage } from '@malou-io/package-utils';

import { SelectComponent } from ':shared/components/select/select.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { FlagPathResolverPipe } from ':shared/pipes/flag-path-resolver.pipe';

@Component({
    selector: 'app-store-locator-edit-page-language-selector',
    templateUrl: './store-locator-language-selector.component.html',
    styleUrls: ['./store-locator-language-selector.component.scss'],
    imports: [LazyLoadImageModule, MatIconModule, TranslateModule, SelectComponent, FlagPathResolverPipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditStoreLocatorPageLanguageSelectorComponent {
    readonly languages = input.required<StoreLocatorLanguage[]>();
    readonly currentSelectedLanguage = input.required<StoreLocatorLanguage>();
    readonly disabled = input<boolean>(false);
    readonly control = input.required<FormControl<StoreLocatorLanguage>>();

    readonly SvgIcon = SvgIcon;
}
