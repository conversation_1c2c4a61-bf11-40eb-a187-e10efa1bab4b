import { ChangeDetectionStrategy, Component, computed, inject, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { isNil } from 'lodash';
import { combineLatest, skip } from 'rxjs';

import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { StoreLocatorPageBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block-form.component';
import {
    PropertyType,
    StoreLocatorInputType,
} from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import {
    InformationBlockContent,
    InformationBlockContentForm,
    InformationBlockContentFormInputValidation,
    InformationBlockCtaFromGroup,
    InformationBlockStyleData,
    InformationBlockStyleForm,
} from ':modules/store-locator/edit-store-locator-page/blocks/information-block/information-block.interface';
import { EditStoreLocatorPageAiSuggestionComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/ai-suggestion/ai-suggestion.component';
import { EditStoreLocatorPageCallToActionComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/call-to-action/call-to-action.component';
import { EditStoreLocatorPageColorSelectorComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/color-selector/color-selector.component';
import { EditStoreLocatorPageRadiusSliderComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/radius-slider/radius-slider.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import {
    getRadiusValue,
    mapUpdateStylesConfiguration,
} from ':modules/store-locator/shared/edit-store-locator/utils/edit-store-locator-page.utils';
import { ImageUploaderComponent } from ':shared/components/image-uploader/image-uploader.component';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { Media } from ':shared/models';
import { UrlValidator } from ':shared/validators/url.validator';

@Component({
    selector: 'app-store-locator-edit-page-information-block-form',
    templateUrl: './information-block-form.component.html',
    styleUrls: ['./information-block-form.component.scss'],
    imports: [
        MatTabsModule,
        TranslateModule,
        MatIconModule,
        MatTooltipModule,
        ReactiveFormsModule,
        ImageUploaderComponent,
        MatExpansionModule,
        InputTextComponent,
        MatButtonModule,
        EditStoreLocatorPageColorSelectorComponent,
        StoreLocatorPageBlockFormComponent,
        EditStoreLocatorPageAiSuggestionComponent,
        EditStoreLocatorPageCallToActionComponent,
        EditStoreLocatorPageRadiusSliderComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageInformationBlockFormComponent extends StoreLocatorPageBlockFormComponent {
    private readonly _formBuilder = inject(FormBuilder);

    readonly InformationBlockContentFormInputValidation = InformationBlockContentFormInputValidation;

    contentForm: FormGroup<InformationBlockContentForm>;
    styleForm: FormGroup<InformationBlockStyleForm>;

    readonly isButtonsPanelExpanded: WritableSignal<boolean> = signal<boolean>(false);
    readonly shouldDisableSecondaryCta: WritableSignal<boolean> = signal<boolean>(false);

    readonly uploadedMedia: WritableSignal<Media | null> = signal<Media | null>(null);

    readonly informationBlockUpdatedData = computed(
        () => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.INFORMATION)?.()?.data
    );

    readonly title = computed(() => this.informationBlockUpdatedData()?.title ?? '');

    readonly callToActionsSuggestions = computed(() => this.storeLocatorPageState()?.getCallToActionsSuggestions() ?? []);

    constructor() {
        super();
        this._initContentForm();
        this._initStyleForm();
        combineLatest([toObservable(this.currentEditingRestaurant), toObservable(this.selectedRestaurantPagesLanguage)])
            .pipe(skip(1), takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this._patchContentForm();
            });
    }

    get titleControl(): FormControl | null {
        return this.contentForm.get('title') as FormControl | null;
    }

    get primaryCtaTextControl(): FormControl | null {
        return this.contentForm.get('primaryCta.text') as FormControl | null;
    }

    get primaryCtaUrlControl(): FormControl | null {
        return this.contentForm.get('primaryCta.url') as FormControl | null;
    }

    get secondaryCtaTextControl(): FormControl | null {
        return this.contentForm.get('secondaryCta.text') as FormControl | null;
    }

    get secondaryCtaUrlControl(): FormControl | null {
        return this.contentForm.get('secondaryCta.url') as FormControl | null;
    }

    onMediaSelected(media: Media | null): void {
        const informationBlockData = this.informationBlockUpdatedData();
        if (informationBlockData && media) {
            this.storeLocatorPageState()?.updateBlock({
                blockType: StoreLocatorPageBlockType.INFORMATION,
                blockData: {
                    ...informationBlockData,
                    image: {
                        description: informationBlockData.image.description,
                        url: media.urls.original,
                    },
                },
            });
        }
    }

    onPrimaryCtaToggle(isPrimaryCtaToggle: boolean): void {
        if (!isPrimaryCtaToggle) {
            this.shouldDisableSecondaryCta.set(true);
            this.secondaryCtaTextControl?.disable();
            this.secondaryCtaUrlControl?.disable();
        } else {
            this.shouldDisableSecondaryCta.set(false);
            this.secondaryCtaUrlControl?.enable();
            this.secondaryCtaTextControl?.enable();
        }
    }

    private _checkIfBlockInError(): void {
        const titleControl = this.titleControl;
        const ctaTextControl = this.contentForm.get('cta.text');
        const ctaUrlControl = this.contentForm.get('cta.url');
        if (!titleControl || !ctaTextControl || !ctaUrlControl) {
            return;
        }
        const isTitleInError = !!titleControl?.errors && titleControl.dirty;
        const isCtaTextInError = !!ctaTextControl?.errors && ctaTextControl.dirty;
        const isCtaUrlInError = !!ctaUrlControl?.errors && ctaUrlControl.dirty;
        this.editStoreLocatorPageContext.isBlockInError.set({
            blockType: StoreLocatorPageBlockType.INFORMATION,
            isError: isTitleInError || isCtaTextInError || isCtaUrlInError,
        });
    }

    private _initContentForm(): void {
        const primaryCta = this.informationBlockUpdatedData()?.ctas?.[0];
        const secondaryCta = this.informationBlockUpdatedData()?.ctas?.[1];

        this.uploadedMedia.set(
            new Media({
                urls: { original: this.informationBlockUpdatedData()?.image?.url ?? '' },
                dimensions: {},
            })
        );

        this.contentForm = this._formBuilder.group({
            title: this._formBuilder.control(this.informationBlockUpdatedData()?.title ?? '', {
                validators: [Validators.required, Validators.maxLength(InformationBlockContentFormInputValidation.TITLE_MAX_LENGTH)],
                nonNullable: true,
            }),
            primaryCta: this._getCtaFromGroup(primaryCta),
            secondaryCta: this._getCtaFromGroup(secondaryCta),
        });

        this.contentForm.valueChanges.pipe(skip(1), takeUntilDestroyed(this.destroyRef)).subscribe((value: InformationBlockContent) => {
            const informationBlockData = this.informationBlockUpdatedData();
            this._checkIfBlockInError();
            if (informationBlockData) {
                const primaryCtaData = value.primaryCta;
                const secondaryCtaData = value.secondaryCta;
                const ctas: {
                    url: string;
                    text: string;
                }[] = [];
                if (primaryCtaData) {
                    ctas.push({
                        text: primaryCtaData.text,
                        url: primaryCtaData.url,
                    });
                }
                if (secondaryCtaData) {
                    ctas.push({
                        text: secondaryCtaData.text,
                        url: secondaryCtaData.url,
                    });
                }
                this._trackEditContentChanges(value);
                this.storeLocatorPageState()?.updateBlock({
                    blockType: StoreLocatorPageBlockType.INFORMATION,
                    blockData: {
                        ...informationBlockData,
                        title: value.title,
                        ctas,
                    },
                });
            }
        });
    }

    private _getCtaFromGroup(cta: { text: string; url: string } | undefined): InformationBlockCtaFromGroup {
        return this._formBuilder.group({
            text: this._formBuilder.control(
                {
                    value: cta?.text ?? '',
                    disabled: isNil(cta),
                },
                {
                    validators: [Validators.required, Validators.maxLength(InformationBlockContentFormInputValidation.CTA_TEXT_MAX_LENGTH)],
                    nonNullable: true,
                }
            ),
            url: this._formBuilder.control(
                { value: cta?.url ?? '', disabled: isNil(cta) },
                {
                    validators: [Validators.required, UrlValidator()],
                    nonNullable: true,
                }
            ),
        });
    }

    private _patchContentForm(): void {
        const primaryCta = this.informationBlockUpdatedData()?.ctas?.[0];
        const secondaryCta = this.informationBlockUpdatedData()?.ctas?.[1];

        this.uploadedMedia.set(
            new Media({
                urls: { original: this.informationBlockUpdatedData()?.image?.url ?? '' },
                dimensions: {},
            })
        );

        this.contentForm.patchValue({
            title: this.informationBlockUpdatedData()?.title,
            primaryCta: {
                text: primaryCta?.text ?? '',
                url: primaryCta?.url ?? '',
            },
            secondaryCta: {
                text: secondaryCta?.text ?? '',
                url: secondaryCta?.url ?? '',
            },
        });
        this.contentForm.get('primaryCta.text')?.[!!primaryCta ? 'enable' : 'disable']();
        this.contentForm.get('primaryCta.url')?.[!!primaryCta ? 'enable' : 'disable']();
        this.contentForm.get('secondaryCta.text')?.[!!secondaryCta ? 'enable' : 'disable']();
        this.contentForm.get('secondaryCta.url')?.[!!secondaryCta ? 'enable' : 'disable']();
        this.contentForm.markAsPristine();
    }

    private _initStyleForm(): void {
        const styleMap = this.getStyleMap([
            StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER,
            StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE,
            StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS,
            StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1,
            StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2,
        ]);

        const informationBlockWrapperStyle = styleMap[StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER] || {};
        const informationBlockTitleStyle = styleMap[StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE] || {};
        const informationBlockIconStyle = styleMap[StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS] || {};
        const informationBlockPrimaryCtaStyle = styleMap[StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1] || {};
        const informationBlockSecondaryCtaStyle = styleMap[StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2] || {};

        this.styleForm = this._formBuilder.group({
            general: this._formBuilder.group({
                titleColor: this._formBuilder.control(informationBlockTitleStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                backgroundColor: this._formBuilder.control(informationBlockWrapperStyle.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                textColor: this._formBuilder.control(informationBlockWrapperStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                iconColor: this._formBuilder.control(informationBlockIconStyle.fill, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
            buttons: this._formBuilder.group({
                radius: this._formBuilder.control(getRadiusValue(informationBlockPrimaryCtaStyle.borderRadius), {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                primaryBackgroundColor: this._formBuilder.control(informationBlockPrimaryCtaStyle.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                primaryBorderColor: this._formBuilder.control(informationBlockPrimaryCtaStyle.borderColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                primaryTextColor: this._formBuilder.control(informationBlockPrimaryCtaStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                secondaryBackgroundColor: this._formBuilder.control(informationBlockSecondaryCtaStyle.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                secondaryBorderColor: this._formBuilder.control(informationBlockSecondaryCtaStyle.borderColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                secondaryTextColor: this._formBuilder.control(informationBlockSecondaryCtaStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
        });
        this.styleForm.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((value) => {
            this._updateStyleConfiguration(value as InformationBlockStyleData);
        });
    }

    private _updateStyleConfiguration(value: InformationBlockStyleData): void {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();

        if (organizationStyleConfiguration) {
            const dataToUpdate = mapUpdateStylesConfiguration(organizationStyleConfiguration, [
                {
                    elementId: StoreLocatorRestaurantPageElementIds.INFORMATION_WRAPPER,
                    data: [
                        {
                            propertyType: PropertyType.BackgroundColor,
                            value: value.general.backgroundColor,
                        },
                        {
                            propertyType: PropertyType.Color,
                            value: value.general.textColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.INFORMATION_TITLE,
                    data: [
                        {
                            propertyType: PropertyType.Color,
                            value: value.general.titleColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.INFORMATION_ICONS,
                    data: [
                        {
                            propertyType: PropertyType.Fill,
                            value: value.general.iconColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_1,
                    data: [
                        {
                            propertyType: PropertyType.BackgroundColor,
                            value: value.buttons.primaryBackgroundColor,
                        },
                        {
                            propertyType: PropertyType.BorderColor,
                            value: value.buttons.primaryBorderColor,
                        },
                        {
                            propertyType: PropertyType.Color,
                            value: value.buttons.primaryTextColor,
                        },
                        {
                            propertyType: PropertyType.BorderRadius,
                            value: value.buttons.radius.toString(),
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER_CTA_2,
                    data: [
                        {
                            propertyType: PropertyType.BackgroundColor,
                            value: value.buttons.secondaryBackgroundColor,
                        },
                        {
                            propertyType: PropertyType.BorderColor,
                            value: value.buttons.secondaryBorderColor,
                        },
                        {
                            propertyType: PropertyType.Color,
                            value: value.buttons.secondaryTextColor,
                        },
                        {
                            propertyType: PropertyType.BorderRadius,
                            value: value.buttons.radius.toString(),
                        },
                    ],
                },
            ]);
            this.trackEditStyleActivity({
                block: StoreLocatorPageBlockType.INFORMATION,
                changes: dataToUpdate,
            });
            organizationStyleConfiguration.updateStyle(dataToUpdate);
        }
    }

    private _trackEditContentChanges(changes: InformationBlockContent): void {
        const informationBlockData = this.informationBlockUpdatedData();
        if (informationBlockData) {
            if (informationBlockData.title !== changes.title) {
                this.trackEditContentActivity({
                    block: StoreLocatorPageBlockType.INFORMATION,
                    element: StoreLocatorInputType.TITLE,
                });
            }
            if (
                informationBlockData.ctas?.[0]?.text !== changes.primaryCta?.text ||
                informationBlockData.ctas?.[0]?.url !== changes.primaryCta?.url
            ) {
                this.trackEditContentActivity({
                    block: StoreLocatorPageBlockType.INFORMATION,
                    element: StoreLocatorInputType.CTA,
                });
            }
            if (
                informationBlockData.ctas?.[1]?.url !== changes.secondaryCta?.url ||
                informationBlockData.ctas?.[1]?.text !== changes.secondaryCta?.text
            ) {
                this.trackEditContentActivity({
                    block: StoreLocatorPageBlockType.INFORMATION,
                    element: StoreLocatorInputType.CTA,
                });
            }
        }
    }
}
