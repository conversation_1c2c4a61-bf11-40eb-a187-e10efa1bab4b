<div class="flex flex-col justify-center gap-0">
    <div class="flex items-center" [class.justify-between]="!!title()" [class.justify-center]="!title()">
        @if (title()) {
            <div class="!malou-text-12--bold text-malou-color-text-1">
                {{ title() }}
            </div>
        }

        @if (!shouldHideMagicWand() && !forceHideMagicWand()) {
            <button
                class="malou-btn-icon"
                mat-icon-button
                [disabled]="isDisabled() || isLoadingOptimization()"
                [ngClass]="{
                    'opacity-50 hover:cursor-not-allowed': isDisabled() || isLoadingGeneration(),
                }"
                (click)="generateStoreLocatorContent($event)">
                @if (isLoadingGeneration()) {
                    <app-malou-spinner [size]="'xs'" [color]="'#AC32B7'"></app-malou-spinner>
                } @else {
                    <mat-icon class="!h-4 !w-4 text-malou-color-chart-purple--accent" [svgIcon]="SvgIcon.MAGIC_WAND"></mat-icon>
                }
            </button>
        }
    </div>
    <div
        class="rounded-[10px] border border-malou-color-background-dark"
        [ngClass]="{
            'border-none': control() && control()?.errors,
            '!border-malou-color-border-secondary': control() && !control()?.errors && isFocused(),
        }">
        <ng-content></ng-content>
        @if (shouldHideMagicWand() && !forceHideButtons()) {
            <div class="my-3.5 ml-5 flex items-center gap-x-2.5">
                <app-button
                    [theme]="'secondary--alt'"
                    [loading]="isLoadingGeneration()"
                    [disabled]="isDisabled() || isLoadingOptimization()"
                    (buttonClick)="generateStoreLocatorContent()">
                    <ng-template #textTemplate>
                        <div class="flex items-center">
                            <mat-icon
                                class="mb-1 mr-1 h-4 !w-4 text-malou-color-chart-purple--accent"
                                icon
                                [svgIcon]="SvgIcon.MAGIC_WAND"></mat-icon>
                            <span class="text-[10px] text-malou-color-chart-purple--accent">
                                {{ 'store_locator.edit_modal.ai_suggestion.retry' | translate }}
                            </span>
                        </div>
                    </ng-template>
                </app-button>
                <app-button
                    [theme]="'secondary--alt'"
                    [loading]="isLoadingOptimization()"
                    [disabled]="isDisabled() || isLoadingGeneration()"
                    (buttonClick)="optimizeStoreLocatorContent()">
                    <ng-template #textTemplate>
                        <div class="flex items-center">
                            <span class="text-[10px] text-malou-color-chart-purple--accent">
                                {{ 'store_locator.edit_modal.ai_suggestion.enhance' | translate }}
                            </span>
                        </div>
                    </ng-template></app-button
                >
            </div>
        }
    </div>
</div>
