<div
    class="relative flex h-fit flex-col items-center justify-center"
    [ngClass]="{
        'cursor-not-allowed': shouldShowCursorNotAllowed(),
        'cursor-pointer opacity-60 hover:opacity-100': !isBlockSelected(),
    }"
    (click)="handleShowFormBlock(StoreLocatorPageBlockType.DESCRIPTION)">
    @if (item()) {
        <div
            class="w-full"
            [ngStyle]="
                isEven()
                    ? stylesMap()[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_EVEN]
                    : stylesMap()[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_UNEVEN]
            ">
            <div class="flex w-full justify-center">
                <div class="relative h-auto w-1/2 overflow-hidden" [class.order-last]="isEven()">
                    <img
                        class="absolute h-full w-full object-cover object-center"
                        [alt]="item()?.imageDescription"
                        [defaultImage]="ImageAssets.DEFAULT_POST | imagePathResolver"
                        [lazyLoad]="item()?.imageUrl ?? ''" />
                </div>
                <div class="box-border w-1/2 break-words px-8 py-20">
                    <h2
                        class="mb-6 w-full text-4xl font-extrabold uppercase"
                        [ngStyle]="
                            isEven()
                                ? stylesMap()[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_EVEN]
                                : stylesMap()[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_TITLE_UNEVEN]
                        ">
                        {{ item()?.title }}
                    </h2>

                    <div class="flex flex-col gap-4">
                        @for (descriptionBlock of item()?.blocks; track $index) {
                            <h3 class="text-lg" [ngStyle]="stylesMap()[StoreLocatorRestaurantPageElementIds.DESCRIPTIONS_BLOCK_SUBTITLE]">
                                {{ descriptionBlock.title }}
                            </h3>
                            <p class="text-sm">{{ descriptionBlock.text }}</p>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
    <div
        class="absolute left-0 top-0 h-full w-full bg-transparent hover:border-4 hover:border-malou-color-primary"
        [class.border-malou-color-primary]="isBlockSelected()"
        [class.border-4]="isBlockSelected()"></div>
</div>
