@if (languages().length > 1) {
    <div class="language-selector h-12.5 w-20">
        <app-select [values]="languages()" [formControl]="control()" [selectedValues]="[currentSelectedLanguage()]">
            <ng-template let-value="value" #simpleSelectedValueTemplate>
                <div class="flex items-center justify-center">
                    <img class="h-6 w-7" [src]="value | flagPathResolver" [alt]="value" />
                </div>
            </ng-template>
            <ng-template let-value="value" let-isValueSelected="isValueSelected" #optionTemplate>
                <div class="flex items-center justify-center">
                    <img class="h-6 w-7" [src]="value | flagPathResolver" [alt]="value" />
                </div>
            </ng-template>
        </app-select>
    </div>
} @else {
    <div class="flex h-12.5 w-12.5 items-center justify-center rounded-[5px] border border-malou-color-background-dark bg-white p-2">
        <img class="h-6 w-6" [src]="currentSelectedLanguage() | flagPathResolver" [alt]="currentSelectedLanguage()" />
    </div>
}
