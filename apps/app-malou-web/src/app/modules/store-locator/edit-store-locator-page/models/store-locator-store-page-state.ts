import { signal, WritableSignal } from '@angular/core';

import { GetStoreLocatorDraftStoreDto, StoreLocatorStorePageUpdatesDto } from '@malou-io/package-dto';
import { StoreLocatorLanguage } from '@malou-io/package-utils';

import { EditStorePageCallToActionUrlOption } from ':modules/store-locator/edit-store-locator-page/blocks/shared/call-to-action/call-to-action.interface';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { StoreLocatorBlockTypeUpdateMap } from ':modules/store-locator/edit-store-locator-page/models/models.interface';

const INITIAL_BLOCK_DATA = {
    isModified: false,
    data: undefined,
};

interface StoreLocatorBlockTypeMap {
    [StoreLocatorPageBlockType.INFORMATION]: GetStoreLocatorDraftStoreDto['informationBlock'];
    [StoreLocatorPageBlockType.GALLERY]: GetStoreLocatorDraftStoreDto['galleryBlock'];
    [StoreLocatorPageBlockType.REVIEWS]: GetStoreLocatorDraftStoreDto['reviewsBlock'];
    [StoreLocatorPageBlockType.SOCIAL_NETWORKS]: GetStoreLocatorDraftStoreDto['socialNetworksBlock'];
    [StoreLocatorPageBlockType.CALL_TO_ACTION]: GetStoreLocatorDraftStoreDto['callToActionsBlock'];
    [StoreLocatorPageBlockType.DESCRIPTION]: GetStoreLocatorDraftStoreDto['descriptionsBlock'];
    [StoreLocatorPageBlockType.FAQ]: GetStoreLocatorDraftStoreDto['faqBlock'];
    [StoreLocatorPageBlockType.FOOTER]: null;
}

export class StoreLocatorStorePageState {
    id: string;
    lang: StoreLocatorLanguage;
    informationBlock: GetStoreLocatorDraftStoreDto['informationBlock'];
    galleryBlock: GetStoreLocatorDraftStoreDto['galleryBlock'];
    reviewsBlock: GetStoreLocatorDraftStoreDto['reviewsBlock'];
    socialNetworksBlock: GetStoreLocatorDraftStoreDto['socialNetworksBlock'];
    callToActionsBlock: GetStoreLocatorDraftStoreDto['callToActionsBlock'];
    descriptionsBlock: GetStoreLocatorDraftStoreDto['descriptionsBlock'];
    faqBlock: GetStoreLocatorDraftStoreDto['faqBlock'];

    suggestions: GetStoreLocatorDraftStoreDto['suggestionsForEdit'];

    readonly isDirty: WritableSignal<boolean> = signal(false);

    readonly updatedInformationBlock: WritableSignal<StoreLocatorBlockTypeUpdateMap[StoreLocatorPageBlockType.INFORMATION]> =
        signal(INITIAL_BLOCK_DATA);
    readonly updatedGalleryBlock: WritableSignal<StoreLocatorBlockTypeUpdateMap[StoreLocatorPageBlockType.GALLERY]> =
        signal(INITIAL_BLOCK_DATA);
    readonly updatedReviewsBlock: WritableSignal<StoreLocatorBlockTypeUpdateMap[StoreLocatorPageBlockType.REVIEWS]> =
        signal(INITIAL_BLOCK_DATA);
    readonly updatedSocialNetworksBlock: WritableSignal<StoreLocatorBlockTypeUpdateMap[StoreLocatorPageBlockType.SOCIAL_NETWORKS]> =
        signal(INITIAL_BLOCK_DATA);
    readonly updatedCallToActionsBlock: WritableSignal<StoreLocatorBlockTypeUpdateMap[StoreLocatorPageBlockType.CALL_TO_ACTION]> =
        signal(INITIAL_BLOCK_DATA);
    readonly updatedDescriptionsBlock: WritableSignal<StoreLocatorBlockTypeUpdateMap[StoreLocatorPageBlockType.DESCRIPTION]> =
        signal(INITIAL_BLOCK_DATA);
    readonly updatedFaqBlock: WritableSignal<StoreLocatorBlockTypeUpdateMap[StoreLocatorPageBlockType.FAQ]> = signal(INITIAL_BLOCK_DATA);

    readonly blockStaticDataMap: Record<StoreLocatorPageBlockType, () => StoreLocatorBlockTypeMap[StoreLocatorPageBlockType]> = {
        [StoreLocatorPageBlockType.INFORMATION]: () => this.informationBlock,
        [StoreLocatorPageBlockType.GALLERY]: () => this.galleryBlock,
        [StoreLocatorPageBlockType.REVIEWS]: () => this.reviewsBlock,
        [StoreLocatorPageBlockType.SOCIAL_NETWORKS]: () => this.socialNetworksBlock,
        [StoreLocatorPageBlockType.CALL_TO_ACTION]: () => this.callToActionsBlock,
        [StoreLocatorPageBlockType.DESCRIPTION]: () => this.descriptionsBlock,
        [StoreLocatorPageBlockType.FAQ]: () => this.faqBlock,
        [StoreLocatorPageBlockType.FOOTER]: () => null,
    };

    readonly blockSignalMap: Partial<Record<StoreLocatorPageBlockType, WritableSignal<any>>> = {
        [StoreLocatorPageBlockType.INFORMATION]: this.updatedInformationBlock,
        [StoreLocatorPageBlockType.GALLERY]: this.updatedGalleryBlock,
        [StoreLocatorPageBlockType.REVIEWS]: this.updatedReviewsBlock,
        [StoreLocatorPageBlockType.SOCIAL_NETWORKS]: this.updatedSocialNetworksBlock,
        [StoreLocatorPageBlockType.CALL_TO_ACTION]: this.updatedCallToActionsBlock,
        [StoreLocatorPageBlockType.DESCRIPTION]: this.updatedDescriptionsBlock,
        [StoreLocatorPageBlockType.FAQ]: this.updatedFaqBlock,
    };

    constructor(init: GetStoreLocatorDraftStoreDto) {
        this.id = init.id;
        this.lang = init.lang;
        this.informationBlock = init.informationBlock;
        this.galleryBlock = init.galleryBlock;
        this.reviewsBlock = init.reviewsBlock;
        this.callToActionsBlock = init.callToActionsBlock;
        this.socialNetworksBlock = init.socialNetworksBlock;
        this.descriptionsBlock = init.descriptionsBlock;
        this.suggestions = init.suggestionsForEdit;
        this.faqBlock = init.faqBlock;

        this.initBlockUpdateState(StoreLocatorPageBlockType.INFORMATION);
    }

    getLanguage(): StoreLocatorLanguage {
        if (this.lang === StoreLocatorLanguage.UNDETERMINED) {
            return StoreLocatorLanguage.FR;
        }
        return this.lang;
    }

    getBlockUpdatedData$<T extends StoreLocatorPageBlockType>(blockType: T): WritableSignal<StoreLocatorBlockTypeUpdateMap[T]> | undefined {
        const blockSignal = this.blockSignalMap[blockType] as WritableSignal<StoreLocatorBlockTypeUpdateMap[T]> | undefined;
        if (!blockSignal) {
            console.warn(`Block type ${blockType} is not supported for dynamic data retrieval.`);
        }
        return blockSignal;
    }

    getBlockStaticData<T extends StoreLocatorPageBlockType>(blockType: T): StoreLocatorBlockTypeMap[T] | null {
        if (this.blockStaticDataMap[blockType]) {
            return this.blockStaticDataMap[blockType]() as StoreLocatorBlockTypeMap[T];
        }
        console.warn(`Block type ${blockType} is not supported for static data retrieval.`);
        return null;
    }

    updateBlock<T extends StoreLocatorPageBlockType>({
        blockType,
        blockData,
    }: {
        blockType: T;
        blockData: StoreLocatorBlockTypeUpdateMap[T]['data'];
    }): void {
        const blockSignal = this.getBlockUpdatedData$(blockType);
        if (blockSignal) {
            blockSignal.set({
                isModified: true,
                data: blockData,
            } as StoreLocatorBlockTypeUpdateMap[T]);
            if (!this.isDirty()) {
                this.isDirty.set(true);
            }
        } else {
            console.error(`Block type ${blockType} is not supported for update.`);
        }
    }

    getCallToActionsSuggestions(): EditStorePageCallToActionUrlOption[] {
        if (!this.suggestions || !this.suggestions.callToActions) {
            return [];
        }
        return Object.entries(this.suggestions.callToActions).map(([text, url]) => ({
            value: url,
            key: text,
        }));
    }

    initBlockUpdateState(blockType: StoreLocatorPageBlockType): void {
        switch (blockType) {
            case StoreLocatorPageBlockType.INFORMATION:
                if (this.updatedInformationBlock()?.data) {
                    return;
                }
                this.updatedInformationBlock.set({
                    isModified: false,
                    data: {
                        image: {
                            url: this.informationBlock?.imageUrl ?? '',
                            description: this.informationBlock?.imageDescription ?? '',
                        },
                        title: this.informationBlock?.restaurantName ?? '',
                        ctas: this.informationBlock?.ctas ?? undefined,
                    },
                });
                break;
            case StoreLocatorPageBlockType.GALLERY:
                if (this.updatedGalleryBlock()?.data) {
                    return;
                }
                this.updatedGalleryBlock.set({
                    isModified: false,
                    data: this.galleryBlock ?? undefined,
                });
                break;
            case StoreLocatorPageBlockType.REVIEWS:
                if (this.updatedReviewsBlock()?.data) {
                    return;
                }
                this.updatedReviewsBlock.set({
                    isModified: false,
                    data: {
                        title: this.reviewsBlock?.title ?? '',
                        cta: this.reviewsBlock?.cta ?? undefined,
                    },
                });
                break;
            case StoreLocatorPageBlockType.SOCIAL_NETWORKS:
                if (this.updatedSocialNetworksBlock()?.data) {
                    return;
                }
                this.updatedSocialNetworksBlock.set({
                    isModified: false,
                    data: {
                        title: this.socialNetworksBlock?.title ?? '',
                    },
                });
                break;
            case StoreLocatorPageBlockType.CALL_TO_ACTION:
                if (this.updatedCallToActionsBlock()?.data) {
                    return;
                }
                this.updatedCallToActionsBlock.set({
                    isModified: false,
                    data: {
                        title: this.callToActionsBlock?.title ?? '',
                        links: this.callToActionsBlock?.links ?? [],
                    },
                });
                break;
            case StoreLocatorPageBlockType.DESCRIPTION:
                if (this.updatedDescriptionsBlock()?.data) {
                    return;
                }
                this.updatedDescriptionsBlock.set({
                    isModified: false,
                    data: {
                        items:
                            this.descriptionsBlock?.items?.map((item) => ({
                                title: item.title,
                                image: {
                                    url: item.imageUrl,
                                    description: item.imageDescription,
                                },
                                blocks: item.blocks.map((block) => ({
                                    title: block.title,
                                    text: block.text,
                                })),
                            })) ?? [],
                    },
                });
                break;
            case StoreLocatorPageBlockType.FAQ:
                if (this.updatedFaqBlock()?.data) {
                    return;
                }
                this.updatedFaqBlock.set({
                    isModified: false,
                    data: {
                        items:
                            this.faqBlock?.items?.map((item) => ({
                                question: item.question,
                                answer: item.answer,
                            })) ?? [],
                    },
                });
                break;
            default:
                console.warn(`Block type ${blockType} is not supported for initialization.`);
                break;
        }
    }

    toDto(options: { shouldIgnoreCheck: boolean } = { shouldIgnoreCheck: false }): StoreLocatorStorePageUpdatesDto | null {
        const updatedInformationBlock = this.updatedInformationBlock();
        const updatedGalleryBlock = this.updatedGalleryBlock();
        const updatedReviewsBlock = this.updatedReviewsBlock();
        const updatedCallToActionsBlock = this.updatedCallToActionsBlock();
        const updatedSocialNetworksBlock = this.updatedSocialNetworksBlock();
        const updatedDescriptionsBlock = this.updatedDescriptionsBlock();
        const updatedFaqBlock = this.updatedFaqBlock();

        if (!options.shouldIgnoreCheck && !this.isDirty()) {
            return null;
        }

        const dataToUpdate: StoreLocatorStorePageUpdatesDto = {
            restaurantId: this.id,
            lang: this.lang,
        };
        if (updatedInformationBlock.isModified) {
            dataToUpdate.information = updatedInformationBlock.data;
        }
        if (updatedGalleryBlock.isModified) {
            dataToUpdate.gallery = updatedGalleryBlock.data;
        }
        if (updatedReviewsBlock.isModified) {
            dataToUpdate.reviews = updatedReviewsBlock.data;
        }
        if (updatedCallToActionsBlock.isModified) {
            dataToUpdate.callToActions = updatedCallToActionsBlock.data;
        }
        if (updatedSocialNetworksBlock.isModified) {
            dataToUpdate.socialNetworks = updatedSocialNetworksBlock.data;
        }
        if (updatedDescriptionsBlock.isModified) {
            dataToUpdate.descriptions = updatedDescriptionsBlock.data;
        }
        if (updatedFaqBlock.isModified) {
            dataToUpdate.faq = updatedFaqBlock.data;
        }

        return dataToUpdate;
    }
}
