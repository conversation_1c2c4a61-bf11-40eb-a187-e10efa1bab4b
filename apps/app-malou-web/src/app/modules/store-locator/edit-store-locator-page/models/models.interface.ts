import { StoreLocatorStorePageUpdatesDto } from '@malou-io/package-dto';

import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';

interface StoreLocatorBlockDataUpdateWithStatus<T> {
    isModified: boolean;
    data: T | undefined;
}

export interface StoreLocatorBlockTypeUpdateMap {
    [StoreLocatorPageBlockType.INFORMATION]: NonNullable<
        StoreLocatorBlockDataUpdateWithStatus<StoreLocatorStorePageUpdatesDto['information']>
    >;
    [StoreLocatorPageBlockType.GALLERY]: NonNullable<StoreLocatorBlockDataUpdateWithStatus<StoreLocatorStorePageUpdatesDto['gallery']>>;
    [StoreLocatorPageBlockType.REVIEWS]: NonNullable<StoreLocatorBlockDataUpdateWithStatus<StoreLocatorStorePageUpdatesDto['reviews']>>;
    [StoreLocatorPageBlockType.CALL_TO_ACTION]: NonNullable<
        StoreLocatorBlockDataUpdateWithStatus<StoreLocatorStorePageUpdatesDto['callToActions']>
    >;
    [StoreLocatorPageBlockType.SOCIAL_NETWORKS]: NonNullable<
        StoreLocatorBlockDataUpdateWithStatus<StoreLocatorStorePageUpdatesDto['socialNetworks']>
    >;
    [StoreLocatorPageBlockType.DESCRIPTION]: NonNullable<
        StoreLocatorBlockDataUpdateWithStatus<StoreLocatorStorePageUpdatesDto['descriptions']>
    >;
    [StoreLocatorPageBlockType.FAQ]: NonNullable<StoreLocatorBlockDataUpdateWithStatus<StoreLocatorStorePageUpdatesDto['faq']>>;
    [StoreLocatorPageBlockType.FOOTER]: NonNullable<StoreLocatorBlockDataUpdateWithStatus<null>>;
}
