<div class="align-center flex h-full w-full flex-col gap-4 overflow-y-auto px-8.5 py-4 pb-8">
    <app-select-organization></app-select-organization>

    @if (isLoading()) {
        <ng-container [ngTemplateOutlet]="loadingTemplate"></ng-container>
    } @else if (storeLocatorOrganizationConfiguration()) {
        <ng-container [ngTemplateOutlet]="mainPageTemplate"></ng-container>
    } @else {
        <app-marketing-page></app-marketing-page>
    }
</div>

<ng-template #mainPageTemplate>
    @if (shouldShowPageLoader()) {
        <ng-container [ngTemplateOutlet]="generationLoaderPageTemplate"> </ng-container>
    } @else {
        <ng-container [ngTemplateOutlet]="storeLocatorTemplate"> </ng-container>
    }
</ng-template>

<ng-template #storeLocatorTemplate>
    @let _storeLocatorOrganizationConfiguration = storeLocatorOrganizationConfiguration();
    @if (_storeLocatorOrganizationConfiguration) {
        @if (storeLocatorOrganizationPagesState().hasAtLeastOnePageGenerated) {
            <app-store-locator-global-config
                [storeLocatorOrganizationConfiguration]="_storeLocatorOrganizationConfiguration"
                [organizationRestaurantKeywords]="organizationRestaurantKeywords()"
                [storeLocatorOrganizationRestaurants]="storeLocatorOrganizationRestaurants()"
                [storeLocatorOrganizationPagesState]="storeLocatorOrganizationPagesState()">
            </app-store-locator-global-config>
        } @else {
            <app-organization-configuration
                [storeLocatorOrganizationConfiguration]="_storeLocatorOrganizationConfiguration"
                [organizationRestaurantKeywords]="organizationRestaurantKeywords()"
                [storeLocatorOrganizationRestaurants]="storeLocatorOrganizationRestaurants()"
                (startPagesGeneration)="startPagesGeneration()">
            </app-organization-configuration>
        }
    }
</ng-template>

<ng-template #loadingTemplate>
    <div class="flex h-screen w-full items-center justify-center">
        <mat-spinner diameter="50"></mat-spinner>
    </div>
</ng-template>

<ng-template #generationLoaderPageTemplate>
    <app-loader-page
        class="h-full w-full"
        [generationStartDate]="organizationJobState()?.jobStartDate!"
        [generationEstimatedTime]="organizationJobState()?.jobEstimatedTime!"
        [illustration]="Illustration.Google"
        [title]="jobLoaderText().title"
        [footerText]="jobLoaderText().footerText">
    </app-loader-page>
</ng-template>
