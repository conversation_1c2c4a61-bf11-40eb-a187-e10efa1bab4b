import { StoreLocatorLanguage } from '@malou-io/package-utils';

import { StoreLocatorPageTranslation } from ':modules/store-locator/translation/store-locator.translation.interface';

const StoreLocatorTranslationFr: StoreLocatorPageTranslation = {
    informationBlock: {
        open_now: 'Ouvert maintenant',
        open_soon: 'Ouvre bientôt',
        closed: 'Fermé',
        opening_soon: 'Ouverture imminente',
        days: {
            friday: 'Vendredi',
            monday: 'Lundi',
            saturday: 'Samedi',
            sunday: 'Dimanche',
            thursday: 'Je<PERSON>',
            tuesday: 'Mardi',
            wednesday: 'Mercredi',
        },
    },
    socialNetworksBlock: {
        followers: 'abonnés',
        publications: 'publications',
        // eslint-disable-next-line @typescript-eslint/quotes
        likes: "j'aime",
    },
    footer: {
        powered_by: 'Propulsé avec amour par',
    },
};

const StoreLocatorTranslationEn: StoreLocatorPageTranslation = {
    informationBlock: {
        open_now: 'Open now',
        open_soon: 'Open soon',
        closed: 'Closed',
        opening_soon: 'Opening soon',
        days: {
            friday: 'Friday',
            monday: 'Monday',
            saturday: 'Saturday',
            sunday: 'Sunday',
            thursday: 'Thursday',
            tuesday: 'Tuesday',
            wednesday: 'Wednesday',
        },
    },
    socialNetworksBlock: {
        followers: 'followers',
        publications: 'publications',
        likes: 'likes',
    },
    footer: {
        powered_by: 'Powered by',
    },
};

export function getStoreLocatorPageTranslation(storeLocatorLanguage: StoreLocatorLanguage): StoreLocatorPageTranslation {
    return {
        [StoreLocatorLanguage.FR]: StoreLocatorTranslationFr,
        [StoreLocatorLanguage.EN]: StoreLocatorTranslationEn,
        [StoreLocatorLanguage.UNDETERMINED]: StoreLocatorTranslationFr,
    }[storeLocatorLanguage];
}
