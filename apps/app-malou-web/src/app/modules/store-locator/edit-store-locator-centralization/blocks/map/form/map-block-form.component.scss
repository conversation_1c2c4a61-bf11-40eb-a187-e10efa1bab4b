::ng-deep #textInputTitle {
    font-size: 14px;
    font-weight: 700;
    color: theme('colors.malou-color-text-1') !important;
}

::ng-deep .expansion-header {
    .mat-expansion-panel-header {
        background: theme('colors.malou-color-background-light');
    }

    .mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled='true']):hover {
        background: theme('colors.malou-color-background-light');
    }
}

::ng-deep .mat-expansion-panel-header.mat-expanded:focus,
.mat-expansion-panel-header.mat-expanded:hover {
    background: theme('colors.malou-color-background-light') !important;
}

::ng-deep .mat-expansion-panel {
    .mat-expansion-indicator {
        margin: 0 !important;

        &::after {
            margin: 0 !important;
        }
    }
}

::ng-deep .mat-mdc-tab-body-wrapper {
    height: 100%;
}
