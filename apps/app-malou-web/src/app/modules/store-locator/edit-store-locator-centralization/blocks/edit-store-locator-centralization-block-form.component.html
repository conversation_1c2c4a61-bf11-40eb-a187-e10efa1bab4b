<div class="flex h-full flex-col gap-y-4" [ngClass]="{ 'pointer-events-none opacity-50': shouldDisableModal() }">
    <div class="flex h-full flex-col">
        <mat-tab-group
            class="h-full"
            animationDuration="200"
            mat-align-tabs="start"
            [disableRipple]="true"
            [selectedIndex]="selectedTabIndex()"
            (selectedIndexChange)="handleTabChange($event)">
            <mat-tab label="{{ 'store_locator.edit_modal.tabs.content' | translate }}">
                <div class="flex h-full flex-col justify-between py-2">
                    <div class="mb-5 mt-5 flex flex-col gap-5">
                        @if (contentFormTemplate()) {
                            <ng-container [ngTemplateOutlet]="contentFormTemplate()!"></ng-container>
                        }
                    </div>
                </div>
            </mat-tab>
            <mat-tab label="{{ 'store_locator.edit_modal.tabs.style' | translate }}">
                <div class="mt-5 flex flex-col gap-5 px-5">
                    @if (styleFormTemplate()) {
                        <ng-container [ngTemplateOutlet]="styleFormTemplate()!"></ng-container>
                    }
                </div>
            </mat-tab>
        </mat-tab-group>
    </div>
</div>
