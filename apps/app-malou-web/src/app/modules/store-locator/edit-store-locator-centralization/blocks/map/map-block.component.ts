import { Ng<PERSON>ty<PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, signal, WritableSignal } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { StoreLocatorCentralizationBlockComponent } from ':modules/store-locator/edit-store-locator-centralization/blocks/edit-store-locator-centralization-block.component';
import { StoreLocatorCentralizationBlockType } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.interface';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-store-locator-edit-centralization-page-map-block',
    templateUrl: './map-block.component.html',
    styleUrls: ['./map-block.component.scss'],
    imports: [NgStyle, LazyLoadImageModule, MatIconModule, NgTemplateOutlet, ImagePathResolverPipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorCentralizationPageMapBlockComponent extends StoreLocatorCentralizationBlockComponent {
    readonly mapBlockUpdatedData = computed(
        () => this.storeCentralizationPageState()?.getBlockUpdatedData$(StoreLocatorCentralizationBlockType.MAP)?.()?.data
    );

    readonly defaultDistance = signal('18.8 km');

    readonly selectedStore: WritableSignal<string | null> = signal(this.storeCentralizationPageState()?.getStores()[0]?.id ?? null);

    readonly activePin = computed(() => ({
        src: this.mapBlockUpdatedData()?.pins.activePin.url,
        alt: 'active-pin',
    }));

    readonly inactivePin = computed(() => ({
        src: this.mapBlockUpdatedData()?.pins.inactivePin?.url ?? this.activePin().src,
        alt: 'inactive-pin',
    }));

    readonly noStoreImage = computed(() => ({
        src: this.mapBlockUpdatedData()?.popup?.noStoreImage.url ?? '',
        alt: 'no-store-image',
    }));

    readonly firstStore = computed(() => {
        const stores = this.storeCentralizationPageState()?.getStores();
        return stores && stores.length > 0 ? stores[0] : null;
    });

    readonly openingSoonStore = computed(() => {
        const stores = this.storeCentralizationPageState()?.getStores();
        return stores?.find((store) => store.isNotOpenedYet) ?? this.firstStore();
    });

    readonly stores = computed(() => this.storeCentralizationPageState()?.getStores());
}
