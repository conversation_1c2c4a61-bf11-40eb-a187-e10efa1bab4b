import { FormControl, FormGroup } from '@angular/forms';

export enum MapBlockControlType {
    PIN = 'title',
    RESTAURANT_INFORMATION = 'RESTAURANT_INFORMATION',
}

export interface MapBlockStyleData {
    search: {
        backgroundColor: string;
        radius: number;
        inputBorderColor: string;
        inputBackgroundColor: string;
        buttonBackgroundColor: string;
        buttonBorderColor: string;
        buttonIconColor: string;
    };
    item: {
        wrapperBackgroundColor: string;
        itemBackgroundColor: string;
        radius: number;
        textColor: string;
        iconColor: string;
        selectedBarColor: string;
        distanceButtonBackgroundColor: string;
        distanceButtonTextColor: string;
        nextOpeningBlockBackgroundColor: string;
        nextOpeningBlockTextColor: string;
    };
}
export interface MapBlockStyleForm {
    search: FormGroup<{
        backgroundColor: FormControl<string>;
        radius: FormControl<number>;
        inputBorderColor: FormControl<string>;
        inputBackgroundColor: FormControl<string>;
        buttonBackgroundColor: FormControl<string>;
        buttonBorderColor: FormControl<string>;
        buttonIconColor: FormControl<string>;
    }>;
    item: FormGroup<{
        wrapperBackgroundColor: FormControl<string>;
        itemBackgroundColor: FormControl<string>;
        radius: FormControl<number>;
        textColor: FormControl<string>;
        iconColor: FormControl<string>;
        selectedBarColor: FormControl<string>;
        distanceButtonBackgroundColor: FormControl<string>;
        distanceButtonTextColor: FormControl<string>;
        nextOpeningBlockBackgroundColor: FormControl<string>;
        nextOpeningBlockTextColor: FormControl<string>;
    }>;
}
