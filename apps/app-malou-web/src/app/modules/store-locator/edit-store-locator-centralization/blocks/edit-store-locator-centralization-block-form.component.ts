import { NgClass, NgTemplateOutlet } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    inject,
    input,
    signal,
    TemplateRef,
    WritableSignal,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';

import { MimeType, StoreLocatorCentralizationPageElementIds } from '@malou-io/package-utils';

import { EditStoreLocatorCentralizationContext } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.context';
import { EXTRA_COLORS } from ':modules/store-locator/shared/edit-store-locator/edit-store-locator.interface';
import { parseConfigurationStyleClassesToCssStyle } from ':modules/store-locator/shared/edit-store-locator/utils/edit-store-locator-page.utils';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

enum BlockFromTabs {
    CONTENT,
    STYLE,
}
@Component({
    selector: 'app-edit-store-locator-centralization-block-form-wrapper',
    templateUrl: './edit-store-locator-centralization-block-form.component.html',
    styleUrls: ['./edit-store-locator-centralization-block-form.component.scss'],
    imports: [MatButtonModule, MatTabsModule, TranslateModule, NgTemplateOutlet, MatIconModule, NgClass],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorCentralizationBlockFormComponent {
    readonly contentFormTemplate = input<TemplateRef<any>>();
    readonly styleFormTemplate = input<TemplateRef<any>>();

    readonly destroyRef = inject(DestroyRef);
    readonly editStoreLocatorCentralizationContext = inject(EditStoreLocatorCentralizationContext);

    readonly organizationStyleConfiguration = computed(() => this.editStoreLocatorCentralizationContext.organizationStyleConfiguration());
    readonly colorOptions = computed(() => [
        ...(this.editStoreLocatorCentralizationContext.organizationStyleConfiguration()?.colors.map((color) => color.value) ?? []),
        ...EXTRA_COLORS,
    ]);

    readonly storeCentralizationPageState = computed(() => this.editStoreLocatorCentralizationContext.storeCentralizationPageState());

    readonly selectedTabIndex: WritableSignal<number> = signal(BlockFromTabs.CONTENT);

    readonly SvgIcon = SvgIcon;
    readonly MimeType = MimeType;

    readonly isBlockInError = computed(() => this.editStoreLocatorCentralizationContext.isBlockInError().isError);
    readonly shouldDisableModal = computed(() => this.editStoreLocatorCentralizationContext.shouldDisableModal());

    handleTabChange(event: number): void {
        this.selectedTabIndex.set(event);
    }

    protected getStyleMap(elementIds: StoreLocatorCentralizationPageElementIds[]): Record<string, Record<string, string>> {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();
        if (!organizationStyleConfiguration) {
            return {};
        }

        const styleMap: Record<string, Record<string, string>> = {};

        elementIds.forEach((key) => {
            styleMap[key] = parseConfigurationStyleClassesToCssStyle(
                organizationStyleConfiguration.getMapPageElementStyle(key) as string[],
                {
                    colors: organizationStyleConfiguration.colors,
                    fonts: organizationStyleConfiguration.fonts,
                }
            );
        });

        return styleMap;
    }
}
