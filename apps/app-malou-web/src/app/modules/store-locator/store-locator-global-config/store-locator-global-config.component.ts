import { NgTemplateOutlet } from '@angular/common';
import { Component, DestroyRef, inject, input, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { uniq } from 'lodash';

import { HeapEventName, StoreLocatorLanguage } from '@malou-io/package-utils';

import { HeapService } from ':core/services/heap.service';
import { ToastService } from ':core/services/toast.service';
import { EditAiSettingsModalComponent } from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.component';
import { EditAiSettingsModalInputData } from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.interface';
import { EditLanguagesModalComponent } from ':modules/store-locator/edit-languages-modal/edit-languages-modal.component';
import { EditLanguagesModalInputData } from ':modules/store-locator/edit-languages-modal/edit-languages-modal.interface';
import { EditStoreLocatorCentralizationModalComponent } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.component';
import { EditStoreLocatorCentralizationContext } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.context';
import { EditStoreLocatorCentralizationModalInputData } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.interface';
import { EditStoreLocatorPageModalComponent } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.component';
import { EditStoreLocatorPageContext } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.context';
import { EditStoreLocatorPageModalInputData } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { StoreLocatorActivityTracker } from ':modules/store-locator/edit-store-locator-page/models/store-locator-activity-tracker';
import { StoreLocatorStorePageState } from ':modules/store-locator/edit-store-locator-page/models/store-locator-store-page-state';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/models/store-locator-organization-config';
import { StoreLocatorOrganizationKeyword } from ':modules/store-locator/models/store-locator-organization-keyword';
import { StoreLocatorOrganizationRestaurant } from ':modules/store-locator/models/store-locator-organization-restaurant';
import { StoreLocatorOrganizationPagesState } from ':modules/store-locator/models/store-locator.interface';
import { ArrayMap } from ':modules/store-locator/shared/edit-store-locator/utils/array-map';
import { StoreLocatorConfig } from ':modules/store-locator/store-locator-.interface';
import { StoreLocatorContext } from ':modules/store-locator/store-locator.context';
import { ButtonComponent } from ':shared/components/button/button.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { CustomDialogService, DialogScreenSize } from ':shared/services/custom-dialog.service';

@Component({
    selector: 'app-store-locator-global-config',
    imports: [MatButtonModule, TranslateModule, NgTemplateOutlet, MatIconModule, ButtonComponent],
    templateUrl: './store-locator-global-config.component.html',
    styleUrl: './store-locator-global-config.component.scss',
})
export class StoreLocatorGlobalConfigComponent {
    readonly storeLocatorOrganizationConfiguration = input.required<StoreLocatorOrganizationConfiguration>();
    readonly storeLocatorOrganizationPagesState = input.required<StoreLocatorOrganizationPagesState>();
    readonly storeLocatorOrganizationRestaurants = input.required<StoreLocatorOrganizationRestaurant[]>();
    readonly organizationRestaurantKeywords = input.required<StoreLocatorOrganizationKeyword[]>();

    private readonly _storeLocatorContext = inject(StoreLocatorContext);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _toastService = inject(ToastService);
    private readonly _translate = inject(TranslateService);
    private readonly _editStoreLocatorPageContext = inject(EditStoreLocatorPageContext);
    private readonly _editStoreLocatorCentralizationContext = inject(EditStoreLocatorCentralizationContext);
    private readonly _destroyRef = inject(DestroyRef);
    private readonly _heapService = inject(HeapService);

    readonly SvgIcon = SvgIcon;

    readonly isFetchingRestaurantPages: WritableSignal<boolean> = signal(false);
    readonly isFetchingCentralizationPages: WritableSignal<boolean> = signal(false);

    editStoreLocatorPage(): void {
        this.isFetchingRestaurantPages.set(true);
        this._editStoreLocatorPageContext
            .getRestaurantPages(this.storeLocatorOrganizationConfiguration().organizationId)
            .pipe(takeUntilDestroyed(this._destroyRef))
            .subscribe({
                next: (stores) => {
                    if (!stores || stores.length === 0) {
                        this._toastService.openErrorToast(this._translate.instant('store_locator.edit_modal.init.error_no_stores'));
                        this.isFetchingRestaurantPages.set(false);
                        return;
                    }

                    const primaryLanguage = this.storeLocatorOrganizationConfiguration().languages.primary;
                    const secondaryLanguages = this.storeLocatorOrganizationConfiguration().languages.secondary;
                    this._editStoreLocatorPageContext.selectedRestaurantPagesLanguage.set(primaryLanguage);
                    this._editStoreLocatorPageContext.restaurantPagesLanguages.set(
                        [primaryLanguage, ...secondaryLanguages].filter((lang) => lang !== StoreLocatorLanguage.UNDETERMINED)
                    );

                    const firstRestaurant = this.storeLocatorOrganizationRestaurants()[0];
                    this._editStoreLocatorPageContext.currentEditingRestaurant.set(
                        new StoreLocatorOrganizationRestaurant({
                            id: firstRestaurant.id,
                            name: firstRestaurant.getDisplayName(),
                            internalName: firstRestaurant.internalName,
                            address: firstRestaurant.address,
                            type: firstRestaurant.type,
                        })
                    );

                    this._editStoreLocatorPageContext.organizationData.set(this.storeLocatorOrganizationConfiguration().organization);

                    const restaurantIds = uniq(this.storeLocatorOrganizationRestaurants().map((restaurant) => restaurant.id));
                    const storesPagesMap = new ArrayMap<string, StoreLocatorStorePageState>(
                        restaurantIds.map((restaurantId) => [restaurantId, stores.filter((store) => store.id === restaurantId)])
                    );
                    this._editStoreLocatorPageContext.storesPagesStates.set(storesPagesMap);

                    const selectedRestaurantStorePage = storesPagesMap.get(firstRestaurant.id);
                    if (selectedRestaurantStorePage) {
                        this._editStoreLocatorPageContext.selectedRestaurantStorePageState.set(
                            storesPagesMap.findIn(firstRestaurant.id, (page) => page.lang === primaryLanguage)
                        );
                    }

                    const data: EditStoreLocatorPageModalInputData = {
                        organizationConfiguration: this.storeLocatorOrganizationConfiguration(),
                        organizationRestaurants: this.storeLocatorOrganizationRestaurants(),
                    };

                    this._editStoreLocatorPageContext.editStoreLocatorActivityTracker.set(
                        new StoreLocatorActivityTracker(this._heapService)
                    );
                    this._editStoreLocatorPageContext.editStoreLocatorActivityTracker()?.setRestaurantTrackingMap(firstRestaurant.id);

                    this.isFetchingRestaurantPages.set(false);

                    this._customDialogService
                        .open<EditStoreLocatorPageModalComponent, EditStoreLocatorPageModalInputData>(
                            EditStoreLocatorPageModalComponent,
                            {
                                width: '100%',
                                height: '100%',
                                panelClass: 'malou-dialog-panel--without-border-radius',
                                disableClose: true,
                                data,
                            },
                            { animateScreenSize: DialogScreenSize.ALL }
                        )
                        .afterClosed()
                        .subscribe();
                },
                error: (error) => {
                    console.error('Error init:', error);
                    this._toastService.openErrorToast(this._translate.instant('store_locator.edit_modal.init.error'));
                    this.isFetchingRestaurantPages.set(false);
                },
            });
    }

    editStoreLocatorCentralizationPage(): void {
        this.isFetchingCentralizationPages.set(true);
        this._editStoreLocatorCentralizationContext
            .getCentralizationPages(this.storeLocatorOrganizationConfiguration().organizationId)
            .pipe(takeUntilDestroyed(this._destroyRef))
            .subscribe({
                next: (pages) => {
                    if (!pages || pages.length === 0) {
                        this.isFetchingCentralizationPages.set(false);
                        return;
                    }

                    this._editStoreLocatorCentralizationContext.organizationData.set(
                        this.storeLocatorOrganizationConfiguration().organization
                    );

                    const data: EditStoreLocatorCentralizationModalInputData = {
                        organizationConfiguration: this.storeLocatorOrganizationConfiguration(),
                    };

                    this._editStoreLocatorCentralizationContext.storeCentralizationPageState.set(pages[0]);

                    this.isFetchingCentralizationPages.set(false);

                    this._customDialogService
                        .open<EditStoreLocatorCentralizationModalComponent, EditStoreLocatorCentralizationModalInputData>(
                            EditStoreLocatorCentralizationModalComponent,
                            {
                                width: '100%',
                                height: '100%',
                                panelClass: 'malou-dialog-panel--without-border-radius',
                                disableClose: true,
                                data,
                            },
                            { animateScreenSize: DialogScreenSize.ALL }
                        )
                        .afterClosed()
                        .subscribe();
                },
                error: (error) => {
                    console.error('Error init:', error);
                    this.isFetchingCentralizationPages.set(false);
                },
            });
    }

    openEditAiSettingsModal(): void {
        const data: EditAiSettingsModalInputData = {
            organizationId: this.storeLocatorOrganizationConfiguration().organizationId,
            aiSettings: this.storeLocatorOrganizationConfiguration().aiSettings,
            organizationRestaurants: this.storeLocatorOrganizationRestaurants(),
            organizationRestaurantKeywords: this.organizationRestaurantKeywords(),
        };
        this._heapService.track(HeapEventName.TRACKING_STORE_LOCATOR_OPEN_CONFIGURATION_MODAL, {
            element: StoreLocatorConfig.AI_ASSISTANT,
        });
        this._customDialogService
            .open<EditAiSettingsModalComponent, EditAiSettingsModalInputData>(EditAiSettingsModalComponent, {
                height: 'unset',
                maxHeight: '90vh',
                width: '90vw',
                maxWidth: '80vw',
                data,
            })
            .afterClosed()
            .subscribe({
                next: (result: StoreLocatorOrganizationConfiguration | undefined) => {
                    if (result) {
                        this._storeLocatorContext.updateStoreLocatorOrganizationConfiguration(result);
                        this._toastService.openSuccessToast(
                            this._translate.instant('store_locator.edit_ai_settings_modal.success_message')
                        );
                    }
                },
                error: (error) => {
                    console.error('Error opening EditAiSettingsModal:', error);
                    this._toastService.openErrorToast(this._translate.instant('common.unknown_error'));
                },
            });
    }

    openEditLanguagesModal(): void {
        this._heapService.track(HeapEventName.TRACKING_STORE_LOCATOR_OPEN_CONFIGURATION_MODAL, {
            element: StoreLocatorConfig.LANGUAGE,
        });
        this._customDialogService
            .open<EditLanguagesModalComponent, EditLanguagesModalInputData>(EditLanguagesModalComponent, {
                height: 'unset',
                maxHeight: '90vh',
                width: '90vw',
                maxWidth: '80vw',
                data: {
                    organizationId: this.storeLocatorOrganizationConfiguration().organizationId,
                    languages: this.storeLocatorOrganizationConfiguration().languages,
                },
            })
            .afterClosed()
            .subscribe({
                next: (result: StoreLocatorOrganizationConfiguration | undefined) => {
                    if (result) {
                        this._storeLocatorContext.updateStoreLocatorOrganizationConfiguration(result);
                        this._toastService.openSuccessToast(
                            this._translate.instant('store_locator.configuration.translation.languages_updated')
                        );
                    }
                },
                error: (error) => {
                    console.error('Error opening EditLanguagesModal:', error);
                    this._toastService.openErrorToast(this._translate.instant('common.unknown_error'));
                },
            });
    }
}
