import { NgClass, NgTemplateOutlet } from '@angular/common';
import {
    Component,
    computed,
    DestroyRef,
    effect,
    ElementRef,
    inject,
    OnDestroy,
    OnInit,
    output,
    signal,
    Signal,
    viewChild,
    WritableSignal,
} from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { InfiniteScrollDirective } from 'ngx-infinite-scroll';
import { catchError, combineLatest, debounceTime, filter, forkJoin, map, Observable, of, Subject, switchMap } from 'rxjs';

import { getPlatformKeysWithStories, isNotNil, PlatformKey, StoriesListFilter, TimeInMilliseconds } from '@malou-io/package-utils';

import { PlatformsService } from ':core/services/platforms.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ScreenSizeService } from ':core/services/screen-size.service';
import { LocalStorage } from ':core/storage/local-storage';
import {
    DisconnectedPlatformsModalComponent,
    DisconnectedPlatformsModalProps,
    DisconnectedPlatformsModalResult,
    DisconnectedPlatformsModalType,
} from ':modules/informations/disconnected-platforms-modal/disconnected-platforms-modal.component';
import { PlatformOption } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/previews.component';
import { NoStoryComponent } from ':modules/stories/v2/components/no-story/no-story.component';
import { StoriesListHeaderComponent } from ':modules/stories/v2/components/stories-list/stories-list-header/stories-list-header.component';
import { StoriesPreviewsHeaderComponent } from ':modules/stories/v2/components/stories-list/stories-previews-header/stories-previews-header.component';
import { StoryItemComponent } from ':modules/stories/v2/components/stories-list/story-item/story-item.component';
import { StoriesPreviewsComponent } from ':modules/stories/v2/components/stories-previews/stories-previews.component';
import { StoryItem } from ':modules/stories/v2/models/story-item';
import { StoriesContext } from ':modules/stories/v2/stories.context';
import { SkeletonComponent } from ':shared/components/skeleton/skeleton.component';
import { DuplicationDestination } from ':shared/enums/duplication-destination.enum';
import { LocalStorageKey } from ':shared/enums/local-storage-key';
import { Platform, Restaurant } from ':shared/models';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';
import { CustomDialogService } from ':shared/services/custom-dialog.service';
import { ScrollableContentService } from ':shared/services/scrollable-content.service';

interface PlatformsStore {
    platformsData: {
        [key: string]: Platform[];
    };
}

@Component({
    selector: 'app-stories-list',
    templateUrl: './stories-list.component.html',
    styleUrls: ['./stories-list.component.scss'],
    imports: [
        NgClass,
        NgTemplateOutlet,
        MatCheckboxModule,
        NoStoryComponent,
        SkeletonComponent,
        StoriesListHeaderComponent,
        StoriesPreviewsComponent,
        StoriesPreviewsHeaderComponent,
        StoryItemComponent,
        InfiniteScrollDirective,
        ApplyPurePipe,
    ],
})
export class StoriesListComponent implements OnInit, OnDestroy {
    readonly createStory = output<void>();
    readonly duplicateToOtherRestaurants = output<{
        postRefs: ({ id: string } | { bindingId: string })[];
    }>();
    readonly updateStory = output<{ storyId: string; shouldOpenFeedbacks?: boolean }>();

    private readonly _storiesContext = inject(StoriesContext);
    private readonly _platformsService = inject(PlatformsService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _scrollableContentService = inject(ScrollableContentService);
    private readonly _screenSizeService = inject(ScreenSizeService);
    private readonly _store = inject(Store);
    private readonly _destroyRef = inject(DestroyRef);
    private readonly _activatedRoute = inject(ActivatedRoute);
    private readonly _router = inject(Router);

    readonly previewPanel = viewChild<ElementRef<HTMLDivElement>>('previewPanel');

    readonly stories: Signal<StoryItem[]> = this._storiesContext.sortedStories;
    readonly isFetchingStories: Signal<boolean> = this._storiesContext.isFetchingStories;
    readonly isFetchingMoreStories: Signal<boolean> = this._storiesContext.isFetchingMoreStories;
    readonly selectedFilter: WritableSignal<StoriesListFilter> = this._storiesContext.selectedFilter;
    readonly selectedFilter$ = toObservable(this.selectedFilter);
    readonly fetchNextPage$ = this._storiesContext.fetchNextPage$;

    readonly isSelecting = this._storiesContext.isSelecting;
    readonly selectedStoriesCount = this._storiesContext.storySelection.getCountAsSignal();

    readonly highlightedStoryIds = this._storiesContext.highlightedStoryIds;

    private readonly _platformsStore$: Observable<PlatformsStore> = this._store.select((state) => state.platforms);
    private readonly _availablePlatforms$ = this._getAvailablePlatforms$();
    private readonly _availablePlatforms = toSignal(this._availablePlatforms$, { initialValue: [] });

    readonly isIgConnected = computed(() => this._availablePlatforms().some((p) => p === PlatformKey.INSTAGRAM));
    readonly isFbConnected = computed(() => this._availablePlatforms().some((p) => p === PlatformKey.FACEBOOK));

    readonly restaurantId = computed(() => this._storiesContext.restaurant().id);
    readonly restaurant$ = this._storiesContext.restaurant$;

    readonly FETCH_STORIES_LIMIT = 20;

    readonly restaurantHasNoStory = this._storiesContext.restaurantHasNoStory;

    readonly futureStories = computed(() =>
        this.stories()
            .filter((story) => story.isCurrentOrFutureStory())
            .sort((a, b) => (a.getPostDate() ?? new Date()).getTime() - (b.getPostDate() ?? new Date()).getTime())
    );

    readonly selectedPreviewPlatform = signal<PlatformOption | null>(null);
    readonly previewPlatformOptions = signal<PlatformOption[]>([]);
    private readonly _fetchProfilePictureUrls$ = new Subject<PlatformOption[]>();

    private readonly _resizeEvent = toSignal(this._screenSizeService.resize$);

    constructor() {
        effect(() => {
            this._resizeEvent();
            const previewPanel = this.previewPanel();
            const previewPanelWidth = previewPanel?.nativeElement.clientWidth ?? 0;
            const offsetForArrow = 30;
            this._scrollableContentService.moveArrow({ right: `${previewPanelWidth + offsetForArrow}px` });
        });
    }

    ngOnInit(): void {
        this._scrollableContentService.scrollToTop();

        this._activatedRoute.queryParams.pipe(filter((params) => !!params.postId)).subscribe((params) => {
            const shouldOpenFeedbacks = params.openFeedback === 'true';
            this.updateStory.emit({ storyId: params.postId, shouldOpenFeedbacks });
        });

        this.selectedFilter$.pipe(takeUntilDestroyed(this._destroyRef)).subscribe(() => {
            this._storiesContext.resetPagination();
        });

        this.restaurant$.pipe(takeUntilDestroyed(this._destroyRef)).subscribe(() => {
            this._storiesContext.init();
        });

        combineLatest([this._availablePlatforms$, this.restaurant$])
            .pipe(
                filter(([_platformKeys, restaurant]) => !!restaurant),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe(([platformKeys]) => {
                this._initPreviewPlatformOptions(platformKeys);
            });

        // debounceTime(0) is used to make sure the observable is triggered after the previous observables
        combineLatest([this.restaurant$, this.selectedFilter$, this.fetchNextPage$])
            .pipe(debounceTime(0), takeUntilDestroyed(this._destroyRef))
            .subscribe(([restaurant, selectedFilter]) => {
                this._storiesContext.fetchStories(selectedFilter, restaurant.id, this.FETCH_STORIES_LIMIT);
            });

        this._checkIfShouldOpenDisconnectedPlatformsModal();

        this._initFetchProfilePictureUrls();
    }

    ngOnDestroy(): void {
        this._scrollableContentService.moveArrowToDefault();
    }

    onScrollDown(): void {
        const hasNextPage = this._storiesContext.hasNextPage();
        if (hasNextPage) {
            this._storiesContext.goNextPage();
        }
    }

    onCreateStory(): void {
        this.createStory.emit();
    }

    onUpdateStory(data: { storyId: string; shouldOpenFeedbacks?: boolean }): void {
        const queryParams = { postId: data.storyId, ...(data.shouldOpenFeedbacks ? { openFeedback: data.shouldOpenFeedbacks } : {}) };
        this._router.navigate(['.'], {
            relativeTo: this._activatedRoute,
            queryParams,
        });
    }

    onDeleteStory(_event: any): void {
        // TODO stories-v2
    }

    onDuplicateStory(event: { postRef: { id: string }; destination: DuplicationDestination }): void {
        if (event.destination === DuplicationDestination.HERE) {
            const restaurantId = this._storiesContext.restaurant().id;
            this._storiesContext.duplicateStories({
                postRefs: [event.postRef],
                restaurantIds: [restaurantId],
            });
        } else {
            this.duplicateToOtherRestaurants.emit({
                postRefs: [event.postRef],
            });
        }
    }

    onStoryDateChange(_event: any): void {
        // TODO stories-v2
    }

    onSelect(post: StoryItem): void {
        this._storiesContext.storySelection.toggle(post);
        if (!this._storiesContext.storySelection.isSelected(post)) {
            this._storiesContext.allStoriesSelected.set(false);
        }
    }

    isSelected = (post: StoryItem): boolean => this._storiesContext.storySelection.isSelected(post);

    isHighlighted = (post: StoryItem, highlightedPostIds: string[]): boolean => highlightedPostIds.includes(post.id);

    private _getAvailablePlatforms$(): Observable<PlatformKey[]> {
        return combineLatest([this._platformsStore$, this._restaurantsService.restaurantSelected$]).pipe(
            filter(([platforms, restaurant]) => !!restaurant && !!platforms.platformsData[restaurant._id]),
            map(([platforms, restaurant]: [PlatformsStore, Restaurant]) => {
                const storiesPlatforms = getPlatformKeysWithStories();
                const connectedPlatforms = platforms.platformsData[restaurant._id].map((plat) => plat.key);
                const availablePlatforms = storiesPlatforms.filter((platform) => connectedPlatforms.includes(platform));
                return availablePlatforms;
            }),
            catchError((err) => {
                console.warn('err :>> ', err);
                return [];
            }),
            takeUntilDestroyed(this._destroyRef)
        );
    }

    private _checkIfShouldOpenDisconnectedPlatformsModal(): void {
        this._storiesContext.disconnectedPlatforms$
            .pipe(
                filter((platforms) => {
                    const restaurant = this._storiesContext.restaurant();
                    return platforms.length > 0 && !!restaurant && this._canOpenPopin(restaurant);
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe((platforms) => {
                this._showDisconnectedPlatformsPopin(platforms);
            });
    }

    private _canOpenPopin(restaurant: Restaurant): boolean {
        try {
            const disconnectedPlatformsPopinLastOpenedAt = this._getDisconnectedPlatformsPopinLastOpenedAtFromLocalStorage();
            const lastOpenedAt = disconnectedPlatformsPopinLastOpenedAt[restaurant._id];
            return !lastOpenedAt || new Date().getTime() - new Date(lastOpenedAt).getTime() > 1 * TimeInMilliseconds.DAY;
        } catch {
            return true;
        }
    }

    private _showDisconnectedPlatformsPopin(platforms: Platform[]): void {
        const restaurant = this._storiesContext.restaurant();
        if (!restaurant) {
            return;
        }

        this._customDialogService
            .open<DisconnectedPlatformsModalComponent, DisconnectedPlatformsModalProps, DisconnectedPlatformsModalResult>(
                DisconnectedPlatformsModalComponent,
                {
                    width: '600px',
                    height: 'auto',
                    data: {
                        platforms,
                        restaurantId: restaurant._id,
                        type: DisconnectedPlatformsModalType.SOCIAL,
                    },
                }
            )
            .afterClosed()
            .subscribe((res) => {
                if (!res?.ignoreUpdateLocalStorage) {
                    this._updateDisconnectedPlatformsPopinLastOpenedAtInLocalStorage(restaurant);
                }
            });
    }

    private _getDisconnectedPlatformsPopinLastOpenedAtFromLocalStorage(): Record<string, string> {
        const disconnectedPlatformsPopinLastOpenedAtString =
            LocalStorage.getItem(LocalStorageKey.DISCONNECTED_SOCIAL_PLATFORMS_POPIN_LAST_OPENED_AT) ?? '{}';
        return JSON.parse(disconnectedPlatformsPopinLastOpenedAtString);
    }

    private _updateDisconnectedPlatformsPopinLastOpenedAtInLocalStorage(restaurant: Restaurant): void {
        const now = new Date();
        try {
            const disconnectedPlatformsPopinLastOpenedAt = this._getDisconnectedPlatformsPopinLastOpenedAtFromLocalStorage();
            disconnectedPlatformsPopinLastOpenedAt[restaurant._id] = now.toISOString();
            LocalStorage.setItem(
                LocalStorageKey.DISCONNECTED_SOCIAL_PLATFORMS_POPIN_LAST_OPENED_AT,
                JSON.stringify(disconnectedPlatformsPopinLastOpenedAt)
            );
        } catch (error) {
            console.error('Error updating disconnected platforms popin last opened at in local storage', error);
        }
    }

    private _initPreviewPlatformOptions(platformKeys: PlatformKey[]): void {
        this.previewPlatformOptions.set([]);

        forkJoin(platformKeys.map((platformKey) => this._platformsService.getPlatformSocialLink(this.restaurantId(), platformKey)))
            .pipe(takeUntilDestroyed(this._destroyRef))
            .subscribe((responses) => {
                const platformOptions = responses
                    .filter((response) => isNotNil(response.data))
                    .map((response) => this._mapPlatformToPlatformOption(response.data));
                this.previewPlatformOptions.set(platformOptions);
                this._fetchProfilePictureUrls$.next(platformOptions); // Always fetch profile pictures to have the latest

                const selectedPreviewPlatform = this.selectedPreviewPlatform();
                const foundSelectedPreviewPlatform = platformOptions.find(
                    (platformOption) => platformOption.platformKey === selectedPreviewPlatform?.platformKey
                );
                if (!foundSelectedPreviewPlatform) {
                    this.selectedPreviewPlatform.set(platformOptions[0] ?? null);
                }
            });
    }

    private _mapPlatformToPlatformOption(platform: {
        _id: string;
        key: PlatformKey;
        socialLink?: string;
        socialId: string;
        name: string;
        profilePictureUrl?: string;
    }): PlatformOption {
        let username = '';

        switch (platform.key) {
            case PlatformKey.INSTAGRAM:
                const matchedUsername = platform.socialLink?.match(/^https:\/\/www.instagram.com\/(.*)/)?.[1];
                username = matchedUsername ?? '';
                break;
            case PlatformKey.FACEBOOK:
            case PlatformKey.TIKTOK:
                if (platform.name) {
                    username = platform.name;
                }
        }
        return {
            platformKey: platform.key,
            username,
            profilePictureUrl: platform.profilePictureUrl,
        };
    }

    private _initFetchProfilePictureUrls(): void {
        this._fetchProfilePictureUrls$
            .pipe(
                switchMap((platformOptions) =>
                    forkJoin(
                        platformOptions.map((platformOption) =>
                            this._platformsService
                                .getProfilePictureUrl(this._storiesContext.restaurant().id, platformOption.platformKey)
                                .pipe(
                                    map((result) => ({ data: { ...result.data, platformKey: platformOption.platformKey } })),
                                    catchError((error) => {
                                        console.error('Error fetching profile picture URL', {
                                            platformKey: platformOption.platformKey,
                                            error,
                                        });
                                        return of({ data: { profilePictureUrl: undefined, platformKey: platformOption.platformKey } });
                                    })
                                )
                        )
                    )
                ),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe((responses) => {
                this.previewPlatformOptions.update((prev) => {
                    const responseMap = new Map(responses.map((response) => [response.data.platformKey, response.data.profilePictureUrl]));

                    return prev.map((option) => ({
                        ...option,
                        profilePictureUrl: responseMap.get(option.platformKey) ?? option.profilePictureUrl,
                    }));
                });
            });
    }
}
