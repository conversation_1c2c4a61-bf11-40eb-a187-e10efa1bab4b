@let userCanManagePost = CaslAction.MANAGE | caslAble: CaslSubject.SOCIAL_POST;

<app-posts-list-header
    [(selectedFilter)]="selectedFilter"
    [errorFilter]="StoriesListFilter.ERROR"
    [allFilter]="StoriesListFilter.ALL"
    [filterOptionsAndCount]="filterOptionsAndCount()"
    [atLeastOnePostInList]="atLeastOnePostInList()"
    [isSelecting]="isSelecting()"
    [duplicateTooltip]="duplicateStoriesTooltip()"
    [deleteTooltip]="deleteStoriesTooltip()"
    [allPostsSelected]="allPostsSelected()"
    [userCanManagePost]="userCanManagePost"
    [deleteButtonId]="'tracking_stories_init_delete_bulk_v2'"
    [filterOptionsIdFn]="filterOptionsIdFn"
    [filterOptionDisplayFn]="filterOptionDisplayFn"
    (deleteSelection)="onDeleteSelection()"
    (setIsSelecting)="setIsSelecting($event)"
    (toggleSelectAll)="toggleSelectAll($event)">
    <div createButton>
        <app-button
            [text]="'stories.create_story' | translate"
            [size]="'large'"
            [isSquareButton]="true"
            (buttonClick)="onCreateStory()"></app-button>
    </div>

    <div duplicateActions>
        <button id="tracking_stories_duplicate_here_bulk_v2" mat-menu-item (click)="onDuplicateSelection(DuplicationDestination.HERE)">
            <span class="malou-text-14--regular">{{ 'common.here' | translate }}</span>
        </button>
        <button mat-menu-item (click)="onDuplicateSelection(DuplicationDestination.OUT)">
            <div class="flex w-full items-center justify-between">
                <span class="malou-text-14--regular">{{ 'common.to_other_venues' | translate }}</span>
            </div>
        </button>
    </div>
</app-posts-list-header>
