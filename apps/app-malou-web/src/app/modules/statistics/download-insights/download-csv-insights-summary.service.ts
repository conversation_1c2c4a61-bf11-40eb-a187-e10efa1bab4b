import { Inject, Injectable } from '@angular/core';
import { map, Observable, of } from 'rxjs';

import { CsvInsightChart, MalouComparisonPeriod } from '@malou-io/package-utils';

import { CsvAsStringArrays, CsvService } from ':shared/services/csv-services/csv-service.abstract';
import { CsvStringCreator } from ':shared/services/csv-services/helpers/create-csv-string';
import { SummaryCsvInsightsService } from ':shared/services/csv-services/insights';

interface CsvInsightsSummaryParams {
    csvChart: CsvInsightChart;
    options: {
        startDate: Date;
        endDate: Date;
        comparisonPeriod: MalouComparisonPeriod;
    };
}
@Injectable({ providedIn: 'root' })
export class DownloadCsvInsightsSummaryService {
    constructor(
        private readonly _csvStringCreator: CsvStringCreator,
        @Inject(SummaryCsvInsightsService)
        private readonly _csvSummaryInsightsService: CsvService
    ) {}

    getCsvInsightsSummaryData$({ csvChart, options }: CsvInsightsSummaryParams): Observable<string | null> {
        let csvData$: Observable<CsvAsStringArrays | null>;
        switch (csvChart) {
            case CsvInsightChart.INSIGHTS_SUMMARY:
                csvData$ = this._csvSummaryInsightsService.getCsvData$(options);
                break;
            default:
                return of(null);
        }

        return csvData$.pipe(
            map((csvData) => {
                if (!csvData) {
                    return null;
                }
                return this._csvStringCreator.create(csvData);
            })
        );
    }
}
