import { AsyncPipe, NgTemplateOutlet } from '@angular/common';
import { AfterViewInit, Component, computed, DestroyRef, effect, ElementRef, inject, Signal, signal, ViewChild } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuItem } from '@angular/material/menu';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { isEqual, isInteger } from 'lodash';
import { combineLatest, distinctUntilChanged, EMPTY, filter, fromEvent, Observable, switchMap, take } from 'rxjs';

import { HeapEventName, InsightsChart, InsightsTab, PlatformFilterPage, PlatformKey } from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { HeapService } from ':core/services/heap.service';
import { ScreenSizeService } from ':core/services/screen-size.service';
import { ToastService } from ':core/services/toast.service';
import { DownloadInsightsSummaryService } from ':modules/statistics/download-insights/download-insights-summary.service';
import { StatisticsSeoRatingsComponent } from ':modules/statistics/e-reputation/ratings/ratings.component';
import { ReviewAnalysesComponent } from ':modules/statistics/e-reputation/review-analyses/review-analyses.component';
import { ReviewsKpisComponent } from ':modules/statistics/e-reputation/reviews-kpis/reviews-kpis.component';
import { ReviewsRatingsEvolutionComponent } from ':modules/statistics/e-reputation/reviews-ratings-evolution/reviews-ratings-evolution.component';
import { ReviewsRatingsTotalComponent } from ':modules/statistics/e-reputation/reviews-ratings-total/reviews-ratings-total.component';
import { SemanticAnalysisComponent } from ':modules/statistics/e-reputation/semantic-analysis/semantic-analysis.component';
import { FiltersComponent } from ':modules/statistics/filters/filters.component';
import { EReputationInsightsTabs } from ':modules/statistics/statistics.interfaces';
import * as StatisticsSelectors from ':modules/statistics/store/statistics.selectors';
import {
    DownloadInsightsModalComponent,
    DownloadInsightsModalData,
} from ':shared/components/download-insights-modal/download-insights-modal.component';
import { ChartOptions, StatisticsDataViewMode } from ':shared/components/download-insights-modal/download-insights.interface';
import { MenuButtonV2Component } from ':shared/components/menu-button-v2/menu-button-v2.component';
import { MenuButtonSize } from ':shared/components/menu-button-v2/menu-button-v2.interface';
import { ViewBy } from ':shared/enums/view-by.enum';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { CustomDialogService } from ':shared/services/custom-dialog.service';
import { ScrollableContentService } from ':shared/services/scrollable-content.service';

@Component({
    selector: 'app-statistics-e-reputation',
    templateUrl: './e-reputation.component.html',
    styleUrls: ['./e-reputation.component.scss'],
    imports: [
        NgTemplateOutlet,
        ReviewsKpisComponent,
        ReviewsRatingsEvolutionComponent,
        ReviewsRatingsTotalComponent,
        ReviewAnalysesComponent,
        FiltersComponent,
        AsyncPipe,
        IllustrationPathResolverPipe,
        TranslateModule,
        MatButtonModule,
        MatTooltipModule,
        MatTabsModule,
        StatisticsSeoRatingsComponent,
        SemanticAnalysisComponent,
        MenuButtonV2Component,
        EnumTranslatePipe,
        MatMenuItem,
    ],
})
export class EReputationComponent implements AfterViewInit {
    @ViewChild('topOfComponent') topOfComponent: ElementRef<HTMLElement>;

    private readonly _experimentationService = inject(ExperimentationService);
    private readonly _activatedRoute = inject(ActivatedRoute);
    private readonly _destroyRef = inject(DestroyRef);
    private readonly _store = inject(Store);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);
    private readonly _heapService = inject(HeapService);
    private readonly _router = inject(Router);
    private readonly _scrollableContentService = inject(ScrollableContentService);
    private readonly _downloadInsightsSummaryService = inject(DownloadInsightsSummaryService);
    public readonly screenSizeService = inject(ScreenSizeService);

    readonly PlatformFilterPage = PlatformFilterPage;
    readonly Illustration = Illustration;
    readonly InsightsChart = InsightsChart;
    readonly EReputationInsightsTabs = EReputationInsightsTabs;
    readonly InsightsTab = InsightsTab;
    readonly MenuButtonSize = MenuButtonSize;

    readonly isSeoRatingLoading = signal(true);
    readonly isReviewsKpisLoading = signal(true);
    readonly isReviewsRatingsEvolutionLoading = signal(true);
    readonly isReviewsRatingsTotalLoading = signal(true);
    readonly isReviewAnalysesLoading = signal(true);
    readonly selectedIndex = signal(EReputationInsightsTabs.REVIEWS);
    readonly semanticAnalysisSectionDisplayStartTime = signal<number>(0);
    readonly reviewsSectionDisplayStartTime = signal<number>(0);

    readonly isLoading = computed(
        () =>
            this.isSeoRatingLoading() ||
            this.isReviewsKpisLoading() ||
            this.isReviewsRatingsEvolutionLoading() ||
            this.isReviewsRatingsTotalLoading() ||
            this.isReviewAnalysesLoading()
    );

    readonly platformKeys$: Observable<PlatformKey[]> = this._store
        .select(StatisticsSelectors.selectPlatformsFilter({ page: PlatformFilterPage.E_REPUTATION }))
        .pipe(distinctUntilChanged((prev, curr) => prev.length === curr.length && isEqual(prev, curr)));
    readonly platformKeys: Signal<PlatformKey[]> = toSignal(this.platformKeys$, { initialValue: [] });
    readonly hasAtLeastOnePlatformConnected = computed(() => this.platformKeys().length > 0);

    readonly isNewSemanticAnalysisFeatureEnabled = toSignal(
        this._experimentationService.isFeatureEnabledForRestaurant$('release-new-semantic-analysis'),
        { initialValue: false }
    );

    readonly isDownloadStatisticsResumeEnabled: Signal<boolean> = toSignal(
        this._experimentationService.isFeatureEnabled$('release-download-statistics-resume'),
        {
            initialValue: false,
        }
    );

    chartOptions: ChartOptions = {
        [InsightsChart.REVIEW_RATING_EVOLUTION]: {
            viewBy: ViewBy.WEEK,
            hiddenDatasetIndexes: [],
        },
        [InsightsChart.REVIEW_ANALYSES_TAG_EVOLUTION]: {
            viewBy: ViewBy.WEEK,
            hiddenDatasetIndexes: [],
        },
        [InsightsChart.SEMANTIC_ANALYSIS_TOPICS_EVOLUTION]: {
            viewBy: ViewBy.WEEK,
            hiddenDatasetIndexes: [],
        },
        [InsightsChart.REVIEW_RATING_TOTAL]: {
            viewMode: StatisticsDataViewMode.CHART,
            hiddenDatasetIndexes: [],
        },
    };

    constructor() {
        this._activatedRoute.queryParams.pipe(takeUntilDestroyed(this._destroyRef)).subscribe((params) => {
            const tab = params['tab'];
            const expectedValues = Object.values(EReputationInsightsTabs).map((value) => value.toString());
            if (tab && expectedValues.includes(tab.toUpperCase())) {
                if (isInteger(+tab)) {
                    this.selectedIndex.set(tab);
                } else {
                    this.selectedIndex.set(EReputationInsightsTabs[tab.toUpperCase() as keyof typeof EReputationInsightsTabs]);
                }
            }
            if (this.selectedIndex() === EReputationInsightsTabs.REVIEWS) {
                this.reviewsSectionDisplayStartTime.set(Date.now());
            } else if (this.selectedIndex() === EReputationInsightsTabs.SEMANTIC_ANALYSIS) {
                this.semanticAnalysisSectionDisplayStartTime.set(Date.now());
            }
        });

        effect(() => {
            const currentTabIndex = this.selectedIndex();
            this._router.navigate([], {
                relativeTo: this._activatedRoute,
                queryParams: { tab: currentTabIndex },
            });
        });
    }

    ngAfterViewInit(): void {
        setTimeout(() => document.getElementById('scrollable-content')?.scrollTo({ top: 0 }));
        const target = this._scrollableContentService.getElement();
        if (!target) {
            return;
        }
        fromEvent(target as HTMLElement, 'scroll')
            .pipe(
                filter(() => this.selectedIndex().toString() === EReputationInsightsTabs.SEMANTIC_ANALYSIS.toString()),
                take(1)
            )
            .subscribe(() => {
                const semanticAnalysisSectionDisplayStartTime = this.semanticAnalysisSectionDisplayStartTime();
                const durationBeforeFirstScroll = semanticAnalysisSectionDisplayStartTime
                    ? new Date().getTime() - semanticAnalysisSectionDisplayStartTime
                    : null;
                this._heapService.track(HeapEventName.TRACKING_SEMANTIC_ANALYSIS_SCROLLING, {
                    durationBeforeFirstScroll,
                    isNewSemanticAnalysis: this.isNewSemanticAnalysisFeatureEnabled(),
                });
            });
    }

    downloadInsightsSummary(): void {
        this._downloadInsightsSummaryService.downloadCsvInsightsSummary(InsightsTab.SUMMARY);
    }

    openStatisticsDownload(): void {
        combineLatest([this._store.select(StatisticsSelectors.selectDatesFilter), this.platformKeys$])
            .pipe(
                take(1),
                switchMap(([{ startDate, endDate }, platforms]) => {
                    if (!startDate || !endDate) {
                        this._toastService.openErrorToast(
                            this._translateService.instant('aggregated_statistics.download_insights_modal.please_select_dates')
                        );
                        return EMPTY;
                    }
                    const tab = this.isNewSemanticAnalysisFeatureEnabled()
                        ? InsightsTab.E_REPUTATION_WITH_NEW_SEMANTIC_ANALYSIS
                        : InsightsTab.E_REPUTATION;
                    return this._customDialogService
                        .open<DownloadInsightsModalComponent, DownloadInsightsModalData>(DownloadInsightsModalComponent, {
                            height: undefined,
                            data: {
                                tab,
                                filters: {
                                    dates: { startDate, endDate },
                                    platforms,
                                },
                                chartOptions: this.chartOptions,
                            },
                        })
                        .afterClosed();
                })
            )
            .subscribe();
    }

    onViewModeChange(chart: InsightsChart, value: StatisticsDataViewMode): void {
        this.chartOptions = {
            ...this.chartOptions,
            [chart]: {
                ...this.chartOptions[chart],
                viewMode: value,
            },
        };
    }

    onViewByChange(chart: InsightsChart, value: ViewBy): void {
        this.chartOptions = {
            ...this.chartOptions,
            [chart]: {
                ...this.chartOptions[chart],
                viewBy: value,
            },
        };
    }

    onHiddenDatasetIndexesChange(chart: InsightsChart, value: number[]): void {
        this.chartOptions = {
            ...this.chartOptions,
            [chart]: {
                ...this.chartOptions[chart],
                hiddenDatasetIndexes: value,
            },
        };
    }

    handleTabChange(event: number): void {
        switch (event) {
            case EReputationInsightsTabs.REVIEWS:
                this.reviewsSectionDisplayStartTime.set(Date.now());
                this._heapService.track(HeapEventName.TRACKING_CLICK_E_REPUTATION_INSIGHTS_REVIEWS_TAB);
                this.selectedIndex.set(EReputationInsightsTabs.REVIEWS);
                break;
            case EReputationInsightsTabs.SEMANTIC_ANALYSIS:
                const reviewsSectionDisplayStartTime = this.reviewsSectionDisplayStartTime();
                const durationBetweenTabSwitch = reviewsSectionDisplayStartTime ? Date.now() - reviewsSectionDisplayStartTime : 0;
                this._heapService.track(HeapEventName.TRACKING_CLICK_E_REPUTATION_INSIGHTS_SEMANTIC_ANALYSES_TAB, {
                    timeSpentOnReviewsBefore: durationBetweenTabSwitch,
                    isNewSemanticAnalysis: this.isNewSemanticAnalysisFeatureEnabled(),
                });
                this.selectedIndex.set(EReputationInsightsTabs.SEMANTIC_ANALYSIS);
                this.semanticAnalysisSectionDisplayStartTime.set(Date.now());
                break;
        }
    }
}
