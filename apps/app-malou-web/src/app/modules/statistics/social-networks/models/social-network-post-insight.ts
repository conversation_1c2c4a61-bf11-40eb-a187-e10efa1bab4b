import { DateTime } from 'luxon';

import { DailyPlatformInsights, PlatformPostInsightResponseDto } from '@malou-io/package-dto';
import { MalouErrorCode, MalouMetric, PlatformKey, PostType } from '@malou-io/package-utils';

import { getClosestValueFromDate } from ':shared/helpers';
import { PostInsight, PostInsightProps } from ':shared/models/post-insight';

export class PlatformSocialNetworkPostInsight {
    platformKey: PlatformKey;
    postInsights: SocialNetworkPostInsight[];
    error?: { code: MalouErrorCode; message: string };

    constructor(data: {
        platformKey: PlatformKey;
        postInsights: SocialNetworkPostInsight[];
        error?: { code: MalouErrorCode; message: string };
    }) {
        this.platformKey = data.platformKey;
        this.postInsights = data.postInsights;
        this.error = data.error;
    }

    static fromPlatformPostInsightResponseDto(
        dto: PlatformPostInsightResponseDto,
        followers: DailyPlatformInsights | undefined
    ): PlatformSocialNetworkPostInsight {
        const postInsights = dto.postInsights.map((postInsight) => new SocialNetworkPostInsight(postInsight, followers));
        return new PlatformSocialNetworkPostInsight({
            platformKey: dto.platformKey,
            postInsights,
            error: dto.error ? { code: dto.error.code, message: dto.error.message } : undefined,
        });
    }
}

enum PostInsightMetric {
    LIKES = 'likes',
    COMMENTS = 'comments',
    SHARES = 'shares',
    SAVED = 'saved',
    ENGAGEMENT_RATE = 'engagementRate',
    REACH = 'reach',
    IMPRESSIONS = 'impressions',
    PLAYS = 'plays',
}
export class SocialNetworkPostInsight extends PostInsight {
    nbFollowers: number | null;
    engagementRate: number | null;

    constructor(data: PostInsightProps, followers: DailyPlatformInsights | undefined) {
        super(data);
        this.nbFollowers = this._getNbFollowers(followers) ?? null;
        this.engagementRate = this._getEngagementRate();
    }

    isPlatformMissingProperty = (metric: PostInsightMetric): boolean => {
        const nonExistentMetricsByPlatforms: Partial<Record<PlatformKey, PostInsightMetric[]>> = {
            [PlatformKey.INSTAGRAM]: [],
            [PlatformKey.FACEBOOK]: [PostInsightMetric.SAVED],
            [PlatformKey.TIKTOK]: [PostInsightMetric.REACH, PostInsightMetric.SAVED],
        };
        return nonExistentMetricsByPlatforms[this.platformKey]?.includes(metric) ?? false;
    };

    isInstagramCarrousel = (): boolean => this.platformKey === PlatformKey.INSTAGRAM && this.postType === PostType.CAROUSEL;

    getEngagement(): number {
        return this.likes + this.comments + (this.saved ?? 0) + this.shares;
    }

    private _getNbFollowers(followers: DailyPlatformInsights | undefined): number | null {
        if (!followers || !followers.insights || !followers.insights[MalouMetric.FOLLOWERS]) {
            return null;
        }
        const followersData = followers.insights[MalouMetric.FOLLOWERS];
        const postDate = new Date(this.postCreatedAt);
        const isoPostDate = DateTime.fromJSDate(postDate).toISODate();
        const nbFollowers = followersData[isoPostDate];
        if (nbFollowers) {
            return nbFollowers;
        }

        // If no followers data for the exact post date, find the closest date with followers data
        const possibleDates = Object.keys(followersData).map((d) => new Date(d));
        const closestDate = getClosestValueFromDate(postDate, possibleDates);
        const closestDateFormatted = closestDate ? DateTime.fromJSDate(closestDate).toISODate() : null;
        return closestDateFormatted ? followersData[closestDateFormatted] : null;
    }

    private _getEngagementRate(): number | null {
        if (this.nbFollowers === null) {
            return null;
        }
        const engagement = this.getEngagement();
        return (engagement / this.nbFollowers) * 100;
    }
}
