import { computed, DestroyRef, inject, Injectable, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Store } from '@ngrx/store';
import { catchError, combineLatest, distinctUntilChanged, EMPTY, filter, forkJoin, map, Observable, of, switchMap, tap } from 'rxjs';

import { PlatformPostInsightResponseDto } from '@malou-io/package-dto';
import {
    getDateRangeFromMalouComparisonPeriod,
    isNotNil,
    MalouComparisonPeriod,
    PlatformFilterPage,
    PlatformKey,
} from '@malou-io/package-utils';

import { PostInsightsV2Service } from ':core/services/post-insights-v2.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { SocialNetworksCommunityInsightsContext } from ':modules/statistics/social-networks/context/social-networks-community-insights.context';
import { PlatformSocialNetworkPostInsight } from ':modules/statistics/social-networks/models/social-network-post-insight';
import { StatisticsState } from ':modules/statistics/store/statistics.interface';
import * as StatisticsSelector from ':modules/statistics/store/statistics.selectors';
import { StoriesService } from ':modules/stories/v2/stories.service';
import { isDateSetOrGenericPeriod } from ':shared/helpers';
import { DatesAndPeriod, Restaurant } from ':shared/models';

@Injectable({
    providedIn: 'root',
})
export class SocialNetworksPostsInsightsV2Context {
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _store = inject(Store);
    private readonly _storiesService = inject(StoriesService);
    private readonly _postInsightsV2Service = inject(PostInsightsV2Service);
    private readonly _socialNetworksCommunityInsightsContext = inject(SocialNetworksCommunityInsightsContext);
    private readonly _destroyRef = inject(DestroyRef);

    private readonly _currentPlatformPostInsights: WritableSignal<PlatformPostInsightResponseDto[]> = signal([]);
    private readonly _previousPlatformPostInsights: WritableSignal<PlatformPostInsightResponseDto[]> = signal([]);
    private readonly _restaurantId: WritableSignal<string | null> = signal(null);

    readonly statisticsFilters$: Observable<StatisticsState['filters']> = this._store
        .select(StatisticsSelector.selectFilters)
        .pipe(distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)));
    readonly selectedRestaurant$ = this._restaurantsService.restaurantSelected$.pipe(
        distinctUntilChanged((prev, curr) => prev?._id === curr?._id)
    );

    readonly isLoading = signal(true);
    readonly httpError: WritableSignal<string | null> = signal(null);
    readonly areAllPlatformsInError: WritableSignal<boolean> = signal(false);
    readonly platformsErrorTooltip: WritableSignal<string | null> = signal(null);
    readonly hasDate: WritableSignal<boolean> = signal(false);
    readonly comparisonPeriod: WritableSignal<MalouComparisonPeriod> = signal(MalouComparisonPeriod.PREVIOUS_PERIOD);
    readonly previousPeriodDates: WritableSignal<{ startDate: Date; endDate: Date } | null> = signal(null);

    readonly currentSocialNetworkPostInsights = computed<PlatformSocialNetworkPostInsight[]>(() => {
        const currentRestaurantPostInsightsDto = this._currentPlatformPostInsights();
        const restaurantId = this._restaurantId();
        if (!restaurantId || !currentRestaurantPostInsightsDto.length) {
            return [];
        }
        const followersData = this._socialNetworksCommunityInsightsContext.dailyStoredInsightsResponseDtoData()[restaurantId];

        return currentRestaurantPostInsightsDto.map((dto) => {
            const platformFollowers = followersData?.[dto.platformKey];
            return PlatformSocialNetworkPostInsight.fromPlatformPostInsightResponseDto(dto, platformFollowers);
        });
    });

    readonly previousSocialNetworkPostInsights = computed<PlatformSocialNetworkPostInsight[]>(() => {
        const previousRestaurantPostInsightsDto = this._previousPlatformPostInsights();
        const restaurantId = this._restaurantId();
        if (!restaurantId || !previousRestaurantPostInsightsDto.length) {
            return [];
        }
        const followersData = this._socialNetworksCommunityInsightsContext.dailyPreviousStoredInsightsResponseDtoData()[restaurantId];

        return previousRestaurantPostInsightsDto.map((dto) => {
            const platformFollowers = followersData?.[dto.platformKey];
            return PlatformSocialNetworkPostInsight.fromPlatformPostInsightResponseDto(dto, platformFollowers);
        });
    });

    readonly storiesCount: WritableSignal<number> = signal(0);

    readonly postsWithInsights$ = combineLatest([this.selectedRestaurant$, this.statisticsFilters$]).pipe(
        filter(
            ([restaurant, statisticsFilters]: [Restaurant, StatisticsState['filters']]) =>
                !!restaurant &&
                isDateSetOrGenericPeriod(statisticsFilters.dates) &&
                isNotNil(statisticsFilters.dates.startDate) &&
                isNotNil(statisticsFilters.dates.endDate) &&
                statisticsFilters.isFiltersLoaded
        ),
        map(([restaurant, statisticsFilters]: [Restaurant, StatisticsState['filters']]) => [
            restaurant,
            {
                startDate: statisticsFilters.dates.startDate,
                endDate: statisticsFilters.dates.endDate,
            },
            statisticsFilters.platforms[PlatformFilterPage.SOCIAL_NETWORKS],
            statisticsFilters.comparisonPeriod,
        ]),
        tap(() => this._reset()),
        switchMap(
            ([restaurant, dates, platformKeys, comparisonPeriod]: [Restaurant, DatesAndPeriod, PlatformKey[], MalouComparisonPeriod]) => {
                const startDate = dates.startDate as Date;
                const endDate = dates.endDate as Date;
                let previousPeriodDates: {
                    startDate: Date;
                    endDate: Date;
                } | null = null;
                const dateRangeFromMalouComparisonPeriod = getDateRangeFromMalouComparisonPeriod({
                    comparisonPeriod,
                    dateFilters: {
                        startDate,
                        endDate,
                    },
                    restaurantStartDate: new Date(restaurant.createdAt),
                });
                if (dateRangeFromMalouComparisonPeriod.startDate && dateRangeFromMalouComparisonPeriod.endDate) {
                    previousPeriodDates = {
                        startDate: dateRangeFromMalouComparisonPeriod.startDate,
                        endDate: dateRangeFromMalouComparisonPeriod.endDate,
                    };
                } else {
                    const previousPeriod = getDateRangeFromMalouComparisonPeriod({
                        comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                        dateFilters: {
                            startDate,
                            endDate,
                        },
                    });
                    previousPeriodDates = {
                        startDate: previousPeriod.startDate!,
                        endDate: previousPeriod.endDate!,
                    };
                }
                const { startDate: previousStartDate, endDate: previousEndDate } = previousPeriodDates;
                const { _id: restaurantId } = restaurant;
                this._restaurantId.set(restaurantId);

                return forkJoin([
                    this._postInsightsV2Service.getRestaurantPostInsights$({
                        restaurantId,
                        startDate,
                        endDate,
                        platformKeys,
                    }),
                    this._postInsightsV2Service.getRestaurantPostInsights$({
                        restaurantId,
                        startDate: previousStartDate,
                        endDate: previousEndDate,
                        platformKeys,
                    }),
                    this._storiesService.getPublishedStoriesCount$(restaurantId, startDate, endDate),
                    of(previousPeriodDates),
                    of(comparisonPeriod),
                ]);
            }
        ),
        catchError((error) => {
            this.httpError.set(error);
            this.hasDate.set(false);
            this.isLoading.set(false);
            return of(EMPTY);
        }),
        map(
            ([currentPlatformPostInsights, previousPlatformPostInsights, storiesCount, previousPeriodDates, comparisonPeriod]: [
                PlatformPostInsightResponseDto[],
                PlatformPostInsightResponseDto[],
                number,
                { startDate: Date; endDate: Date },
                MalouComparisonPeriod,
            ]) => {
                this.isLoading.set(false);
                this._currentPlatformPostInsights.set(currentPlatformPostInsights);
                this._previousPlatformPostInsights.set(previousPlatformPostInsights);
                this.storiesCount.set(storiesCount);
                this.previousPeriodDates.set(previousPeriodDates);
                this.comparisonPeriod.set(comparisonPeriod);
                this.hasDate.set(true);
            }
        ),
        takeUntilDestroyed(this._destroyRef)
    );

    private _reset(): void {
        this.httpError.set(null);
        this.isLoading.set(true);
        this.platformsErrorTooltip.set(null);
        this.areAllPlatformsInError.set(false);
    }
}
