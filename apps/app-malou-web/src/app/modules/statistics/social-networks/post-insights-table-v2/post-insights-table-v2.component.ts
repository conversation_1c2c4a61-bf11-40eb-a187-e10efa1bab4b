import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, effect, inject, input, output, signal, Signal, WritableSignal } from '@angular/core';
import { Sort } from '@angular/material/sort';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { InsightsChart, PostType } from '@malou-io/package-utils';

import { SocialNetworksPostsInsightsV2Context } from ':modules/statistics/social-networks/context/social-networks-post-insights-v2.context';
import { SocialNetworkPostInsight } from ':modules/statistics/social-networks/models/social-network-post-insight';
import { StoriesComponent } from ':modules/statistics/social-networks/posts-insights-table/stories/stories.component';
import { TopPostCardSkeletonComponent } from ':shared/components/top-post-card-skeleton/top-post-card-skeleton.component';
import { TopPostCardComponent, TopPostCardInputData } from ':shared/components/top-post-card/top-post-card.component';
import { CreateArrayPipe } from ':shared/pipes/create-array.pipe';

import { PostsTableComponent } from './posts-table/posts-table.component';

enum SocialNetworkInsightsTabs {
    POSTS,
    REELS,
    STORIES,
}

@Component({
    selector: 'app-post-insights-table-v2',
    imports: [
        NgTemplateOutlet,
        TranslateModule,
        LazyLoadImageModule,
        MatTabsModule,
        StoriesComponent,
        TopPostCardComponent,
        TopPostCardSkeletonComponent,
        CreateArrayPipe,
        PostsTableComponent,
    ],
    templateUrl: './post-insights-table-v2.component.html',
    styleUrl: './post-insights-table-v2.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PostInsightsTableV2Component {
    readonly shouldDisplayPostInsightsTable = input<boolean>(true);
    readonly shouldDisplayStoryInsightsTable = input<boolean>(true);
    readonly shouldDisplayReelInsightsTable = input<boolean>(true);
    readonly shouldHidePublicationsTablesIfNoData = input<boolean>(false); // by default there is an error message
    readonly shouldDetailPostTables = input<boolean>(false);
    readonly shouldLazyLoadMedia = input<boolean>(true);
    readonly postsTableSortOptions = input<Sort | undefined>(undefined);
    readonly reelsTableSortOptions = input<Sort | undefined>(undefined);
    readonly storiesTableSortOptions = input<Sort | undefined>(undefined);

    readonly tableSortOptionsChange = output<{ chart: InsightsChart; value: Sort }>();
    readonly isLoadingEvent = output<boolean>();

    private readonly _socialNetworksPostsInsightsV2Context = inject(SocialNetworksPostsInsightsV2Context);

    readonly storiesHasNoData: WritableSignal<boolean> = signal(false);
    readonly selectedTab: WritableSignal<SocialNetworkInsightsTabs> = signal(SocialNetworkInsightsTabs.POSTS);

    readonly storiesCount: Signal<number> = computed(() => this._socialNetworksPostsInsightsV2Context.storiesCount());
    readonly httpError = computed(() => this._socialNetworksPostsInsightsV2Context.httpError());
    readonly isLoading = computed(() => this._socialNetworksPostsInsightsV2Context.isLoading());
    readonly areAllPlatformsInError = computed(() => this._socialNetworksPostsInsightsV2Context.areAllPlatformsInError());
    readonly platformsErrorTooltip = computed(() => this._socialNetworksPostsInsightsV2Context.platformsErrorTooltip());

    readonly SocialNetworkInsightsTabs = SocialNetworkInsightsTabs;

    readonly reels = computed(() =>
        this._socialNetworksPostsInsightsV2Context
            .currentSocialNetworkPostInsights()
            .filter((platformPostInsights) => platformPostInsights.postInsights.length)
            .map((platformPostInsights) => platformPostInsights.postInsights)
            .flat()
            .filter((postInsight) => postInsight.postType === PostType.REEL)
    );

    readonly posts = computed(() =>
        this._socialNetworksPostsInsightsV2Context
            .currentSocialNetworkPostInsights()
            .filter((platformPostInsights) => platformPostInsights.postInsights.length)
            .map((platformPostInsights) => platformPostInsights.postInsights)
            .flat()
            .filter((postInsight) => postInsight.postType !== PostType.REEL)
    );

    readonly topPostCardInputsFromPosts: Signal<TopPostCardInputData[]> = computed(() =>
        this._getTop3Posts(this.posts()).map(this._mapToTopPostCardInput)
    );
    readonly topPostCardInputsFromReels: Signal<TopPostCardInputData[]> = computed(() =>
        this._getTop3Posts(this.reels()).map(this._mapToTopPostCardInput)
    );

    constructor() {
        effect(() => this.isLoadingEvent.emit(this.isLoading()));
    }

    handleTabChange(tabIndex: number): void {
        this.selectedTab.set(tabIndex);
    }

    private _getTop3Posts(posts: SocialNetworkPostInsight[]): SocialNetworkPostInsight[] {
        return posts
            .sort((a, b) => {
                if (a.engagementRate === null && b.engagementRate === null) {
                    return 0;
                }
                if (a.engagementRate === null) {
                    return 1;
                }
                if (b.engagementRate === null) {
                    return -1;
                }
                return b.engagementRate - a.engagementRate;
            })
            .slice(0, 3);
    }

    private _mapToTopPostCardInput(post: SocialNetworkPostInsight): TopPostCardInputData {
        return {
            postType: post.postType,
            platformKey: post.platformKey,
            url: post.permalink,
            thumbnailUrl: post.thumbnail,
            createdAt: post.postCreatedAt,
            likes: post.likes,
            comments: post.comments,
            shares: post.shares,
            saves: post.saved || 0,
            impressions: post.impressions || post.plays || 0,
            engagementRate: post.engagementRate ?? 0,
        };
    }
}
