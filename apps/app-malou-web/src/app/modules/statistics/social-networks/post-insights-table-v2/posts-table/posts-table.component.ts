import { DatePipe, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, output, signal, ViewChild, WritableSignal } from '@angular/core';
import { MAT_FORM_FIELD_DEFAULT_OPTIONS } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';
import { Observable } from 'rxjs';

import { InsightsChart, PlatformFilterPage, PlatformKey } from '@malou-io/package-utils';

import { SocialNetworkPostInsight } from ':modules/statistics/social-networks/models/social-network-post-insight';
import { StatisticsHttpErrorPipe } from ':modules/statistics/statistics-http-error.pipe';
import * as StatisticsSelectors from ':modules/statistics/store/statistics.selectors';
import { PaginatorComponent } from ':shared/components/paginator/paginator.component';
import { PlatformLogoComponent } from ':shared/components/platform-logo/platform-logo.component';
import { SkeletonComponent } from ':shared/components/skeleton/skeleton.component';
import { SocialPostMediaComponent } from ':shared/components/social-post-media/social-post-media.component';
import { FilterOption, SortByFiltersComponent } from ':shared/components/sort-by-filters/sort-by-filters.component';
import { TypeSafeMatCellDefDirective } from ':shared/directives/type-safe-mat-cell-def.directive';
import { TypeSafeMatRowDefDirective } from ':shared/directives/type-safe-mat-row-def.directive';
import { ChartSortBy } from ':shared/enums/sort.enum';
import { DatesAndPeriod } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { ShortNumberPipe } from ':shared/pipes/short-number.pipe';

enum TableColumn {
    PICTURE = 'picture',
    PLATFORM = 'platform',
    CREATED_AT = 'createdAt',
    LIKES = 'likes',
    COMMENTS = 'comments',
    SHARES = 'shares',
    SAVED = 'saved',
    ENGAGEMENT_RATE = 'engagementRate',
    REACH = 'reach',
    IMPRESSIONS = 'impressions',
    PLAYS = 'plays',
}

@Component({
    selector: 'app-posts-table',
    providers: [
        {
            provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
            useValue: { appearance: 'outline' },
        },
        EnumTranslatePipe,
    ],
    imports: [
        SortByFiltersComponent,
        NgTemplateOutlet,
        SkeletonComponent,
        PaginatorComponent,
        SortByFiltersComponent,
        TypeSafeMatCellDefDirective,
        TypeSafeMatRowDefDirective,
        LazyLoadImageModule,
        MatIconModule,
        MatSortModule,
        MatTableModule,
        MatTooltipModule,
        TranslateModule,
        ApplySelfPurePipe,
        DatePipe,
        IllustrationPathResolverPipe,
        PlatformLogoComponent,
        ShortNumberPipe,
        StatisticsHttpErrorPipe,
        SocialPostMediaComponent,
    ],
    templateUrl: './posts-table.component.html',
    styleUrl: './posts-table.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PostsTableComponent {
    readonly posts = input<SocialNetworkPostInsight[]>([]);
    readonly areAllPlatformsInError = input<boolean>(false);
    readonly platformsErrorTooltip = input<string | undefined>(undefined);
    readonly httpError = input<any>(undefined);
    readonly isLoading = input<boolean>(true);
    readonly isReelTable = input<boolean>(false);
    readonly shouldDisplayElementsPaginated = input<boolean>(true);
    readonly shouldLazyLoadMedia = input<boolean>(true);
    readonly tableSortOptions = input<Sort | undefined>(undefined);

    readonly tableSortOptionsChange = output<{ chart: InsightsChart; value: Sort }>();

    private readonly _translate = inject(TranslateService);
    private readonly _store = inject(Store);

    readonly SvgIcon = SvgIcon;
    readonly TableColumn = TableColumn;

    readonly dataSource = computed(() => new MatTableDataSource<SocialNetworkPostInsight>(this.posts()));

    readonly platformKeys$: Observable<PlatformKey[]> = this._store.select(
        StatisticsSelectors.selectPlatformsFilter({ page: PlatformFilterPage.SOCIAL_NETWORKS })
    );
    readonly dates$: Observable<DatesAndPeriod> = this._store.select(StatisticsSelectors.selectDatesFilter);

    readonly SORT_OPTIONS: FilterOption[] = [
        { key: 'platform', label: this._translate.instant('statistics.social_networks.posts.platform') },
        { key: 'createdAt', label: this._translate.instant('statistics.social_networks.posts.created_at') },
        { key: 'likes', label: this._translate.instant('statistics.social_networks.posts.likes') },
        { key: 'comments', label: this._translate.instant('statistics.social_networks.posts.comments') },
        { key: 'engagementRate', label: this._translate.instant('statistics.social_networks.posts.engagement') },
        { key: 'impressions', label: this._translate.instant('statistics.social_networks.posts.impressions') },
        { key: 'plays', label: this._translate.instant('statistics.social_networks.posts.plays') },
    ];
    readonly defaultSort: WritableSignal<Sort> = signal({
        active: TableColumn.CREATED_AT,
        direction: ChartSortBy.DESC,
    });

    readonly displayedColumns = computed<TableColumn[]>(() => {
        const columns = [
            TableColumn.PICTURE,
            TableColumn.PLATFORM,
            TableColumn.CREATED_AT,
            TableColumn.LIKES,
            TableColumn.COMMENTS,
            TableColumn.SHARES,
            TableColumn.SAVED,
            TableColumn.ENGAGEMENT_RATE,
        ];
        if (this.isReelTable()) {
            columns.push(TableColumn.PLAYS, TableColumn.REACH);
        } else {
            columns.push(TableColumn.IMPRESSIONS);
        }
        return columns;
    });

    @ViewChild(PaginatorComponent) set paginator(paginatorComponent: PaginatorComponent) {
        if (paginatorComponent) {
            this.dataSource().paginator = paginatorComponent.matPaginator;
        }
    }

    @ViewChild(MatSort, { static: false }) set matSort(sort: MatSort) {
        if (this.dataSource) {
            this.dataSource().sortingDataAccessor = (item, property): string => {
                const { active, direction } = sort;
                this._emitSortOptions({ active, direction }, this.isReelTable());
                switch (property) {
                    case 'platform':
                        return item.platformKey;
                    default:
                        return item[property];
                }
            };
            this.dataSource().sort = sort;
        }
    }

    onSortByChange(sortBy: string): void {
        this.dataSource().sort?.sort({ id: sortBy, start: this.dataSource().sort?.direction || ChartSortBy.ASC, disableClear: true });
    }

    onSortOrderChange(): void {
        const direction = this.dataSource().sort?.direction === ChartSortBy.ASC ? ChartSortBy.DESC : ChartSortBy.ASC;
        this.dataSource().sort?.sort({ id: this.dataSource().sort?.active || ChartSortBy.ASC, start: direction, disableClear: true });
    }

    private _emitSortOptions(sort: Sort, isReelTable: boolean): void {
        const chart = isReelTable ? InsightsChart.REEL_INSIGHTS : InsightsChart.POST_INSIGHTS;
        this.tableSortOptionsChange.emit({ chart, value: sort });
    }
}
