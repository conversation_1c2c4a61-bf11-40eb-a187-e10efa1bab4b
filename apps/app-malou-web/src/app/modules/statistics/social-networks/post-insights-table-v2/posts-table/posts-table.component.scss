@use '_malou_mixins.scss' as *;
@use '_malou_variables.scss' as *;

.malou-mat-table.mat-mdc-table mat-cell:first-of-type,
.malou-mat-table.mat-mdc-table mat-header-cell:first-of-type,
.malou-mat-table.mat-mdc-table mat-footer-cell:first-of-type {
    padding-left: 0.5rem;
}

.malou-mat-table.mat-mdc-table mat-cell:last-of-type,
.malou-mat-table.mat-mdc-table mat-header-cell:last-of-type,
.malou-mat-table.mat-mdc-table mat-footer-cell:last-of-type {
    padding-right: 0.5rem;
}

.mat-column-createdAt {
    min-width: 10%;
}

.mat-column-picture {
    max-width: 60px;
}

.mat-column-platform {
    max-width: 100px;
}

.mat-column-engagementRate,
.mat-column-saved {
    min-width: 12%;
}

.mat-mdc-cell,
.mat-mdc-header-cell {
    width: 100%;
    display: flex;
    justify-content: center;
    text-align: center;
}

:host .malou-mat-table.mat-mdc-table,
.malou-mat-table.mat-mdc-table {
    .mat-mdc-row,
    .mat-mdc-row {
        cursor: default;
        break-inside: avoid;
    }

    @media (hover: hover) and (pointer: fine) {
        .mat-mdc-row:hover {
            background-color: $malou-color-background-white !important;
        }
    }
}

@include malou-respond-to('small') {
    .mat-mdc-header-row {
        display: none;
    }
    .mat-mdc-row {
        padding: 0.5rem 0.5rem !important;
        grid-row-gap: 0 !important;
    }
    .mat-mdc-cell:nth-child(1) {
        grid-area: 1 / 1 / 3 / 2 !important;
        padding-left: 0;
    }
    .mat-mdc-cell:nth-child(2) {
        grid-area: 1 / 2 / 2 / 3 !important;
    }
    .mat-mdc-cell:nth-child(3) {
        margin-top: 0.2rem;
        grid-area: 2 / 2 / 3 / 3 !important;
    }
    .mat-mdc-cell:nth-child(4) {
        margin-top: 0.4rem;
        grid-area: 3 / 1 / 4 / 3 !important;
    }
    .mat-mdc-cell:nth-child(5) {
        margin-top: 0.4rem;
        grid-area: 4 / 1 / 5 / 3 !important;
    }
    .mat-mdc-cell:nth-child(6) {
        margin-top: 0.4rem;
        grid-area: 5 / 1 / 6 / 3 !important;
    }
    .mat-mdc-cell:nth-child(7) {
        margin-top: 0.4rem;
        grid-area: 6 / 1 / 7 / 3 !important;
    }
}

mat-row,
mat-header-row {
    break-inside: avoid;
}
