import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';

import { PlatformPostInsightResponseDto } from '@malou-io/package-dto';
import { ApiResultV2, PlatformKey } from '@malou-io/package-utils';

import { environment } from ':environments/environment';

@Injectable({
    providedIn: 'root',
})
export class PostInsightsV2Service {
    readonly API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/post-insights/v2`;

    constructor(private readonly _http: HttpClient) {}

    getRestaurantPostInsights$({
        restaurantId,
        startDate,
        endDate,
        platformKeys,
    }: {
        restaurantId: string;
        startDate: Date;
        endDate: Date;
        platformKeys: PlatformKey[];
    }): Observable<PlatformPostInsightResponseDto[]> {
        return this._http
            .post<ApiResultV2<PlatformPostInsightResponseDto[]>>(`${this.API_BASE_URL}/restaurants/${restaurantId}`, {
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString(),
                platformKeys,
            })
            .pipe(map((res) => res.data));
    }
}
